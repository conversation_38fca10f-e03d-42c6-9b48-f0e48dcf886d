#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试因子计算和保存过程
"""

import os
import sys
import pandas as pd
import pickle
from datetime import datetime

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '1-数据准备', '2-个股因子生成'))

from indicators.factor_calculator import FactorCalculator
from indicators.precompute_manager import PrecomputeManager

def test_factor_calculation():
    """测试因子计算过程"""
    print("=== 测试因子计算过程 ===")
    
    # 初始化因子计算器
    calculator = FactorCalculator()
    
    # 显示所有可用的指标
    print(f"可用指标: {list(calculator.indicators.keys())}")
    print(f"指标数量: {len(calculator.indicators)}")
    
    # 加载测试数据
    data_dir = r"d:\开发项目\Stock Models\RLStrategy_V2.1（SAC算法）\0-数据汇总\1-原始股票数据"
    test_file = os.path.join(data_dir, "sh.600000_hfq.csv")
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        return
    
    # 读取数据
    data = pd.read_csv(test_file, index_col=0, parse_dates=True)
    print(f"\n原始数据形状: {data.shape}")
    print(f"原始数据列: {list(data.columns)}")
    
    # 标准化列名（小写转大写）
    column_mapping = {
        'open': 'Open',
        'high': 'High', 
        'low': 'Low',
        'close': 'Close',
        'volume': 'Volume'
    }
    
    for old_col, new_col in column_mapping.items():
        if old_col in data.columns:
            data[new_col] = data[old_col]
    
    print(f"标准化后的列: {list(data.columns)}")
    
    # 计算所有因子
    print("\n开始计算因子...")
    factors_result = calculator.calculate_batch(
        {'sh.600000': data},
        indicators=None  # 计算所有指标
    )['sh.600000']
    
    print(f"\n计算结果:")
    for indicator_name, result in factors_result.items():
        if result is None:
            print(f"  {indicator_name}: None (计算失败)")
        elif isinstance(result, dict):
            print(f"  {indicator_name}: dict with {len(result)} keys: {list(result.keys())}")
        elif isinstance(result, pd.Series):
            print(f"  {indicator_name}: Series with {len(result)} values")
        else:
            print(f"  {indicator_name}: {type(result)}")
    
    # 模拟PrecomputeManager的因子构建过程
    print("\n=== 模拟因子构建过程 ===")
    
    # 构建完整因子DataFrame（包含基础数据，用于显示和统计）
    full_factor_data = {'Close': data['Close'], 'Volume': data['Volume']}
    
    # 构建纯因子DataFrame（只包含计算出的因子，用于保存）
    pure_factor_data = {}
    
    for indicator_name, indicator_result in factors_result.items():
        if indicator_result is None:
            print(f"跳过 {indicator_name}: 计算结果为None")
            continue
        
        if isinstance(indicator_result, dict):
            # 多列指标
            print(f"处理多列指标 {indicator_name}: {list(indicator_result.keys())}")
            for sub_name, sub_series in indicator_result.items():
                if isinstance(sub_series, pd.Series):
                    factor_name = f"{indicator_name}_{sub_name}"
                    full_factor_data[factor_name] = sub_series
                    pure_factor_data[factor_name] = sub_series
                    print(f"  添加因子: {factor_name}")
        elif isinstance(indicator_result, pd.Series):
            # 单列指标
            print(f"处理单列指标 {indicator_name}")
            full_factor_data[indicator_name] = indicator_result
            pure_factor_data[indicator_name] = indicator_result
            print(f"  添加因子: {indicator_name}")
    
    print(f"\n完整因子数据列数: {len(full_factor_data)}")
    print(f"完整因子数据列名: {list(full_factor_data.keys())}")
    
    print(f"\n纯因子数据列数: {len(pure_factor_data)}")
    print(f"纯因子数据列名: {list(pure_factor_data.keys())}")
    
    # 创建DataFrame
    pure_factors_df = pd.DataFrame(pure_factor_data, index=data.index)
    print(f"\n纯因子DataFrame形状: {pure_factors_df.shape}")
    print(f"纯因子DataFrame列名: {list(pure_factors_df.columns)}")
    
    # 保存测试文件
    test_output = "test_factors.pkl"
    with open(test_output, 'wb') as f:
        pickle.dump(pure_factors_df, f)
    
    print(f"\n测试文件已保存: {test_output}")
    
    # 重新加载验证
    with open(test_output, 'rb') as f:
        loaded_df = pickle.load(f)
    
    print(f"重新加载验证:")
    print(f"  形状: {loaded_df.shape}")
    print(f"  列名: {list(loaded_df.columns)}")
    
    # 清理测试文件
    os.remove(test_output)
    
def test_precompute_manager():
    """测试PrecomputeManager"""
    print("\n\n=== 测试PrecomputeManager ===")
    
    # 初始化管理器
    cache_dir = r"d:\开发项目\Stock Models\RLStrategy_V2.1（SAC算法）\0-数据汇总\2-因子数据\test_factors"
    data_dir = r"d:\开发项目\Stock Models\RLStrategy_V2.1（SAC算法）\0-数据汇总\1-原始股票数据"
    
    manager = PrecomputeManager(
        cache_dir=cache_dir,
        data_dir=data_dir,
        max_workers=1
    )
    
    # 测试单只股票
    test_stock = "sh.600000"
    print(f"\n测试股票: {test_stock}")
    
    # 强制重新计算
    success = manager.precompute_stock_factors(
        test_stock,
        indicators=None,  # 计算所有指标
        force_update=True
    )
    
    print(f"计算结果: {success}")
    
    if success:
        # 加载并检查结果
        factors_df = manager.load_precomputed_factors(test_stock)
        if factors_df is not None:
            print(f"\n加载的因子数据:")
            print(f"  形状: {factors_df.shape}")
            print(f"  列名: {list(factors_df.columns)}")
            print(f"  前5行:")
            print(factors_df.head())
        else:
            print("加载因子数据失败")
    
    # 清理测试缓存
    import shutil
    if os.path.exists(cache_dir):
        shutil.rmtree(cache_dir)
        print(f"\n已清理测试缓存: {cache_dir}")

if __name__ == "__main__":
    test_factor_calculation()
    test_precompute_manager()