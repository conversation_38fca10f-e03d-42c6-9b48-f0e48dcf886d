#!/usr/bin/env python3
"""
强化学习股票交易系统主入口

这是一个基于深度强化学习的多股票交易系统，使用PPO算法进行股票选择和交易决策。
"""

import argparse
import os
import sys
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from train import StockTrainer
from backtest import BacktestEngine


def setup_directories():
    """创建必要的目录结构"""
    directories = [
        'models',
        'logs',
        'data',
        'results',
        'reports'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"创建目录: {directory}")


def train_model(args):
    """训练模型"""
    print("开始训练模式...")
    
    # 股票列表
    stock_list = args.stocks.split(',') if args.stocks else [
        'sh.600004', 'sh.600036', 'sh.600519', 'sz.000001', 'sz.000002'
    ]
    
    # 训练配置
    training_config = {
        'initial_balance': args.initial_balance,
        'transaction_fee': args.transaction_fee,
        'lookback_window': args.lookback_window,
        'max_positions': args.max_positions
    }
    
    # 创建训练器
    trainer = StockTrainer(
        stock_list=stock_list,
        model_save_path="models",
        log_dir="logs",
        **training_config
    )
    
    # 开始训练
    trainer.train(
        num_episodes=args.episodes,
        max_steps_per_episode=args.max_steps,
        save_frequency=args.save_freq,
        eval_frequency=args.eval_freq
    )
    
    print("训练完成！")


def backtest_model(args):
    """回测模型"""
    print("开始回测模式...")
    
    # 股票列表
    stock_list = args.stocks.split(',') if args.stocks else [
        'sh.600004', 'sh.600036', 'sh.600519', 'sz.000001', 'sz.000002'
    ]
    
    # 回测配置
    backtest_config = {
        'initial_balance': args.initial_balance,
        'transaction_fee': args.transaction_fee,
        'lookback_window': args.lookback_window,
        'max_positions': args.max_positions
    }
    
    # 模型路径
    model_path = args.model if args.model else "models/final_model.pth"
    
    if not os.path.exists(model_path):
        print(f"错误: 模型文件不存在 - {model_path}")
        print("请先运行训练模式或指定正确的模型路径")
        return
    
    # 创建回测引擎
    backtest = BacktestEngine(
        stock_list=stock_list,
        model_path=model_path,
        **backtest_config
    )
    
    # 运行回测
    results = backtest.run_backtest()
    
    # 生成报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backtest.plot_results(f"results/backtest_results_{timestamp}.png")
    backtest.generate_report(f"reports/backtest_report_{timestamp}.txt")
    
    print("回测完成！")
    return results


def demo_mode():
    """演示模式"""
    print("运行演示模式...")
    
    # 使用少量股票进行快速演示
    stock_list = ['AAPL', 'GOOGL', 'MSFT']
    
    # 快速训练配置
    training_config = {
        'initial_balance': 10000.0,
        'transaction_fee': 0.001,
        'lookback_window': 10,
        'max_positions': 2
    }
    
    # 快速训练
    trainer = StockTrainer(
        stock_list=stock_list,
        model_save_path="models",
        log_dir="logs",
        **training_config
    )
    
    trainer.train(
        num_episodes=50,
        max_steps_per_episode=100,
        save_frequency=25,
        eval_frequency=10
    )
    
    # 回测
    backtest = BacktestEngine(
        stock_list=stock_list,
        model_path="models/final_model.pth",
        **training_config
    )
    
    results = backtest.run_backtest()
    backtest.plot_results("results/demo_results.png")
    backtest.generate_report("reports/demo_report.txt")
    
    print("演示完成！")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='强化学习股票交易系统')
    parser.add_argument('mode', choices=['train', 'backtest', 'demo'], 
                       help='运行模式: train(训练), backtest(回测), demo(演示)')
    
    # 通用参数
    parser.add_argument('--stocks', type=str, 
                       help='股票代码列表，用逗号分隔 (例如: AAPL,GOOGL,MSFT)')
    parser.add_argument('--initial-balance', type=float, default=100000.0,
                       help='初始资金 (默认: 100000)')
    parser.add_argument('--transaction-fee', type=float, default=0.001,
                       help='交易手续费 (默认: 0.001)')
    parser.add_argument('--lookback-window', type=int, default=30,
                       help='回溯窗口大小 (默认: 30)')
    parser.add_argument('--max-positions', type=int, default=5,
                       help='最大持仓股票数 (默认: 5)')
    
    # 训练参数
    parser.add_argument('--episodes', type=int, default=500,
                       help='训练回合数 (默认: 500)')
    parser.add_argument('--max-steps', type=int, default=500,
                       help='每回合最大步数 (默认: 500)')
    parser.add_argument('--save-freq', type=int, default=50,
                       help='模型保存频率 (默认: 50)')
    parser.add_argument('--eval-freq', type=int, default=10,
                       help='评估频率 (默认: 10)')
    
    # 回测参数
    parser.add_argument('--model', type=str,
                       help='模型文件路径 (默认: models/final_model.pth)')
    
    args = parser.parse_args()
    
    # 设置目录
    setup_directories()
    
    # 根据模式运行
    if args.mode == 'train':
        train_model(args)
    elif args.mode == 'backtest':
        backtest_model(args)
    elif args.mode == 'demo':
        demo_mode()
    else:
        print("未知模式，请使用 train, backtest 或 demo")


if __name__ == "__main__":
    main()