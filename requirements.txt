# PyTorch with CUDA support (GPU版本)
# 如果需要CPU版本，请使用: torch>=2.0.0 torchvision>=0.15.0 torchaudio>=2.0.0
torch>=2.5.0+cu121
torchvision>=0.20.0+cu121
torchaudio>=2.5.0+cu121

# 核心数据处理
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0

# 机器学习和强化学习
scikit-learn>=1.0.0
gymnasium>=1.0.0
stable-baselines3>=1.6.0

# 技术分析
ta>=0.10.2
yfinance>=0.1.70
baostock>=0.8.9

# 可视化
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# Jupyter环境
jupyter>=1.0.0
ipywidgets>=7.6.0

# 其他依赖
tqdm>=4.60.0
tensorboard>=2.8.0