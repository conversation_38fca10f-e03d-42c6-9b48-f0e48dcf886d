#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据汇总管理脚本

功能说明：
1. 自动收集项目各模块产生的数据文件
2. 按类型分类存储到数据汇总目录
3. 创建数据索引和清单
4. 提供数据清理和备份功能

数据分类：
- 1-原始股票数据：从数据源下载的原始行情数据
- 2-因子数据：计算出的个股因子和排序因子
- 3-训练模型：训练好的机器学习模型文件
- 4-回测结果：各种回测的结果文件和报告
- 5-日志文件：运行过程中产生的日志文件
"""

import os
import sys
import shutil
import pandas as pd
from datetime import datetime
import json
import glob
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataManager:
    """
    数据汇总管理器
    """
    
    def __init__(self, project_root=None):
        """
        初始化数据管理器
        
        Args:
            project_root: 项目根目录
        """
        if project_root is None:
            self.project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        else:
            self.project_root = project_root
        
        # 数据汇总目录
        self.data_summary_dir = os.path.join(self.project_root, '0-数据汇总')
        
        # 各类数据目录
        self.raw_data_dir = os.path.join(self.data_summary_dir, '1-原始股票数据')
        self.factor_data_dir = os.path.join(self.data_summary_dir, '2-个股因子数据')
        self.model_dir = os.path.join(self.data_summary_dir, '3-训练模型')
        self.backtest_dir = os.path.join(self.data_summary_dir, '4-回测结果')
        self.log_dir = os.path.join(self.data_summary_dir, '5-日志文件')
        
        # 源数据路径映射
        self.source_paths = {
            'raw_data': [
                os.path.join(self.project_root, '1-数据准备', '1-最新数据下载', 'data'),
            ],
            'factor_data': [
                os.path.join(self.project_root, '1-数据准备', '2-个股因子生成', 'factor_cache'),
                os.path.join(self.project_root, '1-数据准备', '3-排序因子生成', 'factor_cache'),
            ],
            'models': [
                os.path.join(self.project_root, '2-训练与回测', '2-模型训练', 'models'),
                os.path.join(self.project_root, '2-训练与回测', '4-逐段回测', 'rolling_backtest_results'),
            ],
            'backtest_results': [
                os.path.join(self.project_root, '2-训练与回测', '3-回测', 'results'),
                os.path.join(self.project_root, '2-训练与回测', '3-回测', 'reports'),
                os.path.join(self.project_root, '2-训练与回测', '4-逐段回测', 'rolling_backtest_results'),
            ],
            'logs': [
                os.path.join(self.project_root, '2-训练与回测', '2-模型训练', 'logs'),
                os.path.join(self.project_root, '2-训练与回测', '3-回测', 'logs'),
                self.project_root,  # 项目根目录的日志文件
            ]
        }
        
        logger.info(f"数据管理器初始化完成")
        logger.info(f"项目根目录: {self.project_root}")
        logger.info(f"数据汇总目录: {self.data_summary_dir}")
    
    def ensure_directories(self):
        """
        确保所有目录存在
        """
        directories = [
            self.data_summary_dir,
            self.raw_data_dir,
            self.factor_data_dir,
            self.model_dir,
            self.backtest_dir,
            self.log_dir
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            logger.info(f"确保目录存在: {directory}")
    
    def copy_files(self, source_dir, target_dir, file_patterns=None, preserve_structure=True):
        """
        复制文件到目标目录
        
        Args:
            source_dir: 源目录
            target_dir: 目标目录
            file_patterns: 文件模式列表，如['*.pkl', '*.csv']
            preserve_structure: 是否保持目录结构
        
        Returns:
            list: 复制的文件列表
        """
        if not os.path.exists(source_dir):
            logger.warning(f"源目录不存在: {source_dir}")
            return []
        
        if file_patterns is None:
            file_patterns = ['*']
        
        copied_files = []
        
        for pattern in file_patterns:
            # 使用glob查找文件
            if preserve_structure:
                search_pattern = os.path.join(source_dir, '**', pattern)
                files = glob.glob(search_pattern, recursive=True)
            else:
                search_pattern = os.path.join(source_dir, pattern)
                files = glob.glob(search_pattern)
            
            for file_path in files:
                if os.path.isfile(file_path):
                    # 计算相对路径
                    rel_path = os.path.relpath(file_path, source_dir)
                    target_path = os.path.join(target_dir, rel_path)
                    
                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                    
                    try:
                        # 复制文件
                        shutil.copy2(file_path, target_path)
                        copied_files.append({
                            'source': file_path,
                            'target': target_path,
                            'size': os.path.getsize(file_path),
                            'modified': datetime.fromtimestamp(os.path.getmtime(file_path))
                        })
                        logger.info(f"复制文件: {file_path} -> {target_path}")
                    except Exception as e:
                        logger.error(f"复制文件失败: {file_path} -> {target_path}, 错误: {e}")
        
        return copied_files
    
    def collect_raw_data(self):
        """
        收集原始股票数据
        """
        logger.info("开始收集原始股票数据...")
        
        all_copied = []
        for source_dir in self.source_paths['raw_data']:
            copied = self.copy_files(
                source_dir, 
                self.raw_data_dir,
                file_patterns=['*.csv', '*.xlsx', '*.json', '*.parquet'],
                preserve_structure=True
            )
            all_copied.extend(copied)
        
        logger.info(f"原始数据收集完成，共复制 {len(all_copied)} 个文件")
        return all_copied
    
    def collect_factor_data(self):
        """
        收集因子数据
        """
        logger.info("开始收集因子数据...")
        
        all_copied = []
        for source_dir in self.source_paths['factor_data']:
            copied = self.copy_files(
                source_dir,
                self.factor_data_dir,
                file_patterns=['*.pkl', '*.csv', '*.h5'],
                preserve_structure=True
            )
            all_copied.extend(copied)
        
        logger.info(f"因子数据收集完成，共复制 {len(all_copied)} 个文件")
        return all_copied
    
    def collect_models(self):
        """
        收集训练模型
        """
        logger.info("开始收集训练模型...")
        
        all_copied = []
        for source_dir in self.source_paths['models']:
            copied = self.copy_files(
                source_dir,
                self.model_dir,
                file_patterns=['*.pkl', '*.pth', '*.h5', '*.joblib', '*.model'],
                preserve_structure=True
            )
            all_copied.extend(copied)
        
        logger.info(f"模型文件收集完成，共复制 {len(all_copied)} 个文件")
        return all_copied
    
    def collect_backtest_results(self):
        """
        收集回测结果
        """
        logger.info("开始收集回测结果...")
        
        all_copied = []
        for source_dir in self.source_paths['backtest_results']:
            copied = self.copy_files(
                source_dir,
                self.backtest_dir,
                file_patterns=['*.csv', '*.xlsx', '*.json', '*.html', '*.png', '*.jpg'],
                preserve_structure=True
            )
            all_copied.extend(copied)
        
        logger.info(f"回测结果收集完成，共复制 {len(all_copied)} 个文件")
        return all_copied
    
    def collect_logs(self):
        """
        收集日志文件
        """
        logger.info("开始收集日志文件...")
        
        all_copied = []
        for source_dir in self.source_paths['logs']:
            copied = self.copy_files(
                source_dir,
                self.log_dir,
                file_patterns=['*.log', '*.txt'],
                preserve_structure=True
            )
            all_copied.extend(copied)
        
        logger.info(f"日志文件收集完成，共复制 {len(all_copied)} 个文件")
        return all_copied
    
    def create_data_index(self, collected_data):
        """
        创建数据索引
        
        Args:
            collected_data: 收集的数据信息字典
        """
        logger.info("创建数据索引...")
        
        index_data = {
            'created_time': datetime.now().isoformat(),
            'project_root': self.project_root,
            'summary': {
                'raw_data_files': len(collected_data.get('raw_data', [])),
                'factor_data_files': len(collected_data.get('factor_data', [])),
                'model_files': len(collected_data.get('models', [])),
                'backtest_files': len(collected_data.get('backtest_results', [])),
                'log_files': len(collected_data.get('logs', []))
            },
            'details': collected_data
        }
        
        # 保存为JSON文件
        index_file = os.path.join(self.data_summary_dir, 'data_index.json')
        with open(index_file, 'w', encoding='utf-8') as f:
            json.dump(index_data, f, ensure_ascii=False, indent=2, default=str)
        
        # 创建CSV格式的文件清单
        all_files = []
        for category, files in collected_data.items():
            for file_info in files:
                all_files.append({
                    'category': category,
                    'source_path': file_info['source'],
                    'target_path': file_info['target'],
                    'file_size_bytes': file_info['size'],
                    'modified_time': file_info['modified']
                })
        
        if all_files:
            df = pd.DataFrame(all_files)
            csv_file = os.path.join(self.data_summary_dir, 'file_list.csv')
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            logger.info(f"文件清单已保存: {csv_file}")
        
        logger.info(f"数据索引已创建: {index_file}")
    
    def collect_all_data(self):
        """
        收集所有数据
        
        Returns:
            dict: 收集的数据信息
        """
        logger.info("\n" + "="*60)
        logger.info("开始数据汇总收集")
        logger.info("="*60)
        
        # 确保目录存在
        self.ensure_directories()
        
        # 收集各类数据
        collected_data = {
            'raw_data': self.collect_raw_data(),
            'factor_data': self.collect_factor_data(),
            'models': self.collect_models(),
            'backtest_results': self.collect_backtest_results(),
            'logs': self.collect_logs()
        }
        
        # 创建数据索引
        self.create_data_index(collected_data)
        
        # 统计信息
        total_files = sum(len(files) for files in collected_data.values())
        total_size = sum(
            sum(file_info['size'] for file_info in files)
            for files in collected_data.values()
        )
        
        logger.info("\n" + "="*60)
        logger.info("数据汇总收集完成")
        logger.info(f"总文件数: {total_files}")
        logger.info(f"总大小: {total_size / (1024*1024):.2f} MB")
        logger.info("="*60)
        
        return collected_data
    
    def clean_data_summary(self):
        """
        清理数据汇总目录
        """
        logger.info("清理数据汇总目录...")
        
        if os.path.exists(self.data_summary_dir):
            # 删除除了脚本文件外的所有内容
            for item in os.listdir(self.data_summary_dir):
                item_path = os.path.join(self.data_summary_dir, item)
                if item.endswith('.py'):
                    continue  # 保留Python脚本
                
                if os.path.isdir(item_path):
                    shutil.rmtree(item_path)
                    logger.info(f"删除目录: {item_path}")
                else:
                    os.remove(item_path)
                    logger.info(f"删除文件: {item_path}")
        
        logger.info("数据汇总目录清理完成")
    
    def get_data_summary(self):
        """
        获取数据汇总信息
        
        Returns:
            dict: 数据汇总信息
        """
        index_file = os.path.join(self.data_summary_dir, 'data_index.json')
        
        if os.path.exists(index_file):
            with open(index_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            return None


def main():
    """
    主函数
    """
    import argparse
    
    parser = argparse.ArgumentParser(description='数据汇总管理脚本')
    parser.add_argument(
        '--action',
        choices=['collect', 'clean', 'summary'],
        default='collect',
        help='执行的操作'
    )
    parser.add_argument(
        '--project-root',
        help='项目根目录路径'
    )
    
    args = parser.parse_args()
    
    # 创建数据管理器
    manager = DataManager(args.project_root)
    
    if args.action == 'collect':
        # 收集所有数据
        collected_data = manager.collect_all_data()
        print(f"\n数据收集完成，详情请查看: {manager.data_summary_dir}")
        
    elif args.action == 'clean':
        # 清理数据汇总目录
        manager.clean_data_summary()
        print("数据汇总目录清理完成")
        
    elif args.action == 'summary':
        # 显示数据汇总信息
        summary = manager.get_data_summary()
        if summary:
            print("\n=== 数据汇总信息 ===")
            print(f"创建时间: {summary['created_time']}")
            print(f"项目根目录: {summary['project_root']}")
            print("\n文件统计:")
            for category, count in summary['summary'].items():
                print(f"  {category}: {count} 个文件")
        else:
            print("未找到数据索引文件，请先执行数据收集")


if __name__ == "__main__":
    main()