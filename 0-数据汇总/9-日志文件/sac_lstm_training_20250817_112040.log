2025-08-17 11:20:40,108 - INFO - 成功设置中文字体: SimHei
2025-08-17 11:20:40,323 - INFO - 训练曲线图初始化成功
2025-08-17 11:20:40,325 - INFO - 成功加载沪深300股票代码，共300只股票
2025-08-17 11:20:40,326 - INFO - 找到2个因子数据文件
2025-08-17 11:20:48,135 - INFO - 开始加载股票价格数据...
2025-08-17 11:20:48,811 - INFO - 已处理 50/300 只股票
2025-08-17 11:20:49,351 - INFO - 已处理 100/300 只股票
2025-08-17 11:20:49,797 - INFO - 已处理 150/300 只股票
2025-08-17 11:20:50,151 - INFO - 已处理 200/300 只股票
2025-08-17 11:20:50,783 - INFO - 已处理 250/300 只股票
2025-08-17 11:20:51,184 - INFO - 已处理 300/300 只股票
2025-08-17 11:20:51,186 - INFO - 股票数据加载完成，成功加载300只股票
2025-08-17 11:20:54,409 - INFO - 开始加载因子数据...
2025-08-17 11:20:54,412 - INFO - 加载因子数据文件: train_data_20250817_090852_2018-01-01_to_2022-12-31.pkl
2025-08-17 11:20:54,419 - INFO - 文件路径: D:\开发项目\Stock Models\RLStrategy_V2.3（SAC算法LSTM）\0-数据汇总\4-模型训练数据\train_data_20250817_090852_2018-01-01_to_2022-12-31.pkl
2025-08-17 11:20:54,463 - INFO - 因子数据加载完成
2025-08-17 11:20:54,465 - INFO - 数据形状: (332442, 29)
2025-08-17 11:20:54,475 - INFO - 包含股票: 304只
2025-08-17 11:20:54,477 - INFO - 时间范围: 2018-01-02 00:00:00 至 2022-12-30 00:00:00
2025-08-17 11:20:54,483 - INFO - 因子列: ['date', 'code', 'pbMRQ', 'peTTM', 'pctChg', 'turn', 'psTTM', 'pcfNcfTTM', 'KDJ_D', 'ATR', 'KDJ_J', 'DMI_ADX', 'DMI_PDI', 'DMI_MDI', 'KDJ_K', 'CCI', 'VWAP', 'RSI', 'WR', 'psTTM_rank', 'turn_rank', 'peTTM_rank', 'pbMRQ_rank', 'ATR_rank', 'ReturnStd120_rank', 'WR_rank', 'pcfNcfTTM_rank', 'turnover_std20_rank', 'OBV_rank']
2025-08-17 11:21:15,149 - INFO - 开始初始化训练环境...
2025-08-17 11:21:15,153 - INFO - 正在预处理数据...
2025-08-17 11:21:15,225 - INFO - 过滤后的因子数据: 322340条记录
2025-08-17 11:21:15,235 - INFO - 包含股票: 295只
2025-08-17 11:21:15,246 - INFO - 随机选择100只股票进行训练
2025-08-17 11:21:15,282 - INFO - 数据采样: 使用10.0%的数据 (121天)
2025-08-17 11:21:15,287 - INFO - 最终因子数据: 10321条记录
2025-08-17 11:21:15,290 - INFO - 最终股票数量: 87只
2025-08-17 11:21:15,292 - INFO - 使用股票数据: 87只股票
2025-08-17 11:21:15,295 - INFO - 正在创建交易环境...
2025-08-17 11:21:18,243 - INFO - 交易环境创建成功
2025-08-17 11:21:18,245 - INFO - 观察空间维度: (23581,)
2025-08-17 11:21:18,250 - INFO - 动作空间维度: (87,)
2025-08-17 11:21:18,253 - INFO - 正在创建SAC智能体...
2025-08-17 11:21:18,281 - INFO - 🚀 GPU设备检测:
2025-08-17 11:21:18,764 - INFO -   设备数量: 1
2025-08-17 11:21:18,766 - INFO -   当前设备: 0 - NVIDIA GeForce RTX 3060
2025-08-17 11:21:18,774 - INFO -   CUDA版本: 12.1
2025-08-17 11:21:18,778 - INFO -   显存总量: 12.00 GB
2025-08-17 11:21:18,781 - INFO -   可用显存: 12.00 GB
2025-08-17 11:21:18,783 - INFO -   已用显存: 0.00 GB
2025-08-17 11:21:18,785 - INFO - ✅ GPU显存充足，使用GPU训练
2025-08-17 11:21:18,807 - INFO - 🔧 GPU优化选项已启用
2025-08-17 11:21:18,831 - INFO - 📍 最终选择设备: cuda
2025-08-17 11:21:20,723 - INFO - 训练设备: cuda
2025-08-17 11:21:20,727 - INFO - GPU信息: NVIDIA GeForce RTX 3060
2025-08-17 11:21:20,733 - INFO - 显存状态: 12.00GB 可用 / 12.00GB 总计
2025-08-17 11:21:20,739 - INFO - 📊 GPU内存状态: 已用 0.00GB/总计 12.00GB (0.0%)
2025-08-17 11:21:20,762 - INFO - SAC智能体创建成功
2025-08-17 11:21:20,764 - INFO - 开始训练，共1000轮
2025-08-17 11:21:20,766 - INFO - 环境信息: 87只股票, 121个交易日
2025-08-17 11:21:23,568 - INFO - Episode 1: 开始训练
2025-08-17 11:21:23,592 - INFO - 训练过程中发生错误: Input and parameter tensors are not at the same device, found input tensor at cuda:0 and parameter tensor at cpu
2025-08-17 11:21:23,607 - INFO - 错误详情: Traceback (most recent call last):
  File "D:\开发项目\Stock Models\RLStrategy_V2.3（SAC算法LSTM）\2-训练与回测\2-模型训练\sac_lstm_training_gui.py", line 1121, in train_model
    action = agent.select_action(state, evaluate=False)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\开发项目\Stock Models\RLStrategy_V2.3（SAC算法LSTM）\2-训练与回测\2-模型训练\sac_lstm_model.py", line 489, in select_action
    features = self.extract_lstm_features(state)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\开发项目\Stock Models\RLStrategy_V2.3（SAC算法LSTM）\2-训练与回测\2-模型训练\sac_lstm_model.py", line 470, in extract_lstm_features
    lstm_features = self.lstm_feature_extractor(lstm_data)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Development\Python311\Lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Development\Python311\Lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\开发项目\Stock Models\RLStrategy_V2.3（SAC算法LSTM）\2-训练与回测\2-模型训练\sac_lstm_model.py", line 72, in forward
    lstm_out, (hn, cn) = self.lstm(x, (h0, c0))
                         ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Development\Python311\Lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Development\Python311\Lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Development\Python311\Lib\site-packages\torch\nn\modules\rnn.py", line 1123, in forward
    result = _VF.lstm(
             ^^^^^^^^^
RuntimeError: Input and parameter tensors are not at the same device, found input tensor at cuda:0 and parameter tensor at cpu

