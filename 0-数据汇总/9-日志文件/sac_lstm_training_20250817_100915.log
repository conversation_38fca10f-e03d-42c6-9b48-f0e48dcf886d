2025-08-17 10:09:15,320 - INFO - 成功加载沪深300股票代码，共300只股票
2025-08-17 10:09:15,320 - INFO - 找到2个因子数据文件
2025-08-17 10:09:43,912 - INFO - 开始加载股票价格数据...
2025-08-17 10:09:44,617 - INFO - 已处理 50/300 只股票
2025-08-17 10:09:45,172 - INFO - 已处理 100/300 只股票
2025-08-17 10:09:45,638 - INFO - 已处理 150/300 只股票
2025-08-17 10:09:46,018 - INFO - 已处理 200/300 只股票
2025-08-17 10:09:46,675 - INFO - 已处理 250/300 只股票
2025-08-17 10:09:47,086 - INFO - 已处理 300/300 只股票
2025-08-17 10:09:47,087 - INFO - 股票数据加载完成，成功加载300只股票
2025-08-17 10:09:50,930 - INFO - 开始加载因子数据...
2025-08-17 10:09:50,931 - INFO - 加载因子数据文件: train_data_20250817_090852_2018-01-01_to_2022-12-31.pkl
2025-08-17 10:09:50,931 - INFO - 文件路径: D:\开发项目\Stock Models\RLStrategy_V2.3（SAC算法LSTM）\0-数据汇总\4-模型训练数据\train_data_20250817_090852_2018-01-01_to_2022-12-31.pkl
2025-08-17 10:09:50,975 - INFO - 因子数据加载完成
2025-08-17 10:09:50,975 - INFO - 数据形状: (332442, 29)
2025-08-17 10:09:50,985 - INFO - 包含股票: 304只
2025-08-17 10:09:50,987 - INFO - 时间范围: 2018-01-02 00:00:00 至 2022-12-30 00:00:00
2025-08-17 10:09:50,987 - INFO - 因子列: ['date', 'code', 'pbMRQ', 'peTTM', 'pctChg', 'turn', 'psTTM', 'pcfNcfTTM', 'KDJ_D', 'ATR', 'KDJ_J', 'DMI_ADX', 'DMI_PDI', 'DMI_MDI', 'KDJ_K', 'CCI', 'VWAP', 'RSI', 'WR', 'psTTM_rank', 'turn_rank', 'peTTM_rank', 'pbMRQ_rank', 'ATR_rank', 'ReturnStd120_rank', 'WR_rank', 'pcfNcfTTM_rank', 'turnover_std20_rank', 'OBV_rank']
2025-08-17 10:10:05,309 - INFO - 开始初始化训练环境...
2025-08-17 10:10:05,336 - INFO - 正在预处理数据...
2025-08-17 10:10:05,411 - INFO - 过滤后的因子数据: 322340条记录
2025-08-17 10:10:05,421 - INFO - 包含股票: 295只
2025-08-17 10:10:05,431 - INFO - 随机选择50只股票进行训练
2025-08-17 10:10:05,452 - INFO - 数据采样: 使用10.0%的数据 (121天)
2025-08-17 10:10:05,454 - INFO - 最终因子数据: 5378条记录
2025-08-17 10:10:05,454 - INFO - 最终股票数量: 45只
2025-08-17 10:10:05,455 - INFO - 使用股票数据: 45只股票
2025-08-17 10:10:05,455 - INFO - 正在创建交易环境...
2025-08-17 10:10:06,318 - INFO - 交易环境创建成功
2025-08-17 10:10:06,319 - INFO - 观察空间维度: (12199,)
2025-08-17 10:10:06,321 - INFO - 动作空间维度: (45,)
2025-08-17 10:10:08,150 - INFO - SAC智能体创建成功
2025-08-17 10:10:08,152 - INFO - 开始训练，共1000轮
2025-08-17 10:10:09,049 - INFO - 训练过程中发生错误: Expected parameter loc (Tensor of shape (1, 45)) of distribution Normal(loc: torch.Size([1, 45]), scale: torch.Size([1, 45])) to satisfy the constraint Real(), but found invalid values:
tensor([[nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan,
         nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan]],
       device='cuda:0', grad_fn=<AddmmBackward0>)
2025-08-17 10:10:09,053 - INFO - 错误详情: Traceback (most recent call last):
  File "D:\开发项目\Stock Models\RLStrategy_V2.3（SAC算法LSTM）\2-训练与回测\2-模型训练\sac_lstm_training_gui.py", line 719, in train_model
    action = agent.select_action(state, evaluate=False)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\开发项目\Stock Models\RLStrategy_V2.3（SAC算法LSTM）\2-训练与回测\2-模型训练\sac_lstm_model.py", line 309, in select_action
    action, _ = self.actor.sample(features)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\开发项目\Stock Models\RLStrategy_V2.3（SAC算法LSTM）\2-训练与回测\2-模型训练\sac_lstm_model.py", line 133, in sample
    normal = torch.distributions.Normal(mean, std)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Development\Python311\Lib\site-packages\torch\distributions\normal.py", line 59, in __init__
    super().__init__(batch_shape, validate_args=validate_args)
  File "C:\Development\Python311\Lib\site-packages\torch\distributions\distribution.py", line 71, in __init__
    raise ValueError(
ValueError: Expected parameter loc (Tensor of shape (1, 45)) of distribution Normal(loc: torch.Size([1, 45]), scale: torch.Size([1, 45])) to satisfy the constraint Real(), but found invalid values:
tensor([[nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan,
         nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan]],
       device='cuda:0', grad_fn=<AddmmBackward0>)

