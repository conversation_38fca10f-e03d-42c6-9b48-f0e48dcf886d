{"experiment_name": "ranking_factor_exp_20250802_220100", "model_name": "ranking_factor_model_20250802_220100", "stock_list": ["sh.600000", "sh.600009", "sh.600010", "sh.600011", "sh.600015", "sh.600016", "sh.600018", "sh.600019", "sh.600023", "sh.600025"], "train_start_date": "2020-01-01", "train_end_date": "2023-12-31", "test_start_date": "2024-01-01", "test_end_date": "2024-12-31", "data_config": {"project_root": "D:\\开发项目\\Stock Models\\RLStrategy", "stock_data_dir": "D:\\开发项目\\Stock Models\\RLStrategy\\0-数据汇总\\1-原始股票数据", "factor_cache_dir": "D:\\开发项目\\Stock Models\\RLStrategy\\0-数据汇总\\2-因子数据\\factors", "ranking_factor_dir": "D:\\开发项目\\Stock Models\\RLStrategy\\0-数据汇总\\2-因子数据\\ranking_factors", "model_save_dir": "D:\\开发项目\\Stock Models\\RLStrategy\\0-数据汇总\\3-训练模型", "backtest_results_dir": "D:\\开发项目\\Stock Models\\RLStrategy\\0-数据汇总\\4-回测结果", "results_dir": "D:\\开发项目\\Stock Models\\RLStrategy\\0-数据汇总\\4-回测结果", "log_dir": "D:\\开发项目\\Stock Models\\RLStrategy\\0-数据汇总\\5-日志文件"}, "env_config": {"initial_balance": 100000.0, "transaction_fee": 0.001, "lookback_window": 30, "max_positions": 5, "use_ranking_factors": true, "use_precomputed_factors": true, "train_start_date": "2020-01-01", "train_end_date": "2023-12-31", "test_start_date": "2024-01-01", "test_end_date": "2024-12-31"}, "model_config": {"learning_rate": 0.0003, "gamma": 0.99, "gae_lambda": 0.95, "clip_epsilon": 0.2, "value_loss_coef": 0.5, "entropy_coef": 0.01, "max_grad_norm": 0.5, "hidden_dim": 256, "num_layers": 3, "batch_size": 64, "update_epochs": 10, "buffer_size": 2048}, "training_config": {"num_episodes": 1000, "max_steps_per_episode": 1000, "save_frequency": 50, "eval_frequency": 10, "early_stopping_patience": 100, "min_improvement": 0.01}, "backtest_config": {"backtest_mode": "single", "rolling_window_months": 12, "rebalance_frequency": "monthly", "benchmark_symbols": ["000300.SH", "000905.SH"], "risk_free_rate": 0.03, "confidence_level": 0.05}, "created_time": "2025-08-02T22:01:00.403586"}