# 数据汇总目录说明

本目录用于集中管理项目中产生的各类数据文件，提供统一的数据存储和管理方案。

## 目录结构

```
0-数据汇总/
├── 1-原始股票数据/          # 从数据源下载的原始行情数据
├── 2-因子数据/              # 计算出的个股因子和排序因子
├── 3-训练模型/              # 训练好的机器学习模型文件
├── 4-回测结果/              # 各种回测的结果文件和报告
├── 5-日志文件/              # 运行过程中产生的日志文件
├── data_manager.py          # 数据管理脚本
├── data_index.json          # 数据索引文件（自动生成）
├── file_list.csv            # 文件清单（自动生成）
└── README.md                # 本说明文件
```

## 数据分类说明

### 1-原始股票数据
**来源**: `1-数据准备/1-最新数据下载/data/`

**包含内容**:
- 股票日线行情数据（开高低收、成交量等）
- 基础信息数据（股票代码、名称、行业等）
- 财务数据（如果有）

**文件格式**: CSV, Excel, JSON, Parquet

### 2-因子数据
**来源**: 
- `1-数据准备/2-个股因子生成/factor_cache/`
- `1-数据准备/3-排序因子生成/factor_cache/`

**包含内容**:
- 个股技术指标因子
- 价量因子
- 波动率因子
- 百分位排序因子

**文件格式**: PKL, CSV, HDF5

**重要文件**:
- `factors/{股票代码}_factors.pkl`: 个股因子数据
- 包含原始因子和百分位排序因子

### 3-训练模型
**来源**: 
- `2-训练与回测/2-模型训练/models/`
- `2-训练与回测/4-逐段回测/rolling_backtest_results/`

**包含内容**:
- 强化学习训练好的模型
- 滚动回测中的各阶段模型
- 模型配置文件

**文件格式**: PKL, PTH, H5, JOBLIB, MODEL

### 4-回测结果
**来源**: 
- `2-训练与回测/3-回测/results/`
- `2-训练与回测/3-回测/reports/`
- `2-训练与回测/4-逐段回测/rolling_backtest_results/`

**包含内容**:
- 单次回测结果
- 滚动回测结果
- 性能分析报告
- 可视化图表

**文件格式**: CSV, Excel, JSON, HTML, PNG, JPG

### 5-日志文件
**来源**: 
- `2-训练与回测/2-模型训练/logs/`
- `2-训练与回测/3-回测/logs/`
- 项目根目录的日志文件

**包含内容**:
- 训练过程日志
- 回测过程日志
- 错误和调试信息

**文件格式**: LOG, TXT

## 使用方法

### 自动数据收集

```bash
# 收集所有数据到汇总目录
python data_manager.py --action collect

# 指定项目根目录
python data_manager.py --action collect --project-root "D:\开发项目\Stock Models\RLStrategy"
```

### 查看数据汇总信息

```bash
# 显示数据统计信息
python data_manager.py --action summary
```

### 清理汇总目录

```bash
# 清理所有收集的数据（保留脚本文件）
python data_manager.py --action clean
```

### 在项目主控制脚本中使用

可以在 `run_project.py` 中集成数据收集功能：

```python
# 在项目完成后自动收集数据
from "0-数据汇总.data_manager" import DataManager

manager = DataManager()
manager.collect_all_data()
```

## 数据索引文件

### data_index.json
包含完整的数据收集信息：
- 收集时间
- 项目路径
- 各类文件统计
- 详细文件信息

### file_list.csv
包含所有文件的清单：
- 文件分类
- 源路径和目标路径
- 文件大小
- 修改时间

## 数据管理最佳实践

### 1. 定期收集
建议在以下时机执行数据收集：
- 完成数据准备后
- 完成模型训练后
- 完成回测后
- 项目阶段性完成后

### 2. 版本管理
对于重要的模型和结果，建议：
- 添加时间戳
- 记录模型参数
- 保留多个版本

### 3. 存储优化
- 定期清理过期数据
- 压缩大文件
- 使用高效的文件格式（如Parquet, HDF5）

### 4. 备份策略
- 定期备份重要数据
- 使用云存储或外部存储
- 保留多个备份副本

## 注意事项

1. **存储空间**: 数据汇总可能占用大量存储空间，请确保有足够的磁盘空间

2. **数据同步**: 汇总目录中的数据是源数据的副本，修改汇总目录中的数据不会影响源数据

3. **权限管理**: 确保脚本有足够的权限访问源数据目录

4. **数据安全**: 敏感数据请注意加密和访问控制

5. **定期维护**: 建议定期清理和重新收集数据，保持数据的时效性

## 故障排除

### 常见问题

1. **权限错误**
   - 确保对源目录和目标目录有读写权限
   - 以管理员身份运行脚本

2. **路径错误**
   - 检查项目根目录路径是否正确
   - 确认源数据目录存在

3. **磁盘空间不足**
   - 清理不必要的文件
   - 移动数据到其他磁盘

4. **文件被占用**
   - 关闭正在使用文件的程序
   - 等待长时间运行的任务完成

### 日志查看
数据管理脚本会输出详细的日志信息，包括：
- 复制的文件路径
- 错误信息
- 统计信息

## 扩展功能

可以根据需要扩展数据管理功能：
- 自动压缩功能
- 数据验证功能
- 增量更新功能
- 远程备份功能
- 数据可视化功能

---

**提示**: 建议将数据收集集成到项目的自动化流程中，确保数据的完整性和一致性。