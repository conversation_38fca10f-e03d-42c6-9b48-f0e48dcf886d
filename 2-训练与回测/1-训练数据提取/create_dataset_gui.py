
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import pandas as pd
import os
import glob
from datetime import datetime
import threading

class DataExtractorGUI:
    def __init__(self, master):
        self.master = master
        master.title("模型数据提取工具")
        master.geometry("700x550")

        # Configure style
        self.style = ttk.Style()
        self.style.theme_use('clam')
        self.style.configure('TLabel', font=('Helvetica', 10))
        self.style.configure('TButton', font=('Helvetica', 10, 'bold'))
        self.style.configure('TEntry', font=('Helvetica', 10))
        self.style.configure('TFrame', padding=10)
        self.style.configure('TLabelframe', padding=10)

        # Base path configuration
        self.base_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
        self.source_price_path = os.path.join(self.base_path, '0-数据汇总', '1-原始股票数据')
        self.source_factor_path = os.path.join(self.base_path, '0-数据汇总', '2-个股因子数据')
        self.source_ranking_factor_path = os.path.join(self.base_path, '0-数据汇总', '3-排序因子数据')
        self.output_path = os.path.join(self.base_path, '0-数据汇总', '4-模型训练数据')
        os.makedirs(self.output_path, exist_ok=True)

        # --- Training Data Frame ---
        train_frame = ttk.LabelFrame(master, text="训练数据生成")
        train_frame.pack(fill="x", padx=10, pady=5)

        ttk.Label(train_frame, text="开始日期 (YYYY-MM-DD):").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.train_start_date_entry = ttk.Entry(train_frame, width=20)
        self.train_start_date_entry.grid(row=0, column=1, padx=5, pady=5)
        self.train_start_date_entry.insert(0, "2018-01-01")

        ttk.Label(train_frame, text="结束日期 (YYYY-MM-DD):").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.train_end_date_entry = ttk.Entry(train_frame, width=20)
        self.train_end_date_entry.grid(row=1, column=1, padx=5, pady=5)
        self.train_end_date_entry.insert(0, "2022-12-31")

        self.generate_train_button = ttk.Button(train_frame, text="生成训练数据", command=self.generate_train_data_threaded)
        self.generate_train_button.grid(row=0, column=2, rowspan=2, padx=10, pady=5, ipady=10)

        # --- Testing Data Frame ---
        test_frame = ttk.LabelFrame(master, text="测试数据生成")
        test_frame.pack(fill="x", padx=10, pady=5)

        ttk.Label(test_frame, text="开始日期 (YYYY-MM-DD):").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.test_start_date_entry = ttk.Entry(test_frame, width=20)
        self.test_start_date_entry.grid(row=0, column=1, padx=5, pady=5)
        self.test_start_date_entry.insert(0, "2023-01-01")

        ttk.Label(test_frame, text="结束日期 (YYYY-MM-DD):").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.test_end_date_entry = ttk.Entry(test_frame, width=20)
        self.test_end_date_entry.grid(row=1, column=1, padx=5, pady=5)
        self.test_end_date_entry.insert(0, "2023-12-31")

        self.generate_test_button = ttk.Button(test_frame, text="生成测试数据", command=self.generate_test_data_threaded)
        self.generate_test_button.grid(row=0, column=2, rowspan=2, padx=10, pady=5, ipady=10)

        # --- Log Area ---
        log_frame = ttk.LabelFrame(master, text="日志")
        log_frame.pack(fill="both", expand=True, padx=10, pady=10)
        self.log_area = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.log_area.pack(fill="both", expand=True)
        self.log("GUI初始化完成。")
        self.log(f"数据源基地址: {self.base_path}")
        self.log(f"输出目录: {self.output_path}")


    def log(self, message):
        self.master.after(0, self._log_update, message)

    def _log_update(self, message):
        self.log_area.insert(tk.END, f"[{datetime.now().strftime('%H:%M:%S')}] {message}\n")
        self.log_area.see(tk.END)

    def set_ui_state(self, state):
        if state == 'disabled':
            self.generate_train_button.config(state=tk.DISABLED)
            self.generate_test_button.config(state=tk.DISABLED)
        else:
            self.generate_train_button.config(state=tk.NORMAL)
            self.generate_test_button.config(state=tk.NORMAL)

    def generate_train_data_threaded(self):
        start_date = self.train_start_date_entry.get()
        end_date = self.train_end_date_entry.get()
        threading.Thread(target=self.extract_data, args=(start_date, end_date, "train"), daemon=True).start()

    def generate_test_data_threaded(self):
        start_date = self.test_start_date_entry.get()
        end_date = self.test_end_date_entry.get()
        threading.Thread(target=self.extract_data, args=(start_date, end_date, "test"), daemon=True).start()

    def extract_data(self, start_date_str, end_date_str, mode):
        self.set_ui_state('disabled')
        try:
            self.log(f"开始生成 {mode} 数据...")
            self.log(f"时间范围: {start_date_str} to {end_date_str}")

            # Validate dates
            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            except ValueError:
                self.log("错误: 日期格式不正确，请使用 YYYY-MM-DD 格式。")
                messagebox.showerror("格式错误", "日期格式不正确，请使用 YYYY-MM-DD 格式。")
                self.set_ui_state('normal')
                return

            stock_list = glob.glob(os.path.join(self.source_price_path, '*.csv'))
            if not stock_list:
                self.log(f"错误: 在 {self.source_price_path} 中未找到任何原始股票数据 (.csv 文件)。")
                messagebox.showerror("文件未找到", f"在 {self.source_price_path} 中未找到任何原始股票数据 (.csv 文件)。")
                self.set_ui_state('normal')
                return
            
            self.log(f"发现 {len(stock_list)} 个股票文件。开始处理...")

            all_stocks_data = []
            processed_count = 0
            for stock_csv_path in stock_list:
                stock_code_with_ext = os.path.basename(stock_csv_path).replace('_hfq.csv', '')
                
                factor_pkl_path = os.path.join(self.source_factor_path, f"{stock_code_with_ext}_factors.pkl")
                ranking_pkl_path = os.path.join(self.source_ranking_factor_path, f"{stock_code_with_ext}_ranking_factors.pkl")

                if not os.path.exists(factor_pkl_path) or not os.path.exists(ranking_pkl_path):
                    self.log(f"警告: {stock_code_with_ext} 缺少因子文件，已跳过。")
                    continue

                try:
                    # Load data
                    price_df = pd.read_csv(stock_csv_path)
                    factor_df = pd.read_pickle(factor_pkl_path)
                    ranking_df = pd.read_pickle(ranking_pkl_path)

                    # Data cleaning and preparation
                    for df in [price_df, factor_df, ranking_df]:
                        if 'date' not in df.columns:
                            raise ValueError(f"文件 {os.path.basename(df.name)} 中缺少 'date' 列。")
                        df['date'] = pd.to_datetime(df['date'])
                        if 'code' in df.columns:
                           df['code'] = df['code'].astype(str)


                    # Merge data
                    merged_df = pd.merge(price_df, factor_df, on=['date', 'code'], how='inner')
                    merged_df = pd.merge(merged_df, ranking_df, on=['date', 'code'], how='inner')
                    
                    # Filter by date
                    mask = (merged_df['date'] >= start_date) & (merged_df['date'] <= end_date)
                    filtered_df = merged_df.loc[mask]

                    if not filtered_df.empty:
                        all_stocks_data.append(filtered_df)
                    
                    processed_count += 1
                    if processed_count % 50 == 0:
                        self.log(f"已处理 {processed_count}/{len(stock_list)} 个股票ảng...")

                except Exception as e:
                    self.log(f"错误: 处理 {stock_code_with_ext} 时出错: {e}")

            if not all_stocks_data:
                self.log("错误: 未能提取任何数据。请检查日期范围和源文件ảng。")
                messagebox.showwarning("无数据", "未能在指定日期范围内提取任何有效数据ảng。")
                self.set_ui_state('normal')
                return

            self.log("所有股票数据处理完毕，正在合并为一个文件...")
            final_df = pd.concat(all_stocks_data, ignore_index=True)
            final_df.sort_values(by=['date', 'code'], inplace=True)

            # Save file
            output_filename = f"{mode}_data_{start_date_str}_to_{end_date_str}.pkl"
            output_filepath = os.path.join(self.output_path, output_filename)
            final_df.to_pickle(output_filepath)

            self.log("="*50)
            self.log(f"成功！ {mode} 数据集已生成。")
            self.log(f"总计包含 {len(final_df)} 条记录, {len(final_df['code'].unique())} 个股票。")
            self.log(f"文件已保存至: {output_filepath}")
            self.log("="*50)
            messagebox.showinfo("成功", f"{mode.capitalize()} 数据集已成功生成！\n文件保存在:\n{output_filepath}")

        except Exception as e:
            self.log(f"发生严重错误: {e}")
            messagebox.showerror("严重错误", f"处理过程中发生严重错误: {e}")
        finally:
            self.set_ui_state('normal')


if __name__ == "__main__":
    root = tk.Tk()
    gui = DataExtractorGUI(root)
    root.mainloop()
