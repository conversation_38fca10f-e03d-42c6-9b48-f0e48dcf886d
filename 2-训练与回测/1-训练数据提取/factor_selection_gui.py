import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import pandas as pd
import os
import glob
from datetime import datetime
import threading
import pickle

class FactorSelectionGUI:
    def __init__(self, master):
        self.master = master
        master.title("因子选择数据提取工具")
        master.geometry("900x700")
        
        # Configure style
        self.style = ttk.Style()
        self.style.theme_use('clam')
        self.style.configure('TLabel', font=('Helvetica', 10))
        self.style.configure('TButton', font=('Helvetica', 10, 'bold'))
        self.style.configure('TEntry', font=('Helvetica', 10))
        self.style.configure('TFrame', padding=5)
        self.style.configure('TLabelframe', padding=5)
        
        # Base path configuration
        self.base_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
        self.source_price_path = os.path.join(self.base_path, '0-数据汇总', '1-原始股票数据')
        self.source_factor_path = os.path.join(self.base_path, '0-数据汇总', '2-个股因子数据')
        self.source_ranking_factor_path = os.path.join(self.base_path, '0-数据汇总', '3-排序因子数据')
        self.output_path = os.path.join(self.base_path, '0-数据汇总', '4-模型训练数据')
        os.makedirs(self.output_path, exist_ok=True)
        
        # Available factors
        self.price_factors = ['open', 'high', 'low', 'close', 'preclose', 'volume', 'amount', 'adjustflag', 'turn', 'tradestatus', 'pctChg', 'peTTM', 'psTTM', 'pcfNcfTTM', 'pbMRQ', 'isST']
        self.individual_factors = ['RSI', 'MACD_MACD', 'MACD_Signal', 'MACD_Histogram', 'KDJ_K', 'KDJ_D', 'KDJ_J', 'BOLL_Upper', 'BOLL_Middle', 'BOLL_Lower', 'CCI', 'WR', 'ATR', 'OBV', 'VWAP', 'DMI_PDI', 'DMI_MDI', 'DMI_ADX', 'turnover_std20', 'ReturnStd120']
        self.ranking_factors = ['turn_rank', 'peTTM_rank', 'psTTM_rank', 'pcfNcfTTM_rank', 'pbMRQ_rank', 'WR_rank', 'ATR_rank', 'OBV_rank', 'turnover_std20_rank', 'ReturnStd120_rank']
        
        # Selected factors storage
        self.selected_price_factors = set()
        self.selected_individual_factors = set()
        self.selected_ranking_factors = set()
        
        self.create_widgets()
        self.load_available_factors()
        
    def create_widgets(self):
        # Create main notebook for tabs
        notebook = ttk.Notebook(self.master)
        notebook.pack(fill="both", expand=True, padx=10, pady=5)
        
        # Factor selection tab
        factor_frame = ttk.Frame(notebook)
        notebook.add(factor_frame, text="因子选择")
        
        # Data extraction tab
        extraction_frame = ttk.Frame(notebook)
        notebook.add(extraction_frame, text="数据提取")
        
        self.create_factor_selection_tab(factor_frame)
        self.create_data_extraction_tab(extraction_frame)
        
    def create_factor_selection_tab(self, parent):
        # Create three columns for different factor types
        columns_frame = ttk.Frame(parent)
        columns_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Price factors column
        price_frame = ttk.LabelFrame(columns_frame, text="原始股票数据因子")
        price_frame.pack(side="left", fill="both", expand=True, padx=5)
        
        # Select all/none buttons for price factors
        price_btn_frame = ttk.Frame(price_frame)
        price_btn_frame.pack(fill="x", pady=5)
        ttk.Button(price_btn_frame, text="全选", command=lambda: self.select_all_factors('price')).pack(side="left", padx=2)
        ttk.Button(price_btn_frame, text="全不选", command=lambda: self.deselect_all_factors('price')).pack(side="left", padx=2)
        
        # Price factors listbox with checkboxes
        price_scroll_frame = ttk.Frame(price_frame)
        price_scroll_frame.pack(fill="both", expand=True)
        
        price_canvas = tk.Canvas(price_scroll_frame)
        price_scrollbar = ttk.Scrollbar(price_scroll_frame, orient="vertical", command=price_canvas.yview)
        self.price_scrollable_frame = ttk.Frame(price_canvas)
        
        self.price_scrollable_frame.bind(
            "<Configure>",
            lambda e: price_canvas.configure(scrollregion=price_canvas.bbox("all"))
        )
        
        price_canvas.create_window((0, 0), window=self.price_scrollable_frame, anchor="nw")
        price_canvas.configure(yscrollcommand=price_scrollbar.set)
        
        price_canvas.pack(side="left", fill="both", expand=True)
        price_scrollbar.pack(side="right", fill="y")
        
        # Individual factors column
        individual_frame = ttk.LabelFrame(columns_frame, text="个股因子数据")
        individual_frame.pack(side="left", fill="both", expand=True, padx=5)
        
        # Select all/none buttons for individual factors
        individual_btn_frame = ttk.Frame(individual_frame)
        individual_btn_frame.pack(fill="x", pady=5)
        ttk.Button(individual_btn_frame, text="全选", command=lambda: self.select_all_factors('individual')).pack(side="left", padx=2)
        ttk.Button(individual_btn_frame, text="全不选", command=lambda: self.deselect_all_factors('individual')).pack(side="left", padx=2)
        
        # Individual factors listbox with checkboxes
        individual_scroll_frame = ttk.Frame(individual_frame)
        individual_scroll_frame.pack(fill="both", expand=True)
        
        individual_canvas = tk.Canvas(individual_scroll_frame)
        individual_scrollbar = ttk.Scrollbar(individual_scroll_frame, orient="vertical", command=individual_canvas.yview)
        self.individual_scrollable_frame = ttk.Frame(individual_canvas)
        
        self.individual_scrollable_frame.bind(
            "<Configure>",
            lambda e: individual_canvas.configure(scrollregion=individual_canvas.bbox("all"))
        )
        
        individual_canvas.create_window((0, 0), window=self.individual_scrollable_frame, anchor="nw")
        individual_canvas.configure(yscrollcommand=individual_scrollbar.set)
        
        individual_canvas.pack(side="left", fill="both", expand=True)
        individual_scrollbar.pack(side="right", fill="y")
        
        # Ranking factors column
        ranking_frame = ttk.LabelFrame(columns_frame, text="排序因子数据")
        ranking_frame.pack(side="left", fill="both", expand=True, padx=5)
        
        # Select all/none buttons for ranking factors
        ranking_btn_frame = ttk.Frame(ranking_frame)
        ranking_btn_frame.pack(fill="x", pady=5)
        ttk.Button(ranking_btn_frame, text="全选", command=lambda: self.select_all_factors('ranking')).pack(side="left", padx=2)
        ttk.Button(ranking_btn_frame, text="全不选", command=lambda: self.deselect_all_factors('ranking')).pack(side="left", padx=2)
        
        # Ranking factors listbox with checkboxes
        ranking_scroll_frame = ttk.Frame(ranking_frame)
        ranking_scroll_frame.pack(fill="both", expand=True)
        
        ranking_canvas = tk.Canvas(ranking_scroll_frame)
        ranking_scrollbar = ttk.Scrollbar(ranking_scroll_frame, orient="vertical", command=ranking_canvas.yview)
        self.ranking_scrollable_frame = ttk.Frame(ranking_canvas)
        
        self.ranking_scrollable_frame.bind(
            "<Configure>",
            lambda e: ranking_canvas.configure(scrollregion=ranking_canvas.bbox("all"))
        )
        
        ranking_canvas.create_window((0, 0), window=self.ranking_scrollable_frame, anchor="nw")
        ranking_canvas.configure(yscrollcommand=ranking_scrollbar.set)
        
        ranking_canvas.pack(side="left", fill="both", expand=True)
        ranking_scrollbar.pack(side="right", fill="y")
        
        # Store canvas references for scrolling
        self.price_canvas = price_canvas
        self.individual_canvas = individual_canvas
        self.ranking_canvas = ranking_canvas
        
    def create_data_extraction_tab(self, parent):
        # Data generation (combined training and testing)
        data_frame = ttk.LabelFrame(parent, text="数据生成")
        data_frame.pack(fill="x", padx=10, pady=5)
        
        # Training data section
        ttk.Label(data_frame, text="训练数据:", font=('TkDefaultFont', 9, 'bold')).grid(row=0, column=0, columnspan=2, padx=5, pady=(5,2), sticky="w")
        ttk.Label(data_frame, text="开始日期 (YYYY-MM-DD):").grid(row=1, column=0, padx=5, pady=2, sticky="w")
        self.train_start_date_entry = ttk.Entry(data_frame, width=15)
        self.train_start_date_entry.grid(row=1, column=1, padx=5, pady=2)
        self.train_start_date_entry.insert(0, "2018-01-01")
        
        ttk.Label(data_frame, text="结束日期 (YYYY-MM-DD):").grid(row=2, column=0, padx=5, pady=2, sticky="w")
        self.train_end_date_entry = ttk.Entry(data_frame, width=15)
        self.train_end_date_entry.grid(row=2, column=1, padx=5, pady=2)
        self.train_end_date_entry.insert(0, "2022-12-31")
        
        # Testing data section
        ttk.Label(data_frame, text="测试数据:", font=('TkDefaultFont', 9, 'bold')).grid(row=0, column=2, columnspan=2, padx=5, pady=(5,2), sticky="w")
        ttk.Label(data_frame, text="开始日期 (YYYY-MM-DD):").grid(row=1, column=2, padx=5, pady=2, sticky="w")
        self.test_start_date_entry = ttk.Entry(data_frame, width=15)
        self.test_start_date_entry.grid(row=1, column=3, padx=5, pady=2)
        self.test_start_date_entry.insert(0, "2023-01-01")
        
        ttk.Label(data_frame, text="结束日期 (YYYY-MM-DD):").grid(row=2, column=2, padx=5, pady=2, sticky="w")
        self.test_end_date_entry = ttk.Entry(data_frame, width=15)
        self.test_end_date_entry.grid(row=2, column=3, padx=5, pady=2)
        self.test_end_date_entry.insert(0, "2023-12-31")
        
        # Combined generation button
        self.generate_data_button = ttk.Button(data_frame, text="生成训练和测试数据对", command=self.generate_data_pair_threaded)
        self.generate_data_button.grid(row=3, column=0, columnspan=4, padx=10, pady=10, ipady=10)
        
        # Log area
        log_frame = ttk.LabelFrame(parent, text="日志")
        log_frame.pack(fill="both", expand=True, padx=10, pady=10)
        self.log_area = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.log_area.pack(fill="both", expand=True)
        
        self.log("GUI初始化完成。")
        self.log(f"数据源基地址: {self.base_path}")
        self.log(f"输出目录: {self.output_path}")
        
    def load_available_factors(self):
        """Load and display available factors from actual data files"""
        try:
            # Load sample files to get actual available factors for individual and ranking factors
            sample_files = {
                'individual': glob.glob(os.path.join(self.source_factor_path, '*.pkl'))[:1],
                'ranking': glob.glob(os.path.join(self.source_ranking_factor_path, '*.pkl'))[:1]
            }
            
            # Update individual and ranking factor lists based on actual data
            # Keep the predefined price_factors list as is
                
            if sample_files['individual']:
                individual_df = pd.read_pickle(sample_files['individual'][0])
                self.individual_factors = [col for col in individual_df.columns if not col.endswith('_rank')]
                
            if sample_files['ranking']:
                ranking_df = pd.read_pickle(sample_files['ranking'][0])
                self.ranking_factors = [col for col in ranking_df.columns if col not in ['date']]
                
        except Exception as e:
            self.log(f"警告: 无法加载样本文件获取因子列表: {e}")
            
        # Create checkboxes for each factor type
        self.create_factor_checkboxes()
        
    def create_factor_checkboxes(self):
        """Create checkboxes for all factor types"""
        # Price factors checkboxes
        self.price_vars = {}
        for i, factor in enumerate(self.price_factors):
            var = tk.BooleanVar()
            self.price_vars[factor] = var
            cb = ttk.Checkbutton(self.price_scrollable_frame, text=factor, variable=var,
                               command=lambda f=factor: self.update_selected_factors('price', f))
            cb.grid(row=i, column=0, sticky="w", padx=5, pady=2)
            
        # Individual factors checkboxes
        self.individual_vars = {}
        for i, factor in enumerate(self.individual_factors):
            var = tk.BooleanVar()
            self.individual_vars[factor] = var
            cb = ttk.Checkbutton(self.individual_scrollable_frame, text=factor, variable=var,
                               command=lambda f=factor: self.update_selected_factors('individual', f))
            cb.grid(row=i, column=0, sticky="w", padx=5, pady=2)
            
        # Ranking factors checkboxes
        self.ranking_vars = {}
        for i, factor in enumerate(self.ranking_factors):
            var = tk.BooleanVar()
            self.ranking_vars[factor] = var
            cb = ttk.Checkbutton(self.ranking_scrollable_frame, text=factor, variable=var,
                               command=lambda f=factor: self.update_selected_factors('ranking', f))
            cb.grid(row=i, column=0, sticky="w", padx=5, pady=2)
            
        # Bind mouse wheel to canvases
        self.bind_mousewheel()
        
    def bind_mousewheel(self):
        """Bind mouse wheel scrolling to canvases"""
        def _on_mousewheel(event, canvas):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            
        for canvas in [self.price_canvas, self.individual_canvas, self.ranking_canvas]:
            canvas.bind("<MouseWheel>", lambda e, c=canvas: _on_mousewheel(e, c))
            
    def update_selected_factors(self, factor_type, factor_name):
        """Update selected factors based on checkbox state"""
        if factor_type == 'price':
            if self.price_vars[factor_name].get():
                self.selected_price_factors.add(factor_name)
            else:
                self.selected_price_factors.discard(factor_name)
        elif factor_type == 'individual':
            if self.individual_vars[factor_name].get():
                self.selected_individual_factors.add(factor_name)
            else:
                self.selected_individual_factors.discard(factor_name)
        elif factor_type == 'ranking':
            if self.ranking_vars[factor_name].get():
                self.selected_ranking_factors.add(factor_name)
            else:
                self.selected_ranking_factors.discard(factor_name)
                
    def select_all_factors(self, factor_type):
        """Select all factors of a given type"""
        if factor_type == 'price':
            for factor, var in self.price_vars.items():
                var.set(True)
                self.selected_price_factors.add(factor)
        elif factor_type == 'individual':
            for factor, var in self.individual_vars.items():
                var.set(True)
                self.selected_individual_factors.add(factor)
        elif factor_type == 'ranking':
            for factor, var in self.ranking_vars.items():
                var.set(True)
                self.selected_ranking_factors.add(factor)
                
    def deselect_all_factors(self, factor_type):
        """Deselect all factors of a given type"""
        if factor_type == 'price':
            for factor, var in self.price_vars.items():
                var.set(False)
            self.selected_price_factors.clear()
        elif factor_type == 'individual':
            for factor, var in self.individual_vars.items():
                var.set(False)
            self.selected_individual_factors.clear()
        elif factor_type == 'ranking':
            for factor, var in self.ranking_vars.items():
                var.set(False)
            self.selected_ranking_factors.clear()
            
    def log(self, message):
        """Add message to log area"""
        self.master.after(0, self._log_update, message)
        
    def _log_update(self, message):
        """Update log area with message"""
        self.log_area.insert(tk.END, f"[{datetime.now().strftime('%H:%M:%S')}] {message}\n")
        self.log_area.see(tk.END)
        
    def set_ui_state(self, state):
        """Enable or disable UI elements"""
        if state == 'disabled':
            self.generate_data_button.config(state=tk.DISABLED)
        else:
            self.generate_data_button.config(state=tk.NORMAL)
            
    def generate_data_pair_threaded(self):
        """Generate training and testing data pair in a separate thread"""
        train_start_date = self.train_start_date_entry.get()
        train_end_date = self.train_end_date_entry.get()
        test_start_date = self.test_start_date_entry.get()
        test_end_date = self.test_end_date_entry.get()
        threading.Thread(target=self.extract_data_pair, args=(train_start_date, train_end_date, test_start_date, test_end_date), daemon=True).start()
        
    def generate_train_data_threaded(self):
        """Generate training data in a separate thread"""
        start_date = self.train_start_date_entry.get()
        end_date = self.train_end_date_entry.get()
        threading.Thread(target=self.extract_data, args=(start_date, end_date, "train"), daemon=True).start()
        
    def generate_test_data_threaded(self):
        """Generate testing data in a separate thread"""
        start_date = self.test_start_date_entry.get()
        end_date = self.test_end_date_entry.get()
        threading.Thread(target=self.extract_data, args=(start_date, end_date, "test"), daemon=True).start()
        
    def extract_data(self, start_date_str, end_date_str, mode):
        """Extract data based on selected factors and date range"""
        self.set_ui_state('disabled')
        try:
            self.log(f"开始生成 {mode} 数据...")
            self.log(f"时间范围: {start_date_str} to {end_date_str}")
            
            # Check if any factors are selected
            total_selected = len(self.selected_price_factors) + len(self.selected_individual_factors) + len(self.selected_ranking_factors)
            if total_selected == 0:
                self.log("错误: 请至少选择一个因子。")
                messagebox.showerror("选择错误", "请至少选择一个因子。")
                self.set_ui_state('normal')
                return
                
            self.log(f"已选择因子数量: 原始股票数据({len(self.selected_price_factors)}), 个股因子({len(self.selected_individual_factors)}), 排序因子({len(self.selected_ranking_factors)})")
            
            # Validate dates
            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            except ValueError:
                self.log("错误: 日期格式不正确，请使用 YYYY-MM-DD 格式。")
                messagebox.showerror("格式错误", "日期格式不正确，请使用 YYYY-MM-DD 格式。")
                self.set_ui_state('normal')
                return
                
            # Get stock list
            stock_list = glob.glob(os.path.join(self.source_price_path, '*.csv'))
            if not stock_list:
                self.log(f"错误: 在 {self.source_price_path} 中未找到任何原始股票数据 (.csv 文件)。")
                messagebox.showerror("文件未找到", f"在 {self.source_price_path} 中未找到任何原始股票数据 (.csv 文件)。")
                self.set_ui_state('normal')
                return
                
            self.log(f"发现 {len(stock_list)} 个股票文件。开始处理...")
            
            all_stocks_data = []
            processed_count = 0
            
            for stock_csv_path in stock_list:
                stock_code_with_ext = os.path.basename(stock_csv_path).replace('_hfq.csv', '')
                
                factor_pkl_path = os.path.join(self.source_factor_path, f"{stock_code_with_ext}_factors.pkl")
                ranking_pkl_path = os.path.join(self.source_ranking_factor_path, f"{stock_code_with_ext}_ranking_factors.pkl")
                
                # Check if required files exist based on selected factors
                files_needed = []
                if self.selected_price_factors:
                    files_needed.append(('price', stock_csv_path))
                if self.selected_individual_factors:
                    files_needed.append(('individual', factor_pkl_path))
                if self.selected_ranking_factors:
                    files_needed.append(('ranking', ranking_pkl_path))
                    
                missing_files = []
                for file_type, file_path in files_needed:
                    if not os.path.exists(file_path):
                        missing_files.append(file_type)
                        
                if missing_files:
                    self.log(f"警告: {stock_code_with_ext} 缺少 {', '.join(missing_files)} 文件，已跳过。")
                    continue
                    
                try:
                    # Load and process data
                    dataframes = []
                    
                    # Load price data if selected
                    if self.selected_price_factors:
                        price_df = pd.read_csv(stock_csv_path)
                        price_df['date'] = pd.to_datetime(price_df['date'])
                        if 'code' in price_df.columns:
                            price_df['code'] = price_df['code'].astype(str)
                        # Select only required columns plus date and code
                        price_cols = ['date', 'code'] + [col for col in self.selected_price_factors if col in price_df.columns]
                        price_df = price_df[price_cols]
                        dataframes.append(price_df)
                        
                    # Load individual factor data if selected
                    if self.selected_individual_factors:
                        factor_df = pd.read_pickle(factor_pkl_path)
                        # Check if 'date' is in index, if so, reset_index
                        if factor_df.index.name == 'date':
                            factor_df = factor_df.reset_index()
                        elif isinstance(factor_df.index, pd.DatetimeIndex):
                            # 如果索引是日期但没有名称，也需要重置
                            factor_df = factor_df.reset_index()
                            factor_df.rename(columns={'index': 'date'}, inplace=True)
                        
                        # 处理列名大小写问题
                        if 'Date' in factor_df.columns and 'date' not in factor_df.columns:
                            factor_df.rename(columns={'Date': 'date'}, inplace=True)
                        
                        if 'date' in factor_df.columns:
                            factor_df['date'] = pd.to_datetime(factor_df['date'])
                            factor_df['code'] = stock_code_with_ext
                            # Select only required columns plus date and code
                            factor_cols = ['date', 'code'] + [col for col in self.selected_individual_factors if col in factor_df.columns]
                            factor_df = factor_df[factor_cols]
                            dataframes.append(factor_df)
                        else:
                            self.log(f"警告: {stock_code_with_ext} 个股因子数据没有找到date列，跳过处理")
                            continue
                        
                    # Load ranking factor data if selected
                    if self.selected_ranking_factors:
                        ranking_df = pd.read_pickle(ranking_pkl_path)
                        # Check if 'date' is in index, if so, reset_index
                        if ranking_df.index.name == 'date':
                            ranking_df = ranking_df.reset_index()
                        elif isinstance(ranking_df.index, pd.DatetimeIndex):
                            # 如果索引是日期但没有名称，也需要重置
                            ranking_df = ranking_df.reset_index()
                            ranking_df.rename(columns={'index': 'date'}, inplace=True)
                        
                        # 处理列名大小写问题
                        if 'Date' in ranking_df.columns and 'date' not in ranking_df.columns:
                            ranking_df.rename(columns={'Date': 'date'}, inplace=True)
                        
                        if 'date' in ranking_df.columns:
                            ranking_df['date'] = pd.to_datetime(ranking_df['date'])
                            ranking_df['code'] = stock_code_with_ext
                            # Select only required columns plus date and code
                            ranking_cols = ['date', 'code'] + [col for col in self.selected_ranking_factors if col in ranking_df.columns]
                            ranking_df = ranking_df[ranking_cols]
                            dataframes.append(ranking_df)
                        else:
                            self.log(f"警告: {stock_code_with_ext} 排序因子数据没有找到date列，跳过处理")
                            continue
                        
                    # Merge dataframes
                    if len(dataframes) == 1:
                        merged_df = dataframes[0]
                    else:
                        merged_df = dataframes[0]
                        for df in dataframes[1:]:
                            merged_df = pd.merge(merged_df, df, on=['date', 'code'], how='inner')
                            
                    # Filter by date
                    mask = (merged_df['date'] >= start_date) & (merged_df['date'] <= end_date)
                    filtered_df = merged_df.loc[mask]
                    
                    if not filtered_df.empty:
                        all_stocks_data.append(filtered_df)
                        
                    processed_count += 1
                    if processed_count % 50 == 0:
                        self.log(f"已处理 {processed_count}/{len(stock_list)} 个股票...")
                        
                except Exception as e:
                    self.log(f"错误: 处理 {stock_code_with_ext} 时出错: {e}")
                    
            if not all_stocks_data:
                self.log("错误: 未能提取任何数据。请检查日期范围和源文件。")
                messagebox.showwarning("无数据", "未能在指定日期范围内提取任何有效数据。")
                self.set_ui_state('normal')
                return
                
            self.log("所有股票数据处理完毕，正在合并为一个文件...")
            final_df = pd.concat(all_stocks_data, ignore_index=True)
            final_df.sort_values(by=['date', 'code'], inplace=True)
            
            # Save file
            output_filename = f"{mode}_data_{start_date_str}_to_{end_date_str}.pkl"
            output_filepath = os.path.join(self.output_path, output_filename)
            final_df.to_pickle(output_filepath)
            
            self.log("="*50)
            self.log(f"成功！ {mode} 数据集已生成。")
            self.log(f"总计包含 {len(final_df)} 条记录, {len(final_df['code'].unique())} 个股票。")
            self.log(f"包含因子: {list(final_df.columns)}")
            self.log(f"文件已保存至: {output_filepath}")
            self.log("="*50)
            messagebox.showinfo("成功", f"{mode.capitalize()} 数据集已成功生成！\n文件保存在:\n{output_filepath}")
            
        except Exception as e:
            self.log(f"发生严重错误: {e}")
            messagebox.showerror("严重错误", f"处理过程中发生严重错误: {e}")
        finally:
            self.set_ui_state('normal')
            
    def extract_data_pair(self, train_start_date_str, train_end_date_str, test_start_date_str, test_end_date_str):
        """Extract training and testing data pair with unique identifier"""
        self.set_ui_state('disabled')
        try:
            # Generate unique pair ID based on timestamp
            pair_id = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.log(f"开始生成数据对 (ID: {pair_id})...")
            self.log(f"训练数据时间范围: {train_start_date_str} to {train_end_date_str}")
            self.log(f"测试数据时间范围: {test_start_date_str} to {test_end_date_str}")
            
            # Check if any factors are selected
            total_selected = len(self.selected_price_factors) + len(self.selected_individual_factors) + len(self.selected_ranking_factors)
            if total_selected == 0:
                self.log("错误: 请至少选择一个因子。")
                messagebox.showerror("选择错误", "请至少选择一个因子。")
                self.set_ui_state('normal')
                return
                
            self.log(f"已选择因子数量: 原始股票数据({len(self.selected_price_factors)}), 个股因子({len(self.selected_individual_factors)}), 排序因子({len(self.selected_ranking_factors)})")
            
            # Validate dates
            try:
                train_start_date = datetime.strptime(train_start_date_str, '%Y-%m-%d')
                train_end_date = datetime.strptime(train_end_date_str, '%Y-%m-%d')
                test_start_date = datetime.strptime(test_start_date_str, '%Y-%m-%d')
                test_end_date = datetime.strptime(test_end_date_str, '%Y-%m-%d')
            except ValueError:
                self.log("错误: 日期格式不正确，请使用 YYYY-MM-DD 格式。")
                messagebox.showerror("格式错误", "日期格式不正确，请使用 YYYY-MM-DD 格式。")
                self.set_ui_state('normal')
                return
                
            # Generate training data
            self.log("正在生成训练数据...")
            train_data = self._extract_data_internal(train_start_date, train_end_date)
            if train_data is None:
                self.set_ui_state('normal')
                return
                
            # Generate testing data
            self.log("正在生成测试数据...")
            test_data = self._extract_data_internal(test_start_date, test_end_date)
            if test_data is None:
                self.set_ui_state('normal')
                return
                
            # Save files with pair ID
            train_filename = f"train_data_{pair_id}_{train_start_date_str}_to_{train_end_date_str}.pkl"
            test_filename = f"test_data_{pair_id}_{test_start_date_str}_to_{test_end_date_str}.pkl"
            
            train_filepath = os.path.join(self.output_path, train_filename)
            test_filepath = os.path.join(self.output_path, test_filename)
            
            train_data.to_pickle(train_filepath)
            test_data.to_pickle(test_filepath)
            
            # Save pair metadata
            pair_metadata = {
                'pair_id': pair_id,
                'train_file': train_filename,
                'test_file': test_filename,
                'train_date_range': f"{train_start_date_str} to {train_end_date_str}",
                'test_date_range': f"{test_start_date_str} to {test_end_date_str}",
                'selected_factors': {
                    'price_factors': list(self.selected_price_factors),
                    'individual_factors': list(self.selected_individual_factors),
                    'ranking_factors': list(self.selected_ranking_factors)
                },
                'train_records': len(train_data),
                'test_records': len(test_data),
                'train_stocks': len(train_data['code'].unique()),
                'test_stocks': len(test_data['code'].unique()),
                'generated_time': datetime.now().isoformat()
            }
            
            metadata_filename = f"pair_metadata_{pair_id}.pkl"
            metadata_filepath = os.path.join(self.output_path, metadata_filename)
            with open(metadata_filepath, 'wb') as f:
                pickle.dump(pair_metadata, f)
            
            self.log("="*50)
            self.log(f"成功！数据对 (ID: {pair_id}) 已生成。")
            self.log(f"训练数据: {len(train_data)} 条记录, {len(train_data['code'].unique())} 个股票")
            self.log(f"测试数据: {len(test_data)} 条记录, {len(test_data['code'].unique())} 个股票")
            self.log(f"包含因子: {list(train_data.columns)}")
            self.log(f"训练数据文件: {train_filepath}")
            self.log(f"测试数据文件: {test_filepath}")
            self.log(f"元数据文件: {metadata_filepath}")
            self.log("="*50)
            messagebox.showinfo("成功", f"数据对已成功生成！\n数据对ID: {pair_id}\n训练数据: {train_filename}\n测试数据: {test_filename}")
            
        except Exception as e:
            self.log(f"发生严重错误: {e}")
            messagebox.showerror("严重错误", f"处理过程中发生严重错误: {e}")
        finally:
            self.set_ui_state('normal')
            
    def _extract_data_internal(self, start_date, end_date):
        """Internal method to extract data for a given date range"""
        # Get stock list
        stock_list = glob.glob(os.path.join(self.source_price_path, '*.csv'))
        if not stock_list:
            self.log(f"错误: 在 {self.source_price_path} 中未找到任何原始股票数据 (.csv 文件)。")
            messagebox.showerror("文件未找到", f"在 {self.source_price_path} 中未找到任何原始股票数据 (.csv 文件)。")
            return None
            
        self.log(f"发现 {len(stock_list)} 个股票文件。开始处理...")
        
        all_stocks_data = []
        processed_count = 0
        
        for stock_csv_path in stock_list:
            stock_code_with_ext = os.path.basename(stock_csv_path).replace('_hfq.csv', '')
            
            factor_pkl_path = os.path.join(self.source_factor_path, f"{stock_code_with_ext}_factors.pkl")
            ranking_pkl_path = os.path.join(self.source_ranking_factor_path, f"{stock_code_with_ext}_ranking_factors.pkl")
            
            # Check if required files exist based on selected factors
            files_needed = []
            if self.selected_price_factors:
                files_needed.append(('price', stock_csv_path))
            if self.selected_individual_factors:
                files_needed.append(('individual', factor_pkl_path))
            if self.selected_ranking_factors:
                files_needed.append(('ranking', ranking_pkl_path))
                
            missing_files = []
            for file_type, file_path in files_needed:
                if not os.path.exists(file_path):
                    missing_files.append(file_type)
                    
            if missing_files:
                continue
                
            try:
                # Load and process data
                dataframes = []
                
                # Load price data if selected
                if self.selected_price_factors:
                    price_df = pd.read_csv(stock_csv_path)
                    price_df['date'] = pd.to_datetime(price_df['date'])
                    if 'code' in price_df.columns:
                        price_df['code'] = price_df['code'].astype(str)
                    # Select only required columns plus date and code
                    price_cols = ['date', 'code'] + [col for col in self.selected_price_factors if col in price_df.columns]
                    price_df = price_df[price_cols]
                    dataframes.append(price_df)
                    
                # Load individual factor data if selected
                if self.selected_individual_factors:
                    factor_df = pd.read_pickle(factor_pkl_path)
                    # Check if 'date' is in index, if so, reset_index
                    if factor_df.index.name == 'date':
                        factor_df = factor_df.reset_index()
                    elif isinstance(factor_df.index, pd.DatetimeIndex):
                        # 如果索引是日期但没有名称，也需要重置
                        factor_df = factor_df.reset_index()
                        factor_df.rename(columns={'index': 'date'}, inplace=True)
                    
                    # 处理列名大小写问题
                    if 'Date' in factor_df.columns and 'date' not in factor_df.columns:
                        factor_df.rename(columns={'Date': 'date'}, inplace=True)
                    
                    if 'date' in factor_df.columns:
                        factor_df['date'] = pd.to_datetime(factor_df['date'])
                        factor_df['code'] = stock_code_with_ext
                        # Select only required columns plus date and code
                        factor_cols = ['date', 'code'] + [col for col in self.selected_individual_factors if col in factor_df.columns]
                        factor_df = factor_df[factor_cols]
                        dataframes.append(factor_df)
                    else:
                        continue
                    
                # Load ranking factor data if selected
                if self.selected_ranking_factors:
                    ranking_df = pd.read_pickle(ranking_pkl_path)
                    # Check if 'date' is in index, if so, reset_index
                    if ranking_df.index.name == 'date':
                        ranking_df = ranking_df.reset_index()
                    elif isinstance(ranking_df.index, pd.DatetimeIndex):
                        # 如果索引是日期但没有名称，也需要重置
                        ranking_df = ranking_df.reset_index()
                        ranking_df.rename(columns={'index': 'date'}, inplace=True)
                    
                    # 处理列名大小写问题
                    if 'Date' in ranking_df.columns and 'date' not in ranking_df.columns:
                        ranking_df.rename(columns={'Date': 'date'}, inplace=True)
                    
                    if 'date' in ranking_df.columns:
                        ranking_df['date'] = pd.to_datetime(ranking_df['date'])
                        ranking_df['code'] = stock_code_with_ext
                        # Select only required columns plus date and code
                        ranking_cols = ['date', 'code'] + [col for col in self.selected_ranking_factors if col in ranking_df.columns]
                        ranking_df = ranking_df[ranking_cols]
                        dataframes.append(ranking_df)
                    else:
                        continue
                    
                # Merge dataframes
                if len(dataframes) == 1:
                    merged_df = dataframes[0]
                else:
                    merged_df = dataframes[0]
                    for df in dataframes[1:]:
                        merged_df = pd.merge(merged_df, df, on=['date', 'code'], how='inner')
                        
                # Filter by date
                mask = (merged_df['date'] >= start_date) & (merged_df['date'] <= end_date)
                filtered_df = merged_df.loc[mask]
                
                if not filtered_df.empty:
                    all_stocks_data.append(filtered_df)
                    
                processed_count += 1
                if processed_count % 50 == 0:
                    self.log(f"已处理 {processed_count}/{len(stock_list)} 个股票...")
                    
            except Exception as e:
                self.log(f"错误: 处理 {stock_code_with_ext} 时出错: {e}")
                
        if not all_stocks_data:
            self.log("错误: 未能提取任何数据。请检查日期范围和源文件。")
            messagebox.showwarning("无数据", "未能在指定日期范围内提取任何有效数据。")
            return None
            
        self.log("所有股票数据处理完毕，正在合并为一个文件...")
        final_df = pd.concat(all_stocks_data, ignore_index=True)
        final_df.sort_values(by=['date', 'code'], inplace=True)
        
        return final_df
            

if __name__ == "__main__":
    root = tk.Tk()
    gui = FactorSelectionGUI(root)
    root.mainloop()