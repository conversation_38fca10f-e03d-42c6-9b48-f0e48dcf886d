# SAC强化学习股票交易训练系统

这是一个基于SAC（Soft Actor-Critic）算法的股票交易强化学习训练系统，具有图形化用户界面，支持GPU加速训练。

## 功能特性

- **数据导入**: 从CSV文件导入沪深300股票的历史K线数据
- **因子数据**: 支持加载pickle格式的因子数据文件
- **SAC算法**: 实现了完整的SAC强化学习算法，集成GRU网络处理时序数据
- **交易环境**: 模拟真实的股票交易环境，支持最多10只股票的投资组合
- **GPU加速**: 针对RTX3060显卡优化，最大化GPU利用率
- **实时监控**: 训练过程中实时显示损失函数、收益率、夏普比率等关键指标
- **模型保存**: 自动保存训练好的模型和统计数据
- **日志系统**: 完整的日志记录和文件保存功能

## 系统要求

- Python 3.8+
- CUDA 11.0+ (GPU训练)
- 内存: 8GB+ (推荐16GB+)
- 显存: 6GB+ (RTX3060或更高)

## 安装依赖

```bash
pip install -r requirements.txt
```

### GPU支持

如果要使用GPU训练，请安装对应CUDA版本的PyTorch：

```bash
# CUDA 11.8
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118

# CUDA 12.1
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu121
```

## 使用方法

### 1. 启动程序

```bash
python sac_training_gui.py
```

### 2. 数据准备

1. **股票数据**: 点击"加载股票数据"按钮，系统会自动从`../../0-数据汇总/1-原始股票数据`目录加载所有CSV文件
2. **因子数据**: 点击"选择因子文件"按钮，手动选择`../../0-数据汇总/4-模型训练数据`目录中的pkl文件

### 3. 训练参数设置

- **起始日期**: 设置训练数据的起始日期
- **训练轮数**: 设置训练的episode数量（建议1000-5000）
- **最大持仓数**: 设置最多可持有的股票数量（1-10）
- **使用GPU**: 勾选以启用GPU加速训练

### 4. 开始训练

1. 确保数据已正确加载
2. 设置合适的训练参数
3. 点击"开始训练"按钮
4. 观察实时训练进度和性能指标

### 5. 模型管理

- **保存模型**: 训练完成后点击"保存模型"，模型将保存到`../../0-数据汇总/5-训练模型`目录
- **加载模型**: 点击"加载模型"可以加载之前训练的模型

## 文件结构

```
2-模型训练/
├── sac_training_gui.py      # 主GUI程序
├── sac_model.py             # SAC模型实现
├── trading_environment.py   # 交易环境
├── trainer.py               # 训练器和GPU优化
├── requirements.txt         # 依赖包列表
└── README.md               # 说明文档
```

## 输出文件

- **模型文件**: `../../0-数据汇总/5-训练模型/sac_model_YYYYMMDD_HHMMSS.pth`
- **训练统计**: `../../0-数据汇总/5-训练模型/sac_training_stats_YYYYMMDD_HHMMSS.pkl`
- **日志文件**: `../../0-数据汇总/9-日志文件/sac_training_YYYYMMDD_HHMMSS.log`

## 性能优化

### GPU优化
- 自动检测并优化RTX3060显卡设置
- 动态调整批处理大小以最大化GPU利用率
- 混合精度训练支持

### 内存优化
- 经验回放缓冲区大小自动调整
- 梯度累积减少内存占用
- 数据预处理优化

## 故障排除

### 常见问题

1. **CUDA内存不足**
   - 减少批处理大小
   - 降低经验回放缓冲区大小
   - 关闭其他GPU程序

2. **数据加载失败**
   - 检查CSV文件格式是否正确
   - 确保包含必要的列：date, open, high, low, close, volume
   - 检查文件编码是否为UTF-8

3. **训练速度慢**
   - 确保使用GPU训练
   - 检查数据预处理是否优化
   - 调整网络结构参数

### 日志分析

查看日志文件可以帮助诊断问题：
- 训练进度和性能指标
- 错误信息和警告
- GPU使用情况
- 内存使用统计

## 技术细节

### SAC算法
- Actor-Critic架构
- 自动熵调节
- 双Q网络减少过估计
- 经验回放机制

### GRU特征提取
- 处理时序因子数据
- 自适应序列长度
- 批量归一化

### 交易环境
- 真实的交易成本模拟
- 风险管理机制
- 多目标奖励函数

## 联系支持

如有问题或建议，请查看日志文件或联系开发团队。