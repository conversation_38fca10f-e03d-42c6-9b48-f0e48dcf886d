import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import logging
from dataclasses import dataclass
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

@dataclass
class Position:
    """持仓信息"""
    stock_code: str
    shares: float
    avg_price: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    weight: float

@dataclass
class TradeRecord:
    """交易记录"""
    date: str
    stock_code: str
    action: str  # 'buy' or 'sell'
    shares: float
    price: float
    amount: float
    commission: float

class TradingEnvironment:
    """股票交易环境"""
    
    def __init__(
        self,
        stock_data: Dict[str, pd.DataFrame],
        factor_data: Any,
        initial_capital: float = 1000000.0,
        max_positions: int = 10,
        commission_rate: float = 0.0003,
        min_commission: float = 5.0,
        sequence_length: int = 20,
        start_date: str = '2020-01-01',
        end_date: Optional[str] = None
    ):
        self.stock_data = stock_data
        self.factor_data = factor_data
        self.initial_capital = initial_capital
        self.max_positions = max_positions
        self.commission_rate = commission_rate
        self.min_commission = min_commission
        self.sequence_length = sequence_length
        
        # 日志 - 需要在其他方法调用前初始化
        self.logger = logging.getLogger(__name__)
        
        # 准备数据
        self.prepare_data(start_date, end_date)
        
        # 交易状态
        self.reset()
        
    def prepare_data(self, start_date: str, end_date: Optional[str] = None):
        """准备交易数据"""
        # 获取所有股票的交易日期
        all_dates = set()
        valid_stocks = []
        
        for stock_code, df in self.stock_data.items():
            if 'date' in df.columns and len(df) > self.sequence_length:
                df['date'] = pd.to_datetime(df['date'])
                df = df.sort_values('date')
                
                # 过滤日期范围
                mask = df['date'] >= start_date
                if end_date:
                    mask &= df['date'] <= end_date
                    
                df_filtered = df[mask].copy()
                
                if len(df_filtered) > self.sequence_length:
                    self.stock_data[stock_code] = df_filtered.reset_index(drop=True)
                    all_dates.update(df_filtered['date'].dt.strftime('%Y-%m-%d').tolist())
                    valid_stocks.append(stock_code)
                    
        # 只保留有效股票
        self.stock_codes = valid_stocks[:300]  # 限制为300只股票
        self.trading_dates = sorted(list(all_dates))
        
        # 创建价格矩阵
        self.create_price_matrix()
        
        # 处理因子数据
        self.process_factor_data()
        
        self.logger.info(f"准备数据完成: {len(self.stock_codes)}只股票, {len(self.trading_dates)}个交易日")
        
    def create_price_matrix(self):
        """创建价格矩阵"""
        self.price_matrix = {}
        self.return_matrix = {}
        
        for stock_code in self.stock_codes:
            df = self.stock_data[stock_code]
            
            # 创建日期到价格的映射
            date_price_map = dict(zip(
                df['date'].dt.strftime('%Y-%m-%d'),
                df['close'] if 'close' in df.columns else df['收盘价']
            ))
            
            # 填充价格矩阵
            prices = []
            for date in self.trading_dates:
                if date in date_price_map:
                    prices.append(date_price_map[date])
                else:
                    # 使用前一个有效价格
                    prices.append(prices[-1] if prices else np.nan)
                    
            self.price_matrix[stock_code] = np.array(prices)
            
            # 计算收益率
            returns = np.diff(self.price_matrix[stock_code]) / self.price_matrix[stock_code][:-1]
            self.return_matrix[stock_code] = np.concatenate([[0], returns])  # 第一天收益率为0
            
    def process_factor_data(self):
        """处理因子数据"""
        # 这里需要根据实际的因子数据格式进行处理
        # 假设因子数据是一个字典，键为股票代码，值为因子矩阵
        if isinstance(self.factor_data, dict):
            self.factor_matrix = {}
            for stock_code in self.stock_codes:
                if stock_code in self.factor_data:
                    self.factor_matrix[stock_code] = self.factor_data[stock_code]
                else:
                    # 如果没有因子数据，使用零矩阵
                    self.factor_matrix[stock_code] = np.zeros((len(self.trading_dates), 10))
        else:
            # 如果因子数据格式不对，使用随机数据作为占位符
            self.factor_matrix = {}
            for stock_code in self.stock_codes:
                self.factor_matrix[stock_code] = np.random.randn(len(self.trading_dates), 10)
                
    def reset(self) -> np.ndarray:
        """重置环境"""
        self.current_step = self.sequence_length  # 从有足够历史数据的位置开始
        self.cash = self.initial_capital
        self.positions = {}  # {stock_code: Position}
        self.portfolio_value = self.initial_capital
        self.trade_records = []
        
        # 性能指标
        self.portfolio_values = [self.initial_capital]
        self.returns = [0.0]
        self.max_drawdown = 0.0
        self.peak_value = self.initial_capital
        
        return self.get_state()
    
    def get_state(self) -> np.ndarray:
        """获取当前状态（包含因子数据）"""
        if self.current_step >= len(self.trading_dates):
            return np.zeros(self.get_state_dim())

        # 1. 基础市场状态特征
        market_features = []

        # 当前持仓信息
        position_features = np.zeros(self.max_positions * 3)  # 每个位置3个特征：权重、收益率、持有天数
        pos_idx = 0
        for stock_code, position in self.positions.items():
            if pos_idx < self.max_positions:
                position_features[pos_idx * 3] = position.weight
                position_features[pos_idx * 3 + 1] = position.unrealized_pnl / position.market_value if position.market_value > 0 else 0
                position_features[pos_idx * 3 + 2] = 1.0  # 简化的持有天数特征
                pos_idx += 1

        # 资金使用率
        cash_ratio = self.cash / self.portfolio_value if self.portfolio_value > 0 else 1.0

        # 组合收益率特征
        recent_returns = self.returns[-5:] if len(self.returns) >= 5 else [0] * 5
        recent_returns = recent_returns + [0] * (5 - len(recent_returns))

        # 市场整体特征（使用前10只股票的平均收益率作为市场指标）
        market_return = 0.0
        if self.current_step > 0:
            sample_stocks = self.stock_codes[:10]
            returns = []
            for stock_code in sample_stocks:
                if stock_code in self.return_matrix:
                    returns.append(self.return_matrix[stock_code][self.current_step])
            if returns:
                market_return = np.mean(returns)

        market_features = [
            cash_ratio,
            len(self.positions) / self.max_positions,  # 持仓占用率
            market_return,
            self.max_drawdown,
            self.portfolio_value / self.initial_capital - 1  # 总收益率
        ]

        # 2. 因子数据特征
        factor_features = self._get_factor_features()

        # 3. 持仓股票因子特征
        position_factor_features = self._get_position_factor_features()

        # 4. 市场因子统计特征
        market_factor_features = self._get_market_factor_features()

        # 组合所有特征
        state = np.concatenate([
            market_features,
            position_features,
            recent_returns,
            factor_features,
            position_factor_features,
            market_factor_features
        ])

        return state.astype(np.float32)

    def _get_factor_features(self) -> np.ndarray:
        """获取当前时刻的因子特征"""
        if not self.factor_matrix or self.current_step >= len(self.trading_dates):
            return np.zeros(self.get_factor_dim())

        # 获取当前时刻的因子数据（使用第一只股票作为代表）
        if self.stock_codes and self.stock_codes[0] in self.factor_matrix:
            stock_code = self.stock_codes[0]
            factor_matrix = self.factor_matrix[stock_code]

            if self.current_step < len(factor_matrix):
                current_factors = factor_matrix[self.current_step]
                # 确保返回正确维度
                if len(current_factors) >= self.get_factor_dim():
                    return current_factors[:self.get_factor_dim()].astype(np.float32)
                else:
                    # 如果因子数量不足，用0填充
                    padded_factors = np.zeros(self.get_factor_dim())
                    padded_factors[:len(current_factors)] = current_factors
                    return padded_factors.astype(np.float32)

        return np.zeros(self.get_factor_dim())

    def _get_position_factor_features(self) -> np.ndarray:
        """获取持仓股票的因子特征"""
        if not self.positions or not self.factor_matrix:
            return np.zeros(self.get_factor_dim())

        # 计算持仓股票的加权平均因子
        weighted_factors = np.zeros(self.get_factor_dim())
        total_weight = 0.0

        for stock_code, position in self.positions.items():
            if stock_code in self.factor_matrix and position.weight > 0:
                factor_matrix = self.factor_matrix[stock_code]
                if self.current_step < len(factor_matrix):
                    stock_factors = factor_matrix[self.current_step]
                    # 确保因子维度匹配
                    factor_dim = min(len(stock_factors), self.get_factor_dim())
                    weighted_factors[:factor_dim] += stock_factors[:factor_dim] * position.weight
                    total_weight += position.weight

        # 归一化
        if total_weight > 0:
            weighted_factors /= total_weight

        return weighted_factors.astype(np.float32)

    def _get_market_factor_features(self) -> np.ndarray:
        """获取市场因子统计特征"""
        if not self.factor_matrix or self.current_step >= len(self.trading_dates):
            # 返回市场因子统计特征：均值、标准差、最大值、最小值
            return np.zeros(self.get_factor_dim() * 4)

        # 收集当前时刻所有股票的因子数据
        all_factors = []
        for stock_code in self.stock_codes[:20]:  # 限制为前20只股票以提高效率
            if stock_code in self.factor_matrix:
                factor_matrix = self.factor_matrix[stock_code]
                if self.current_step < len(factor_matrix):
                    all_factors.append(factor_matrix[self.current_step])

        if not all_factors:
            return np.zeros(self.get_factor_dim() * 4)

        # 转换为numpy数组
        factors_array = np.array(all_factors)
        factor_dim = min(factors_array.shape[1], self.get_factor_dim())

        # 计算统计特征
        factor_mean = np.mean(factors_array[:, :factor_dim], axis=0)
        factor_std = np.std(factors_array[:, :factor_dim], axis=0)
        factor_max = np.max(factors_array[:, :factor_dim], axis=0)
        factor_min = np.min(factors_array[:, :factor_dim], axis=0)

        # 组合统计特征
        market_stats = np.concatenate([factor_mean, factor_std, factor_max, factor_min])

        # 确保返回正确维度
        expected_dim = self.get_factor_dim() * 4
        if len(market_stats) >= expected_dim:
            return market_stats[:expected_dim].astype(np.float32)
        else:
            padded_stats = np.zeros(expected_dim)
            padded_stats[:len(market_stats)] = market_stats
            return padded_stats.astype(np.float32)

    def get_factor_sequence(self, stock_code: str) -> np.ndarray:
        """获取指定股票的因子序列"""
        if stock_code not in self.factor_matrix:
            return np.zeros((self.sequence_length, 10))
            
        start_idx = max(0, self.current_step - self.sequence_length)
        end_idx = self.current_step
        
        if end_idx > len(self.factor_matrix[stock_code]):
            # 如果超出范围，用最后的数据填充
            sequence = self.factor_matrix[stock_code][-self.sequence_length:]
        else:
            sequence = self.factor_matrix[stock_code][start_idx:end_idx]
            
        # 确保序列长度正确
        if len(sequence) < self.sequence_length:
            padding = np.zeros((self.sequence_length - len(sequence), sequence.shape[1]))
            sequence = np.vstack([padding, sequence])
            
        return sequence.astype(np.float32)
    
    def step(self, actions: np.ndarray) -> Tuple[np.ndarray, float, bool, Dict]:
        """执行一步交易"""
        if self.current_step >= len(self.trading_dates) - 1:
            return self.get_state(), 0.0, True, {}
            
        # 解析动作
        # actions是一个向量，表示对每只股票的目标权重
        target_weights = self.parse_actions(actions)
        
        # 执行交易
        self.execute_trades(target_weights)
        
        # 更新到下一个交易日
        self.current_step += 1
        
        # 更新持仓价值
        self.update_portfolio_value()
        
        # 计算奖励
        reward = self.calculate_reward()
        
        # 更新性能指标
        self.update_performance_metrics()
        
        # 检查是否结束
        done = self.current_step >= len(self.trading_dates) - 1
        
        # 信息字典
        info = {
            'portfolio_value': self.portfolio_value,
            'cash': self.cash,
            'positions': len(self.positions),
            'daily_return': self.returns[-1] if self.returns else 0.0
        }
        
        return self.get_state(), reward, done, info
    
    def parse_actions(self, actions: np.ndarray) -> Dict[str, float]:
        """解析动作为目标权重"""
        # 动作向量的长度应该等于股票数量
        if len(actions) != len(self.stock_codes):
            # 如果长度不匹配，截断或填充
            if len(actions) > len(self.stock_codes):
                actions = actions[:len(self.stock_codes)]
            else:
                actions = np.pad(actions, (0, len(self.stock_codes) - len(actions)))
                
        # 应用softmax获得权重分布
        exp_actions = np.exp(actions - np.max(actions))  # 数值稳定性
        weights = exp_actions / np.sum(exp_actions)
        
        # 只选择权重最大的max_positions只股票
        top_indices = np.argsort(weights)[-self.max_positions:]
        
        target_weights = {}
        total_weight = 0.0

        # 直接选择权重最大的top_indices作为目标
        for idx in top_indices:
            stock_code = self.stock_codes[idx]
            target_weights[stock_code] = weights[idx]
            total_weight += weights[idx]
                
        # 归一化权重
        if total_weight > 0:
            for stock_code in target_weights:
                target_weights[stock_code] /= total_weight
                
        return target_weights
    
    def execute_trades(self, target_weights: Dict[str, float]):
        """执行交易"""
        current_date = self.trading_dates[self.current_step]
        
        # 计算当前持仓权重
        current_weights = {}
        for stock_code, position in self.positions.items():
            current_weights[stock_code] = position.market_value / self.portfolio_value if self.portfolio_value > 0 else 0
            
        # 计算需要调整的权重
        all_stocks = set(list(current_weights.keys()) + list(target_weights.keys()))
        
        for stock_code in all_stocks:
            current_weight = current_weights.get(stock_code, 0.0)
            target_weight = target_weights.get(stock_code, 0.0)
            weight_diff = target_weight - current_weight
            
            if abs(weight_diff) > 0.01:  # 权重变化阈值
                self.trade_stock(stock_code, weight_diff, current_date)
                
    def trade_stock(self, stock_code: str, weight_diff: float, date: str):
        """交易单只股票"""
        if stock_code not in self.price_matrix:
            return
            
        current_price = self.price_matrix[stock_code][self.current_step]
        if np.isnan(current_price) or current_price <= 0:
            return
            
        # 计算交易金额
        trade_amount = weight_diff * self.portfolio_value
        
        if weight_diff > 0:  # 买入
            # 检查现金是否足够
            available_cash = self.cash * 0.95  # 保留5%现金缓冲
            trade_amount = min(trade_amount, available_cash)
            
            if trade_amount > 1000:  # 最小交易金额
                shares = int(trade_amount / current_price / 100) * 100  # 整手交易
                if shares > 0:
                    actual_amount = shares * current_price
                    commission = max(actual_amount * self.commission_rate, self.min_commission)
                    total_cost = actual_amount + commission
                    
                    if total_cost <= self.cash:
                        # 执行买入
                        if stock_code in self.positions:
                            # 加仓
                            position = self.positions[stock_code]
                            total_shares = position.shares + shares
                            total_cost_basis = position.shares * position.avg_price + actual_amount
                            new_avg_price = total_cost_basis / total_shares
                            
                            position.shares = total_shares
                            position.avg_price = new_avg_price
                        else:
                            # 新建仓位
                            self.positions[stock_code] = Position(
                                stock_code=stock_code,
                                shares=shares,
                                avg_price=current_price,
                                current_price=current_price,
                                market_value=actual_amount,
                                unrealized_pnl=0.0,
                                weight=0.0
                            )
                            
                        self.cash -= total_cost
                        
                        # 记录交易
                        self.trade_records.append(TradeRecord(
                            date=date,
                            stock_code=stock_code,
                            action='buy',
                            shares=shares,
                            price=current_price,
                            amount=actual_amount,
                            commission=commission
                        ))
                        
        else:  # 卖出
            if stock_code in self.positions:
                position = self.positions[stock_code]
                # 计算需要卖出的股份
                target_amount = abs(trade_amount)
                shares_to_sell = min(int(target_amount / current_price / 100) * 100, position.shares)
                
                if shares_to_sell > 0:
                    actual_amount = shares_to_sell * current_price
                    commission = max(actual_amount * self.commission_rate, self.min_commission)
                    net_proceeds = actual_amount - commission
                    
                    # 执行卖出
                    position.shares -= shares_to_sell
                    self.cash += net_proceeds
                    
                    # 记录交易
                    self.trade_records.append(TradeRecord(
                        date=date,
                        stock_code=stock_code,
                        action='sell',
                        shares=shares_to_sell,
                        price=current_price,
                        amount=actual_amount,
                        commission=commission
                    ))
                    
                    # 如果全部卖出，删除持仓
                    if position.shares <= 0:
                        del self.positions[stock_code]
                        
    def update_portfolio_value(self):
        """更新组合价值"""
        total_market_value = self.cash
        
        for stock_code, position in self.positions.items():
            if stock_code in self.price_matrix:
                current_price = self.price_matrix[stock_code][self.current_step]
                if not np.isnan(current_price) and current_price > 0:
                    position.current_price = current_price
                    position.market_value = position.shares * current_price
                    position.unrealized_pnl = position.market_value - position.shares * position.avg_price
                    total_market_value += position.market_value
                    
        self.portfolio_value = total_market_value
        
        # 更新持仓权重
        for position in self.positions.values():
            position.weight = position.market_value / self.portfolio_value if self.portfolio_value > 0 else 0
            
    def calculate_reward(self) -> float:
        """计算奖励函数"""
        # 确保portfolio_value是有效数值
        if np.isnan(self.portfolio_value) or self.portfolio_value <= 0:
            self.logger.warning(f"Invalid portfolio value: {self.portfolio_value}")
            return 0.0

        if len(self.portfolio_values) < 2:
            # 初期没有历史数据时，基于当前组合价值与初始资本的比较
            if self.initial_capital > 0:
                daily_return = (self.portfolio_value - self.initial_capital) / self.initial_capital
            else:
                daily_return = 0.0
        else:
            # 当日收益率
            prev_value = self.portfolio_values[-1]
            if prev_value > 0:
                daily_return = (self.portfolio_value - prev_value) / prev_value
            else:
                daily_return = 0.0

        # 基础收益奖励 - 使用更合理的缩放
        return_reward = daily_return * 10  # 适度放大收益率

        # 风险调整奖励
        risk_penalty = 0.0
        if len(self.returns) >= 20:
            recent_returns = np.array(self.returns[-20:])
            volatility = np.std(recent_returns)
            if volatility > 0:
                sharpe_ratio = np.mean(recent_returns) / volatility
                risk_penalty = -abs(sharpe_ratio - 1.5) * 0.01  # 减小惩罚力度

        # 回撤惩罚
        drawdown_penalty = 0.0
        if self.max_drawdown > 0.05:  # 回撤超过5%
            drawdown_penalty = -self.max_drawdown * 1.0  # 减小惩罚力度

        # 持仓分散度奖励
        diversification_reward = 0.0
        if len(self.positions) > 1:
            weights = [pos.weight for pos in self.positions.values()]
            # 计算权重的基尼系数，越分散越好
            weights_array = np.array(weights)
            total_weight = np.sum(weights_array)
            if total_weight > 0:
                weights_array = weights_array / total_weight
                gini = 1 - np.sum(weights_array ** 2)
                diversification_reward = gini * 0.1  # 减小奖励力度

        # 换手率惩罚
        turnover_penalty = 0.0
        if len(self.trade_records) > 0 and self.current_step < len(self.trading_dates):
            current_date = self.trading_dates[self.current_step]
            recent_trades = [trade for trade in self.trade_records if trade.date == current_date]
            if recent_trades and self.portfolio_value > 0:
                daily_turnover = sum(trade.amount for trade in recent_trades) / self.portfolio_value
                if daily_turnover > 0.1:  # 日换手率超过10%
                    turnover_penalty = -daily_turnover * 0.5  # 减小惩罚力度

        # 总奖励
        total_reward = (
            return_reward +
            risk_penalty +
            drawdown_penalty +
            diversification_reward +
            turnover_penalty
        )

        # 确保奖励是有效数值
        if np.isnan(total_reward) or np.isinf(total_reward):
            self.logger.warning(f"Invalid reward calculated: {total_reward}, using 0.0")
            total_reward = 0.0

        # 记录奖励组成（用于调试）
        if hasattr(self, '_debug_reward') and self._debug_reward:
            self.logger.debug(
                f"Reward components: return={return_reward:.6f}, risk={risk_penalty:.6f}, "
                f"drawdown={drawdown_penalty:.6f}, diversification={diversification_reward:.6f}, "
                f"turnover={turnover_penalty:.6f}, total={total_reward:.6f}"
            )

        return float(total_reward)
    
    def update_performance_metrics(self):
        """更新性能指标"""
        self.portfolio_values.append(self.portfolio_value)
        
        # 计算收益率
        if len(self.portfolio_values) >= 2:
            daily_return = (self.portfolio_values[-1] - self.portfolio_values[-2]) / self.portfolio_values[-2]
            self.returns.append(daily_return)
        else:
            self.returns.append(0.0)
            
        # 更新最大回撤
        if self.portfolio_value > self.peak_value:
            self.peak_value = self.portfolio_value
        else:
            current_drawdown = (self.peak_value - self.portfolio_value) / self.peak_value
            self.max_drawdown = max(self.max_drawdown, current_drawdown)
            
    def get_state_dim(self) -> int:
        """获取状态维度（包含因子数据）"""
        market_features = 5  # 现金比率、持仓占用率、市场收益率、最大回撤、总收益率
        position_features = self.max_positions * 3  # 每个持仓3个特征
        return_features = 5  # 最近5天收益率

        # 因子相关特征
        factor_dim = self.get_factor_dim()
        current_factor_features = factor_dim  # 当前因子特征
        position_factor_features = factor_dim  # 持仓股票因子特征
        market_factor_features = factor_dim * 4  # 市场因子统计特征（均值、标准差、最大值、最小值）

        total_dim = (market_features + position_features + return_features +
                    current_factor_features + position_factor_features + market_factor_features)

        return total_dim
    
    def get_action_dim(self) -> int:
        """获取动作维度"""
        return len(self.stock_codes)
    
    def get_factor_dim(self) -> int:
        """获取因子维度"""
        if self.factor_matrix:
            sample_stock = list(self.factor_matrix.keys())[0]
            return self.factor_matrix[sample_stock].shape[1]
        return 10  # 默认因子维度
    
    def get_performance_summary(self) -> Dict[str, float]:
        """获取性能摘要"""
        if len(self.returns) < 2:
            return {}
            
        returns_array = np.array(self.returns[1:])  # 排除第一个0收益率
        
        total_return = (self.portfolio_value - self.initial_capital) / self.initial_capital
        annual_return = (1 + total_return) ** (252 / len(returns_array)) - 1 if len(returns_array) > 0 else 0
        volatility = np.std(returns_array) * np.sqrt(252) if len(returns_array) > 1 else 0
        sharpe_ratio = annual_return / volatility if volatility > 0 else 0
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': self.max_drawdown,
            'final_portfolio_value': self.portfolio_value,
            'total_trades': len(self.trade_records)
        }