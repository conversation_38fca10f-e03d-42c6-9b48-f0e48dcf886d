#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强后的GUI界面
验证新增的参数设置和状态反馈功能
"""

import tkinter as tk
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sac_training_gui import SACTrainingGUI

def test_enhanced_gui():
    """测试增强后的GUI"""
    print("启动增强后的SAC训练GUI...")
    
    # 创建主窗口
    root = tk.Tk()
    
    # 创建GUI应用
    app = SACTrainingGUI(root)
    
    # 设置窗口关闭事件
    def on_closing():
        if app.is_training:
            app.stop_training()
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    # 启动GUI
    print("GUI已启动，新功能包括：")
    print("1. 增加了训练数据结束时间设置")
    print("2. 增加了SAC模型主要参数设置（学习率、批次大小、预热轮数等）")
    print("3. 增加了详细的实时状态反馈（奖励、各种损失、组合价值、回撤、夏普比率等）")
    print("4. 增强了训练图表显示（6个子图显示更多指标）")
    print("5. 改进了训练日志输出格式")
    
    root.mainloop()

if __name__ == "__main__":
    test_enhanced_gui()
