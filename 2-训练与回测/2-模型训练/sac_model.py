import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import random
from collections import deque
import logging
from typing import Tuple, Dict, List, Optional

class GRUFeatureExtractor(nn.Module):
    """GRU特征提取器，用于处理时序因子数据"""
    
    def __init__(self, input_size: int, hidden_size: int = 128, num_layers: int = 2, dropout: float = 0.1):
        super(GRUFeatureExtractor, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        self.gru = nn.GRU(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            batch_first=True
        )
        
        self.layer_norm = nn.LayerNorm(hidden_size)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor, hidden: Optional[torch.Tensor] = None) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor]:
        """
        Args:
            x: 输入张量 (batch_size, seq_len, input_size)
            hidden: 隐藏状态 (num_layers, batch_size, hidden_size)
        Returns:
            output: GRU输出 (batch_size, seq_len, hidden_size)
            hidden: 最终隐藏状态 (num_layers, batch_size, hidden_size)
        """
        output, hidden = self.gru(x, hidden)
        output = self.layer_norm(output)
        output = self.dropout(output)
        return output, hidden

class Actor(nn.Module):
    """SAC Actor网络"""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int = 256, max_action: float = 1.0):
        super(Actor, self).__init__()
        self.max_action = max_action
        
        # 特征提取层
        self.feature_net = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(0.1)
        )
        
        # 均值和标准差网络
        self.mean_net = nn.Linear(hidden_dim, action_dim)
        self.log_std_net = nn.Linear(hidden_dim, action_dim)
        
        # 初始化权重
        self.apply(self._init_weights)
        
    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            torch.nn.init.xavier_uniform_(m.weight)
            torch.nn.init.constant_(m.bias, 0)
            
    def forward(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        features = self.feature_net(state)
        mean = self.mean_net(features)
        log_std = self.log_std_net(features)
        log_std = torch.clamp(log_std, -20, 2)  # 限制标准差范围
        return mean, log_std
    
    def sample(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        mean, log_std = self.forward(state)
        std = log_std.exp()
        
        # 重参数化技巧
        normal = torch.distributions.Normal(mean, std)
        x_t = normal.rsample()  # 可微分采样
        
        # Tanh变换
        action = torch.tanh(x_t) * self.max_action
        
        # 计算log概率
        log_prob = normal.log_prob(x_t)
        # 修正tanh变换的雅可比行列式
        log_prob -= torch.log(self.max_action * (1 - action.pow(2) / (self.max_action ** 2)) + 1e-6)
        log_prob = log_prob.sum(1, keepdim=True)
        
        return action, log_prob

class Critic(nn.Module):
    """SAC Critic网络（双Q网络）"""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int = 256):
        super(Critic, self).__init__()
        
        # Q1网络
        self.q1 = nn.Sequential(
            nn.Linear(state_dim + action_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, 1)
        )
        
        # Q2网络
        self.q2 = nn.Sequential(
            nn.Linear(state_dim + action_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, 1)
        )
        
        # 初始化权重
        self.apply(self._init_weights)
        
    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            torch.nn.init.xavier_uniform_(m.weight)
            torch.nn.init.constant_(m.bias, 0)
            
    def forward(self, state: torch.Tensor, action: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        sa = torch.cat([state, action], dim=1)
        q1 = self.q1(sa)
        q2 = self.q2(sa)
        return q1, q2

class ReplayBuffer:
    """经验回放缓冲区"""
    
    def __init__(self, capacity: int = 100000):
        self.capacity = capacity
        self.buffer = deque(maxlen=capacity)
        
    def push(self, state, action, reward, next_state, done, factor_sequence, next_factor_sequence):
        self.buffer.append((state, action, reward, next_state, done, factor_sequence, next_factor_sequence))
        
    def sample(self, batch_size: int) -> Tuple[torch.Tensor, ...]:
        batch = random.sample(self.buffer, batch_size)
        state, action, reward, next_state, done, factor_sequence, next_factor_sequence = map(np.stack, zip(*batch))
        
        return (
            torch.FloatTensor(state),
            torch.FloatTensor(action),
            torch.FloatTensor(reward).unsqueeze(1),
            torch.FloatTensor(next_state),
            torch.FloatTensor(done).unsqueeze(1),
            torch.FloatTensor(factor_sequence),
            torch.FloatTensor(next_factor_sequence)
        )
    
    def __len__(self):
        return len(self.buffer)

class SACAgent:
    """SAC智能体"""
    
    def __init__(
        self,
        state_dim: int,
        action_dim: int,
        factor_dim: int,
        sequence_length: int = 20,
        hidden_dim: int = 256,
        lr: float = 3e-4,
        gamma: float = 0.99,
        tau: float = 0.005,
        alpha: float = 0.2,
        automatic_entropy_tuning: bool = True,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        self.device = device
        self.gamma = gamma
        self.tau = tau
        self.alpha = alpha
        self.automatic_entropy_tuning = automatic_entropy_tuning
        
        # GRU特征提取器
        self.gru_extractor = GRUFeatureExtractor(
            input_size=factor_dim,
            hidden_size=128,
            num_layers=2
        ).to(device)
        
        # 计算状态维度（价格特征 + GRU特征）
        gru_feature_dim = 128
        total_state_dim = state_dim + gru_feature_dim
        
        # Actor网络
        self.actor = Actor(total_state_dim, action_dim, hidden_dim).to(device)
        
        # Critic网络
        self.critic = Critic(total_state_dim, action_dim, hidden_dim).to(device)
        self.critic_target = Critic(total_state_dim, action_dim, hidden_dim).to(device)
        
        # 复制参数到目标网络
        self.hard_update(self.critic_target, self.critic)
        
        # 优化器
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=lr)
        self.critic_optimizer = optim.Adam(self.critic.parameters(), lr=lr)
        self.gru_optimizer = optim.Adam(self.gru_extractor.parameters(), lr=lr)
        
        # 自动熵调节
        if self.automatic_entropy_tuning:
            self.target_entropy = -torch.prod(torch.Tensor([action_dim]).to(device)).item()
            self.log_alpha = torch.zeros(1, requires_grad=True, device=device)
            self.alpha_optimizer = optim.Adam([self.log_alpha], lr=lr)
            
        # 经验回放
        self.replay_buffer = ReplayBuffer()
        
        # 日志
        self.logger = logging.getLogger(__name__)
        
    def select_action(self, state: np.ndarray, factor_sequence: np.ndarray, evaluate: bool = False) -> np.ndarray:
        """选择动作"""
        with torch.no_grad():
            # 处理因子序列
            factor_tensor = torch.FloatTensor(factor_sequence).unsqueeze(0).to(self.device)
            gru_output, _ = self.gru_extractor(factor_tensor)
            gru_features = gru_output[:, -1, :]  # 取最后一个时间步的输出
            
            # 组合状态
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            combined_state = torch.cat([state_tensor, gru_features], dim=1)
            
            if evaluate:
                # 评估模式：使用均值
                mean, _ = self.actor(combined_state)
                action = torch.tanh(mean) * self.actor.max_action
            else:
                # 训练模式：采样
                action, _ = self.actor.sample(combined_state)
                
        return action.cpu().numpy()[0]
    
    def update_parameters(self, batch_size: int = 256) -> Dict[str, float]:
        """更新网络参数"""
        if len(self.replay_buffer) < batch_size:
            return {}
            
        # 采样批次数据
        state_batch, action_batch, reward_batch, next_state_batch, done_batch, factor_batch, next_factor_batch = self.replay_buffer.sample(batch_size)
        
        state_batch = state_batch.to(self.device)
        action_batch = action_batch.to(self.device)
        reward_batch = reward_batch.to(self.device)
        next_state_batch = next_state_batch.to(self.device)
        done_batch = done_batch.to(self.device)
        factor_batch = factor_batch.to(self.device)
        next_factor_batch = next_factor_batch.to(self.device)
        
        # 处理因子序列并组合状态
        gru_features, _ = self.gru_extractor(factor_batch)
        gru_features = gru_features[:, -1, :]  # 取最后一个时间步
        combined_state_batch = torch.cat([state_batch, gru_features], dim=1)
        
        next_gru_features, _ = self.gru_extractor(next_factor_batch)
        next_gru_features = next_gru_features[:, -1, :]  # 取最后一个时间步
        combined_next_state_batch = torch.cat([next_state_batch, next_gru_features], dim=1)
        
        # 更新Critic
        with torch.no_grad():
            next_action, next_log_prob = self.actor.sample(combined_next_state_batch)
            q1_next, q2_next = self.critic_target(combined_next_state_batch, next_action)
            q_next = torch.min(q1_next, q2_next) - self.alpha * next_log_prob
            target_q = reward_batch + (1 - done_batch) * self.gamma * q_next
            
        q1, q2 = self.critic(combined_state_batch, action_batch)
        critic_loss = F.mse_loss(q1, target_q) + F.mse_loss(q2, target_q)
        
        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.critic.parameters(), 1.0)
        self.critic_optimizer.step()
        
        # 更新Actor - 重新计算combined_state_batch以避免计算图重用问题
        gru_features_actor, _ = self.gru_extractor(factor_batch)
        gru_features_actor = gru_features_actor[:, -1, :]
        combined_state_batch_actor = torch.cat([state_batch, gru_features_actor], dim=1)
        
        action_new, log_prob = self.actor.sample(combined_state_batch_actor)
        q1_new, q2_new = self.critic(combined_state_batch_actor, action_new)
        q_new = torch.min(q1_new, q2_new)
        
        actor_loss = (self.alpha * log_prob - q_new).mean()
        
        self.actor_optimizer.zero_grad()
        actor_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.actor.parameters(), 1.0)
        self.actor_optimizer.step()
        
        # 更新熵系数
        alpha_loss = 0
        if self.automatic_entropy_tuning:
            alpha_loss = -(self.log_alpha * (log_prob + self.target_entropy).detach()).mean()
            
            self.alpha_optimizer.zero_grad()
            alpha_loss.backward()
            self.alpha_optimizer.step()
            
            self.alpha = self.log_alpha.exp()
            
        # 软更新目标网络
        self.soft_update(self.critic_target, self.critic, self.tau)
        
        return {
            'critic_loss': critic_loss.item(),
            'actor_loss': actor_loss.item(),
            'alpha_loss': alpha_loss.item() if isinstance(alpha_loss, torch.Tensor) else alpha_loss,
            'alpha': self.alpha.item() if isinstance(self.alpha, torch.Tensor) else self.alpha
        }
    
    def soft_update(self, target, source, tau):
        """软更新目标网络"""
        for target_param, param in zip(target.parameters(), source.parameters()):
            target_param.data.copy_(target_param.data * (1.0 - tau) + param.data * tau)
            
    def hard_update(self, target, source):
        """硬更新目标网络"""
        for target_param, param in zip(target.parameters(), source.parameters()):
            target_param.data.copy_(param.data)
            
    def save_model(self, filepath: str):
        """保存模型"""
        torch.save({
            'actor_state_dict': self.actor.state_dict(),
            'critic_state_dict': self.critic.state_dict(),
            'critic_target_state_dict': self.critic_target.state_dict(),
            'gru_extractor_state_dict': self.gru_extractor.state_dict(),
            'actor_optimizer_state_dict': self.actor_optimizer.state_dict(),
            'critic_optimizer_state_dict': self.critic_optimizer.state_dict(),
            'gru_optimizer_state_dict': self.gru_optimizer.state_dict(),
            'log_alpha': self.log_alpha if self.automatic_entropy_tuning else None,
            'alpha_optimizer_state_dict': self.alpha_optimizer.state_dict() if self.automatic_entropy_tuning else None
        }, filepath)
        
    def load_model(self, filepath: str):
        """加载模型"""
        checkpoint = torch.load(filepath, map_location=self.device)
        
        self.actor.load_state_dict(checkpoint['actor_state_dict'])
        self.critic.load_state_dict(checkpoint['critic_state_dict'])
        self.critic_target.load_state_dict(checkpoint['critic_target_state_dict'])
        self.gru_extractor.load_state_dict(checkpoint['gru_extractor_state_dict'])
        
        self.actor_optimizer.load_state_dict(checkpoint['actor_optimizer_state_dict'])
        self.critic_optimizer.load_state_dict(checkpoint['critic_optimizer_state_dict'])
        self.gru_optimizer.load_state_dict(checkpoint['gru_optimizer_state_dict'])
        
        if self.automatic_entropy_tuning and checkpoint['log_alpha'] is not None:
            self.log_alpha = checkpoint['log_alpha']
            self.alpha_optimizer.load_state_dict(checkpoint['alpha_optimizer_state_dict'])