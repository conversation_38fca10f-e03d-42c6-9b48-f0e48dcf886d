import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import pandas as pd
import numpy as np
import os
import pickle
import threading
import datetime
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import matplotlib
# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False
import logging
from pathlib import Path
import torch

# 导入自定义模块
from sac_model import SACAgent
from trading_environment import TradingEnvironment
from trainer import SACTrainer, GPUOptimizer

class SACTrainingGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("SAC强化学习股票交易训练系统")
        self.root.geometry("1400x900")
        
        # 数据存储
        self.stock_data = {}
        self.factor_data = None
        self.sac_agent = None
        self.trading_env = None
        self.trainer = None
        self.training_thread = None
        self.is_training = False
        
        # GPU优化
        self.gpu_info = GPUOptimizer.get_gpu_info()
        if self.gpu_info['available']:
            GPUOptimizer.optimize_for_rtx3060()
        
        # 训练日志数据
        self.training_logs = {
            'episodes': [],
            'rewards': [],
            'losses': [],
            'portfolio_values': [],
            'sharpe_ratios': []
        }
        
        # 设置日志
        self.setup_logging()
        
        # 创建界面
        self.create_widgets()
        
        # 初始化图表
        self.setup_plots()
        
    def setup_logging(self):
        """设置日志系统"""
        self.log_dir = Path("../../0-数据汇总/9-日志文件")
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        self.log_file = self.log_dir / f"sac_training_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        # 创建自定义日志格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 文件处理器
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.INFO)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)
        
        # 配置日志器
        self.logger = logging.getLogger(f'SAC_Training_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}')
        self.logger.setLevel(logging.INFO)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        
        # 防止重复日志
        self.logger.propagate = False
        
        self.logger.info(f"SAC训练系统启动，日志文件: {self.log_file}")
        
        # 更新GUI中的日志文件显示
        self.root.after(100, self.update_log_file_display)
        
    def create_widgets(self):
        """创建GUI组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 右侧显示面板
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.create_control_panel(left_frame)
        self.create_display_panel(right_frame)
        
    def create_control_panel(self, parent):
        """创建控制面板"""
        # 数据导入区域
        data_frame = ttk.LabelFrame(parent, text="数据导入", padding=10)
        data_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 股票数据导入
        ttk.Label(data_frame, text="股票K线数据:").pack(anchor=tk.W)
        stock_frame = ttk.Frame(data_frame)
        stock_frame.pack(fill=tk.X, pady=(5, 10))
        
        self.stock_path_var = tk.StringVar(value="../../0-数据汇总/1-原始股票数据")
        ttk.Entry(stock_frame, textvariable=self.stock_path_var, width=30).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(stock_frame, text="浏览", command=self.browse_stock_data).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(data_frame, text="导入股票数据", command=self.load_stock_data).pack(fill=tk.X, pady=(0, 10))
        
        # 因子数据导入
        ttk.Label(data_frame, text="因子数据文件:").pack(anchor=tk.W)
        factor_frame = ttk.Frame(data_frame)
        factor_frame.pack(fill=tk.X, pady=(5, 10))
        
        self.factor_path_var = tk.StringVar()
        ttk.Entry(factor_frame, textvariable=self.factor_path_var, width=30).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(factor_frame, text="选择", command=self.browse_factor_data).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(data_frame, text="加载因子数据", command=self.load_factor_data).pack(fill=tk.X)
        
        # 训练参数区域
        param_frame = ttk.LabelFrame(parent, text="训练参数", padding=10)
        param_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 起始日期
        ttk.Label(param_frame, text="起始日期:").pack(anchor=tk.W)
        self.start_date_var = tk.StringVar(value="2020-01-01")
        ttk.Entry(param_frame, textvariable=self.start_date_var).pack(fill=tk.X, pady=(0, 10))
        
        # 训练轮数
        ttk.Label(param_frame, text="训练轮数:").pack(anchor=tk.W)
        self.episodes_var = tk.StringVar(value="1000")
        ttk.Entry(param_frame, textvariable=self.episodes_var).pack(fill=tk.X, pady=(0, 10))
        
        # 最大持仓数
        ttk.Label(param_frame, text="最大持仓数:").pack(anchor=tk.W)
        self.max_positions_var = tk.StringVar(value="10")
        ttk.Entry(param_frame, textvariable=self.max_positions_var).pack(fill=tk.X, pady=(0, 10))
        
        # GPU使用
        self.use_gpu_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(param_frame, text="使用GPU加速", variable=self.use_gpu_var).pack(anchor=tk.W)
        
        # 训练控制区域
        control_frame = ttk.LabelFrame(parent, text="训练控制", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.train_button = ttk.Button(control_frame, text="开始训练", command=self.start_training)
        self.train_button.pack(fill=tk.X, pady=(0, 5))
        
        self.stop_button = ttk.Button(control_frame, text="停止训练", command=self.stop_training, state=tk.DISABLED)
        self.stop_button.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(control_frame, text="保存模型", command=self.save_model).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(control_frame, text="加载模型", command=self.load_model).pack(fill=tk.X)
        
        # 状态显示
        status_frame = ttk.LabelFrame(parent, text="状态信息", padding=10)
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(status_frame, textvariable=self.status_var, foreground="blue").pack(anchor=tk.W)
        
        # 日志文件信息
        log_info_frame = ttk.LabelFrame(parent, text="日志信息", padding=10)
        log_info_frame.pack(fill=tk.X)
        
        log_file_frame = ttk.Frame(log_info_frame)
        log_file_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(log_file_frame, text="日志文件:").pack(side=tk.LEFT)
        self.log_file_label = ttk.Label(log_file_frame, text="未设置", foreground="blue")
        self.log_file_label.pack(side=tk.LEFT, padx=(5, 0))
        
        ttk.Button(log_file_frame, text="打开日志目录", command=self.open_log_directory).pack(side=tk.RIGHT)
        
    def create_display_panel(self, parent):
        """创建显示面板"""
        # 创建笔记本控件
        notebook = ttk.Notebook(parent)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 训练图表标签页
        chart_frame = ttk.Frame(notebook)
        notebook.add(chart_frame, text="训练图表")
        
        # 日志标签页
        log_frame = ttk.Frame(notebook)
        notebook.add(log_frame, text="训练日志")
        
        # 数据信息标签页
        info_frame = ttk.Frame(notebook)
        notebook.add(info_frame, text="数据信息")
        
        self.create_chart_panel(chart_frame)
        self.create_log_panel(log_frame)
        self.create_info_panel(info_frame)
        
    def create_chart_panel(self, parent):
        """创建图表面板"""
        # 创建matplotlib图表
        self.fig = Figure(figsize=(12, 8), dpi=100)
        
        # 创建子图
        self.ax1 = self.fig.add_subplot(2, 2, 1)
        self.ax2 = self.fig.add_subplot(2, 2, 2)
        self.ax3 = self.fig.add_subplot(2, 2, 3)
        self.ax4 = self.fig.add_subplot(2, 2, 4)
        
        self.ax1.set_title("训练奖励")
        self.ax2.set_title("损失函数")
        self.ax3.set_title("组合价值")
        self.ax4.set_title("夏普比率")
        
        self.fig.tight_layout()
        
        # 创建画布
        self.canvas = FigureCanvasTkAgg(self.fig, parent)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
    def create_log_panel(self, parent):
        """创建日志面板"""
        self.log_text = scrolledtext.ScrolledText(parent, wrap=tk.WORD, height=20)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
    def create_info_panel(self, parent):
        """创建信息面板"""
        self.info_text = scrolledtext.ScrolledText(parent, wrap=tk.WORD, height=20)
        self.info_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
    def setup_plots(self):
        """初始化图表"""
        # 初始化空图表
        for ax in [self.ax1, self.ax2, self.ax3, self.ax4]:
            ax.clear()
        self.canvas.draw()
        
    def browse_stock_data(self):
        """浏览股票数据目录"""
        directory = filedialog.askdirectory(
            title="选择股票数据目录",
            initialdir=self.stock_path_var.get()
        )
        if directory:
            self.stock_path_var.set(directory)
            
    def browse_factor_data(self):
        """浏览因子数据文件"""
        filename = filedialog.askopenfilename(
            title="选择因子数据文件",
            initialdir="../../0-数据汇总/4-模型训练数据",
            filetypes=[("Pickle files", "*.pkl"), ("All files", "*.*")]
        )
        if filename:
            self.factor_path_var.set(filename)
            
    def load_stock_data(self):
        """加载股票数据"""
        try:
            self.status_var.set("正在加载股票数据...")
            self.root.update()
            
            stock_dir = Path(self.stock_path_var.get())
            if not stock_dir.exists():
                messagebox.showerror("错误", "股票数据目录不存在")
                return
                
            csv_files = list(stock_dir.glob("*.csv"))
            
            if not csv_files:
                messagebox.showerror("错误", "目录中没有找到CSV文件")
                return
                
            self.log_text.insert(tk.END, f"开始加载股票数据，共找到 {len(csv_files)} 个文件...\n")
            self.stock_data = {}
            loaded_count = 0
            
            for i, csv_file in enumerate(csv_files):
                try:
                    # 读取CSV文件
                    df = pd.read_csv(csv_file, encoding='utf-8')
                    
                    # 标准化列名
                    df.columns = df.columns.str.lower()
                    
                    # 确保必要的列存在
                    required_columns = ['date', 'open', 'high', 'low', 'close', 'volume']
                    if not all(col in df.columns for col in required_columns):
                        self.log_text.insert(tk.END, f"跳过 {csv_file.name}: 缺少必要列\n")
                        continue
                    
                    # 转换日期格式
                    df['date'] = pd.to_datetime(df['date'])
                    
                    # 按日期排序
                    df = df.sort_values('date').reset_index(drop=True)
                    
                    # 检查数据质量
                    if len(df) < 100:  # 至少需要100个交易日的数据
                        self.log_text.insert(tk.END, f"跳过 {csv_file.name}: 数据量不足\n")
                        continue
                    
                    # 去除缺失值
                    df = df.dropna(subset=['open', 'high', 'low', 'close', 'volume'])
                    
                    if len(df) == 0:
                        self.log_text.insert(tk.END, f"跳过 {csv_file.name}: 数据全为空\n")
                        continue
                    
                    stock_code = csv_file.stem.replace('_hfq', '')
                    self.stock_data[stock_code] = df
                    loaded_count += 1
                    
                    # 更新进度
                    progress = (i + 1) / len(csv_files) * 100
                    self.status_var.set(f"加载股票数据... {progress:.1f}%")
                    self.root.update()
                    
                    # 每加载50个股票更新一次日志
                    if loaded_count % 50 == 0:
                        self.log_text.insert(tk.END, f"已加载 {loaded_count} 只股票数据...\n")
                        self.log_text.see(tk.END)
                    
                except Exception as e:
                    self.logger.error(f"加载文件 {csv_file} 失败: {e}")
                    self.log_text.insert(tk.END, f"加载 {csv_file.name} 失败: {e}\n")
                    continue
                    
            if loaded_count == 0:
                messagebox.showerror("错误", "没有成功加载任何股票数据")
                return
                
            self.status_var.set(f"成功加载 {loaded_count} 只股票数据")
            self.log_text.insert(tk.END, f"成功加载 {loaded_count} 只股票的数据\n")
            self.update_info_display()
            messagebox.showinfo("成功", f"成功加载 {loaded_count} 只股票的数据")
            self.logger.info(f"成功加载 {loaded_count} 只股票数据")
            
        except Exception as e:
            self.status_var.set("加载股票数据失败")
            messagebox.showerror("错误", f"加载股票数据失败: {e}")
            self.logger.error(f"加载股票数据失败: {e}")
            
    def load_factor_data(self):
        """加载因子数据"""
        try:
            filename = filedialog.askopenfilename(
                title="选择因子数据文件",
                initialdir="../../0-数据汇总/4-模型训练数据",
                filetypes=[("Pickle files", "*.pkl"), ("All files", "*.*")]
            )
            
            if filename:
                self.factor_path_var.set(filename)
                self.log_text.insert(tk.END, f"开始加载因子数据: {Path(filename).name}...\n")
                
                self.status_var.set("正在加载因子数据...")
                self.root.update()
                
                with open(filename, 'rb') as f:
                    self.factor_data = pickle.load(f)
                
                # 验证因子数据格式
                if not isinstance(self.factor_data, (pd.DataFrame, dict)):
                    messagebox.showerror("错误", "因子数据格式不正确，应为DataFrame或字典格式")
                    self.factor_data = None
                    return
                
                # 如果是字典格式，检查结构
                if isinstance(self.factor_data, dict):
                    if not all(isinstance(v, pd.DataFrame) for v in self.factor_data.values()):
                        messagebox.showerror("错误", "因子数据字典中应包含DataFrame")
                        self.factor_data = None
                        return
                    
                    total_factors = sum(len(df.columns) - 1 for df in self.factor_data.values() if 'date' in df.columns)
                    total_stocks = len(self.factor_data)
                    self.log_text.insert(tk.END, f"因子数据包含 {total_stocks} 只股票，{total_factors} 个因子\n")
                    
                elif isinstance(self.factor_data, pd.DataFrame):
                    if 'date' not in self.factor_data.columns:
                        messagebox.showerror("错误", "因子数据必须包含'date'列")
                        self.factor_data = None
                        return
                    
                    factor_count = len(self.factor_data.columns) - 1  # 减去date列
                    self.log_text.insert(tk.END, f"因子数据包含 {len(self.factor_data)} 行，{factor_count} 个因子\n")
                
                # 检查日期范围
                if isinstance(self.factor_data, dict):
                    date_ranges = []
                    for stock_code, df in self.factor_data.items():
                        if 'date' in df.columns:
                            df['date'] = pd.to_datetime(df['date'])
                            date_ranges.extend([df['date'].min(), df['date'].max()])
                    
                    if date_ranges:
                        min_date = min(date_ranges)
                        max_date = max(date_ranges)
                        self.log_text.insert(tk.END, f"因子数据日期范围: {min_date.strftime('%Y-%m-%d')} 至 {max_date.strftime('%Y-%m-%d')}\n")
                        
                elif isinstance(self.factor_data, pd.DataFrame):
                    self.factor_data['date'] = pd.to_datetime(self.factor_data['date'])
                    min_date = self.factor_data['date'].min()
                    max_date = self.factor_data['date'].max()
                    self.log_text.insert(tk.END, f"因子数据日期范围: {min_date.strftime('%Y-%m-%d')} 至 {max_date.strftime('%Y-%m-%d')}\n")
                
                self.status_var.set(f"因子数据已加载: {Path(filename).name}")
                self.log_text.insert(tk.END, f"因子数据加载成功\n")
                self.update_info_display()
                messagebox.showinfo("成功", f"因子数据已加载: {Path(filename).name}")
                self.logger.info(f"成功加载因子数据: {filename}")
                
        except Exception as e:
            self.status_var.set("加载因子数据失败")
            messagebox.showerror("错误", f"加载因子数据失败: {e}")
            self.logger.error(f"加载因子数据失败: {e}")
            self.log_text.insert(tk.END, f"加载因子数据失败: {e}\n")
            
    def update_info_display(self):
        """更新信息显示"""
        info_text = "=== 数据信息 ===\n\n"
        
        if self.stock_data:
            info_text += f"股票数据: {len(self.stock_data)} 只股票\n"
            sample_stock = list(self.stock_data.keys())[0]
            sample_data = self.stock_data[sample_stock]
            info_text += f"样本股票: {sample_stock}\n"
            info_text += f"数据列: {list(sample_data.columns)}\n"
            info_text += f"数据行数: {len(sample_data)}\n\n"
            
        if self.factor_data is not None:
            info_text += f"因子数据类型: {type(self.factor_data)}\n"
            if hasattr(self.factor_data, 'shape'):
                info_text += f"因子数据形状: {self.factor_data.shape}\n"
            elif isinstance(self.factor_data, dict):
                info_text += f"因子数据键: {list(self.factor_data.keys())}\n"
                
        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(1.0, info_text)
        
    def update_log_file_display(self):
        """更新日志文件显示"""
        if hasattr(self, 'log_file') and self.log_file:
            self.log_file_label.config(text=self.log_file.name)
        else:
            self.log_file_label.config(text="未设置")
            
    def open_log_directory(self):
        """打开日志目录"""
        try:
            import os
            import subprocess
            import platform
            
            log_dir = str(self.log_dir)
            
            if platform.system() == "Windows":
                os.startfile(log_dir)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", log_dir])
            else:  # Linux
                subprocess.run(["xdg-open", log_dir])
                
        except Exception as e:
            messagebox.showerror("错误", f"无法打开日志目录: {e}")
        
    def start_training(self):
        """开始训练"""
        if not self.stock_data:
            messagebox.showerror("错误", "请先加载股票数据")
            return
            
        if self.factor_data is None:
            messagebox.showerror("错误", "请先加载因子数据")
            return
            
        if self.is_training:
            messagebox.showwarning("警告", "训练正在进行中")
            return
            
        # 启动训练线程
        self.is_training = True
        self.train_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        
        self.training_thread = threading.Thread(target=self.training_loop)
        self.training_thread.daemon = True
        self.training_thread.start()
        
    def stop_training(self):
        """停止训练"""
        self.is_training = False
        self.train_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_var.set("训练已停止")
        
    def training_loop(self):
        """真实的SAC训练循环"""
        try:
            # 创建交易环境
            self.trading_env = TradingEnvironment(
                stock_data=self.stock_data,
                factor_data=self.factor_data,
                initial_capital=1000000.0,
                max_positions=int(self.max_positions_var.get()),
                start_date=self.start_date_var.get()
            )
            
            # 创建SAC智能体
            device = 'cuda' if self.use_gpu_var.get() and torch.cuda.is_available() else 'cpu'
            self.sac_agent = SACAgent(
                state_dim=self.trading_env.get_state_dim(),
                action_dim=self.trading_env.get_action_dim(),
                factor_dim=self.trading_env.get_factor_dim(),
                device=device
            )
            
            # 创建训练器
            episodes = int(self.episodes_var.get())
            self.trainer = SACTrainer(
                env=self.trading_env,
                agent=self.sac_agent,
                episodes=episodes,
                log_callback=self.training_log_callback,
                progress_callback=self.training_progress_callback
            )
            
            # 开始训练
            self.root.after(0, lambda: self.log_text.insert(tk.END, f"开始SAC训练，设备: {device}\n"))
            training_stats = self.trainer.train(stop_flag=lambda: not self.is_training)
            
            # 更新训练日志
            self.training_logs = {
                'episodes': list(range(len(training_stats['episode_rewards']))),
                'rewards': training_stats['episode_rewards'],
                'losses': training_stats['critic_losses'],
                'portfolio_values': training_stats['portfolio_values'],
                'sharpe_ratios': training_stats['sharpe_ratios']
            }
            
        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"训练过程出错: {error_msg}")
            self.root.after(0, lambda msg=error_msg: messagebox.showerror("错误", f"训练过程出错: {msg}"))
            self.root.after(0, lambda msg=error_msg: self.log_text.insert(tk.END, f"训练出错: {msg}\n"))
        finally:
            self.root.after(0, self.training_finished)
    
    def training_log_callback(self, episode, episode_stats):
        """训练日志回调函数"""
        # 使用正确的键名
        reward = episode_stats.get('reward', 0)
        actor_loss = episode_stats.get('actor_loss', 0)
        critic_loss = episode_stats.get('critic_loss', 0)
        portfolio_value = episode_stats.get('portfolio_value', 0)

        message = (f"Episode {episode + 1}: Reward={reward:.6f}, "
                  f"Actor_Loss={actor_loss:.6f}, Critic_Loss={critic_loss:.6f}, "
                  f"Portfolio={portfolio_value:.2f}")
        self.root.after(0, lambda msg=message: self.log_text.insert(tk.END, f"{msg}\n"))
        self.root.after(0, lambda: self.log_text.see(tk.END))
        
    def training_progress_callback(self, episode, total_episodes, stats):
        """训练进度回调函数"""
        # 更新训练日志 - 使用正确的键名
        if 'reward' in stats:
            self.training_logs['episodes'].append(episode)
            self.training_logs['rewards'].append(stats['reward'])

        if 'critic_loss' in stats:
            self.training_logs['losses'].append(stats['critic_loss'])

        if 'portfolio_value' in stats:
            self.training_logs['portfolio_values'].append(stats['portfolio_value'])

        if 'sharpe_ratio' in stats:
            self.training_logs['sharpe_ratios'].append(stats['sharpe_ratio'])

        # 更新界面显示
        total_episodes = int(self.episodes_var.get())
        self.root.after(0, self.update_training_display, episode, total_episodes)

        # 每10个episode更新一次图表
        if episode % 10 == 0:
            self.root.after(0, self.update_charts)
            
    def update_training_display(self, episode, total_episodes):
        """更新训练显示"""
        # 更新状态
        progress = (episode + 1) / total_episodes * 100
        self.status_var.set(f"训练中... Episode {episode + 1}/{total_episodes} ({progress:.1f}%)")
        
        # 更新图表
        if len(self.training_logs['episodes']) > 0:
            self.update_charts()
            
        # 更新日志
        if episode % 10 == 0 and len(self.training_logs['rewards']) > 0:  # 每10轮更新一次日志
            log_msg = f"Episode {episode + 1}: Reward={self.training_logs['rewards'][-1]:.4f}, Loss={self.training_logs['losses'][-1]:.4f}\n"
            self.log_text.insert(tk.END, log_msg)
            self.log_text.see(tk.END)
            
    def update_charts(self):
        """更新图表"""
        try:
            # 清除旧图表
            for ax in [self.ax1, self.ax2, self.ax3, self.ax4]:
                ax.clear()
                
            episodes = self.training_logs['episodes']
            
            # 检查数据长度是否一致
            if len(episodes) == 0:
                return
                
            # 奖励图表
            if len(self.training_logs['rewards']) == len(episodes):
                self.ax1.plot(episodes, self.training_logs['rewards'], 'b-', alpha=0.7)
            self.ax1.set_title("训练奖励")
            self.ax1.set_xlabel("Episode")
            self.ax1.set_ylabel("Reward")
            self.ax1.grid(True)
            
            # 损失图表
            if len(self.training_logs['losses']) == len(episodes):
                self.ax2.plot(episodes, self.training_logs['losses'], 'r-', alpha=0.7)
            self.ax2.set_title("损失函数")
            self.ax2.set_xlabel("Episode")
            self.ax2.set_ylabel("Loss")
            self.ax2.grid(True)
            
            # 组合价值图表
            if len(self.training_logs['portfolio_values']) == len(episodes):
                self.ax3.plot(episodes, self.training_logs['portfolio_values'], 'g-', alpha=0.7)
            self.ax3.set_title("组合价值")
            self.ax3.set_xlabel("Episode")
            self.ax3.set_ylabel("Portfolio Value")
            self.ax3.grid(True)
            
            # 夏普比率图表
            if len(self.training_logs['sharpe_ratios']) == len(episodes):
                self.ax4.plot(episodes, self.training_logs['sharpe_ratios'], 'm-', alpha=0.7)
            self.ax4.set_title("夏普比率")
            self.ax4.set_xlabel("Episode")
            self.ax4.set_ylabel("Sharpe Ratio")
            self.ax4.grid(True)
            
            self.fig.tight_layout()
            self.canvas.draw()
            
        except Exception as e:
            self.logger.error(f"更新图表失败: {e}")
            
    def training_finished(self):
        """训练完成"""
        self.is_training = False
        self.train_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_var.set("训练完成")
        
        log_msg = "\n=== 训练完成 ===\n"
        self.log_text.insert(tk.END, log_msg)
        self.log_text.see(tk.END)
        
    def save_model(self):
        """保存模型"""
        try:
            if self.sac_agent is None:
                messagebox.showerror("错误", "没有训练好的模型可以保存")
                return
                
            model_dir = Path("../../0-数据汇总/5-训练模型")
            model_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            model_path = model_dir / f"sac_model_{timestamp}.pth"
            
            # 保存SAC模型
            self.sac_agent.save_model(str(model_path))
            
            # 保存训练统计和参数
            stats_path = model_dir / f"sac_training_stats_{timestamp}.pkl"
            model_data = {
                'training_logs': self.training_logs,
                'parameters': {
                    'start_date': self.start_date_var.get(),
                    'episodes': self.episodes_var.get(),
                    'max_positions': self.max_positions_var.get(),
                    'use_gpu': self.use_gpu_var.get()
                },
                'training_summary': self.trainer.get_training_summary() if self.trainer else {}
            }
            
            with open(stats_path, 'wb') as f:
                pickle.dump(model_data, f)
                
            self.status_var.set(f"模型已保存: {model_path.name}")
            messagebox.showinfo("成功", f"模型已保存到: {model_path}\n统计数据: {stats_path}")
            self.logger.info(f"模型已保存: {model_path}")
            
        except Exception as e:
            messagebox.showerror("错误", f"保存模型失败: {e}")
            self.logger.error(f"保存模型失败: {e}")
            
    def load_model(self):
        """加载模型"""
        try:
            filename = filedialog.askopenfilename(
                title="选择模型文件",
                initialdir="../../0-数据汇总/5-训练模型",
                filetypes=[("PyTorch files", "*.pth"), ("Pickle files", "*.pkl"), ("All files", "*.*")]
            )
            
            if filename:
                file_path = Path(filename)
                
                if file_path.suffix == '.pth':
                    # 加载PyTorch模型
                    if not self.stock_data or self.factor_data is None:
                        messagebox.showerror("错误", "请先加载股票数据和因子数据")
                        return
                        
                    # 创建环境和智能体
                    self.trading_env = TradingEnvironment(
                        stock_data=self.stock_data,
                        factor_data=self.factor_data,
                        max_positions=int(self.max_positions_var.get())
                    )
                    
                    device = 'cuda' if self.use_gpu_var.get() and torch.cuda.is_available() else 'cpu'
                    self.sac_agent = SACAgent(
                        state_dim=self.trading_env.get_state_dim(),
                        action_dim=self.trading_env.get_action_dim(),
                        factor_dim=self.trading_env.get_factor_dim(),
                        device=device
                    )
                    
                    # 加载模型权重
                    self.sac_agent.load_model(filename)
                    
                elif file_path.suffix == '.pkl':
                    # 加载训练统计
                    with open(filename, 'rb') as f:
                        model_data = pickle.load(f)
                        
                    # 恢复训练日志
                    if 'training_logs' in model_data:
                        self.training_logs = model_data['training_logs']
                        self.update_charts()
                        
                    # 恢复参数
                    if 'parameters' in model_data:
                        params = model_data['parameters']
                        self.start_date_var.set(params.get('start_date', '2020-01-01'))
                        self.episodes_var.set(params.get('episodes', '1000'))
                        self.max_positions_var.set(params.get('max_positions', '10'))
                        self.use_gpu_var.set(params.get('use_gpu', True))
                    
                self.status_var.set(f"模型已加载: {file_path.name}")
                messagebox.showinfo("成功", f"模型已加载: {filename}")
                self.logger.info(f"模型已加载: {filename}")
                
        except Exception as e:
            messagebox.showerror("错误", f"加载模型失败: {e}")
            self.logger.error(f"加载模型失败: {e}")

def main():
    root = tk.Tk()
    app = SACTrainingGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()