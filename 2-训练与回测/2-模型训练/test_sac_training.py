#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SAC训练测试脚本
用于测试修复后的SAC训练流程
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
import torch
import logging
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sac_model import SACAgent
from trading_environment import TradingEnvironment
from trainer import SACTrainer

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_sac_training.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)

def load_test_data():
    """加载测试数据"""
    logger = logging.getLogger(__name__)

    # 获取正确的路径
    current_dir = Path(__file__).parent
    project_root = current_dir.parent.parent

    # 加载股票数据
    stock_dir = project_root / "0-数据汇总" / "1-原始股票数据"
    stock_data = {}

    logger.info(f"股票数据目录: {stock_dir}")
    logger.info("开始加载股票数据...")

    if not stock_dir.exists():
        logger.error(f"股票数据目录不存在: {stock_dir}")
        return {}, None

    csv_files = list(stock_dir.glob("*.csv"))[:10]  # 只加载前10只股票进行测试
    logger.info(f"找到 {len(csv_files)} 个CSV文件")

    for csv_file in csv_files:
        try:
            df = pd.read_csv(csv_file, encoding='utf-8')
            df.columns = df.columns.str.lower()

            required_columns = ['date', 'open', 'high', 'low', 'close', 'volume']
            if all(col in df.columns for col in required_columns):
                df['date'] = pd.to_datetime(df['date'])
                df = df.sort_values('date').reset_index(drop=True)

                if len(df) >= 100:
                    stock_code = csv_file.stem.replace('_hfq', '')
                    stock_data[stock_code] = df
                    logger.info(f"加载股票 {stock_code}: {len(df)} 条记录")

        except Exception as e:
            logger.error(f"加载文件 {csv_file} 失败: {e}")

    logger.info(f"成功加载 {len(stock_data)} 只股票数据")

    # 加载因子数据
    factor_dir = project_root / "0-数据汇总" / "4-模型训练数据"
    logger.info(f"因子数据目录: {factor_dir}")

    if not factor_dir.exists():
        logger.error(f"因子数据目录不存在: {factor_dir}")
        return stock_data, None

    factor_files = list(factor_dir.glob("train_data_*.pkl"))
    logger.info(f"找到 {len(factor_files)} 个因子数据文件")

    if factor_files:
        factor_file = factor_files[0]
        logger.info(f"加载因子数据: {factor_file}")

        with open(factor_file, 'rb') as f:
            factor_data = pickle.load(f)

        logger.info(f"因子数据类型: {type(factor_data)}")
        if isinstance(factor_data, dict):
            logger.info(f"因子数据包含 {len(factor_data)} 只股票")
    else:
        logger.error("未找到因子数据文件")
        factor_data = None

    return stock_data, factor_data

def test_sac_training():
    """测试SAC训练"""
    logger = setup_logging()
    logger.info("开始SAC训练测试")
    
    # 加载数据
    stock_data, factor_data = load_test_data()
    
    if not stock_data or factor_data is None:
        logger.error("数据加载失败，无法进行测试")
        return
        
    try:
        # 创建交易环境
        logger.info("创建交易环境...")
        trading_env = TradingEnvironment(
            stock_data=stock_data,
            factor_data=factor_data,
            initial_capital=1000000.0,
            max_positions=5,  # 减少持仓数量以简化测试
            start_date='2020-01-01'
        )
        
        logger.info(f"环境创建成功: {len(trading_env.stock_codes)} 只股票, {len(trading_env.trading_dates)} 个交易日")
        
        # 创建SAC智能体
        logger.info("创建SAC智能体...")
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        logger.info(f"使用设备: {device}")
        
        sac_agent = SACAgent(
            state_dim=trading_env.get_state_dim(),
            action_dim=trading_env.get_action_dim(),
            factor_dim=trading_env.get_factor_dim(),
            device=device
        )
        
        logger.info(f"智能体创建成功: state_dim={trading_env.get_state_dim()}, action_dim={trading_env.get_action_dim()}, factor_dim={trading_env.get_factor_dim()}")
        
        # 创建训练器
        logger.info("创建训练器...")
        trainer = SACTrainer(
            env=trading_env,
            agent=sac_agent,
            episodes=20,  # 只训练20轮进行测试
            batch_size=32,  # 减小批次大小
            warmup_episodes=5,  # 减少预热轮数
        )
        
        # 开始训练
        logger.info("开始训练...")
        training_stats = trainer.train()
        
        # 输出训练结果
        logger.info("训练完成！")
        logger.info(f"训练轮数: {len(training_stats['episode_rewards'])}")
        
        if training_stats['episode_rewards']:
            rewards = training_stats['episode_rewards']
            logger.info(f"奖励统计: 平均={np.mean(rewards):.6f}, 最小={np.min(rewards):.6f}, 最大={np.max(rewards):.6f}")
            
        if training_stats['critic_losses']:
            losses = training_stats['critic_losses']
            logger.info(f"损失统计: 平均={np.mean(losses):.6f}, 最小={np.min(losses):.6f}, 最大={np.max(losses):.6f}")
            
        if training_stats['portfolio_values']:
            values = training_stats['portfolio_values']
            logger.info(f"组合价值: 初始={values[0]:.2f}, 最终={values[-1]:.2f}, 收益率={(values[-1]/values[0]-1)*100:.2f}%")
            
        # 检查是否存在非零值
        non_zero_rewards = [r for r in training_stats['episode_rewards'] if r != 0]
        non_zero_losses = [l for l in training_stats['critic_losses'] if l != 0]
        
        logger.info(f"非零奖励数量: {len(non_zero_rewards)}/{len(training_stats['episode_rewards'])}")
        logger.info(f"非零损失数量: {len(non_zero_losses)}/{len(training_stats['critic_losses'])}")
        
        if len(non_zero_rewards) > 0 and len(non_zero_losses) > 0:
            logger.info("✅ 训练成功！奖励和损失都有非零值")
            return True
        else:
            logger.warning("⚠️ 训练可能存在问题：奖励或损失仍为零")
            return False
            
    except Exception as e:
        logger.error(f"训练过程出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = test_sac_training()
    if success:
        print("\n🎉 SAC训练测试成功！")
    else:
        print("\n❌ SAC训练测试失败！")
