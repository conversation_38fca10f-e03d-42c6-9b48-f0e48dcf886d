# SAC强化学习股票交易训练系统依赖包

# 深度学习框架
torch>=1.12.0
torchvision>=0.13.0

# 数据处理
pandas>=1.5.0
numpy>=1.21.0
scipy>=1.9.0

# 机器学习
scikit-learn>=1.1.0

# 可视化
matplotlib>=3.5.0
seaborn>=0.11.0

# GUI
tkinter  # 通常随Python安装

# 其他工具
pickle5>=0.0.11  # Python < 3.8兼容
pathlib2>=2.3.0  # Python < 3.4兼容
psutil>=5.9.0  # 系统监控

# 可选：CUDA支持（如果使用GPU）
# 请根据您的CUDA版本安装对应的PyTorch版本
# 访问 https://pytorch.org/get-started/locally/ 获取安装命令

# 开发工具（可选）
# jupyter>=1.0.0
# ipython>=8.0.0