#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的图表布局
验证损失函数分开显示的效果
"""

import matplotlib.pyplot as plt
import numpy as np
from matplotlib.figure import Figure
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

def test_chart_layout():
    """测试新的图表布局"""
    print("测试新的图表布局...")
    
    # 创建测试数据
    episodes = list(range(1, 101))
    
    # 模拟不同数量级的损失数据
    actor_losses = np.random.normal(-20, 5, 100)  # Actor损失通常是负数，数量级较大
    critic_losses = np.random.normal(5, 2, 100)   # Critic损失通常是正数，数量级较小
    alpha_losses = np.random.normal(-10, 3, 100)  # Alpha损失通常是负数，数量级中等
    
    # 其他指标数据
    rewards = np.random.normal(-200, 50, 100)
    portfolio_values = 1000000 + np.cumsum(np.random.normal(1000, 5000, 100))
    sharpe_ratios = np.random.normal(0.5, 0.3, 100)
    max_drawdowns = np.abs(np.random.normal(0.05, 0.02, 100))
    returns = np.cumsum(np.random.normal(0.001, 0.01, 100))
    
    # 创建图表
    fig = Figure(figsize=(16, 12), dpi=100)
    
    # 创建8个子图 (4行2列)
    ax1 = fig.add_subplot(4, 2, 1)  # 训练奖励
    ax2 = fig.add_subplot(4, 2, 2)  # Actor损失
    ax3 = fig.add_subplot(4, 2, 3)  # Critic损失
    ax4 = fig.add_subplot(4, 2, 4)  # Alpha损失
    ax5 = fig.add_subplot(4, 2, 5)  # 组合价值
    ax6 = fig.add_subplot(4, 2, 6)  # 夏普比率
    ax7 = fig.add_subplot(4, 2, 7)  # 最大回撤
    ax8 = fig.add_subplot(4, 2, 8)  # 收益率
    
    # 1. 训练奖励
    ax1.plot(episodes, rewards, 'b-', alpha=0.7)
    ax1.set_title("训练奖励")
    ax1.set_xlabel("Episode")
    ax1.set_ylabel("Reward")
    ax1.grid(True)
    
    # 2. Actor损失 (独立显示)
    ax2.plot(episodes, actor_losses, 'r-', alpha=0.7)
    ax2.set_title("Actor损失")
    ax2.set_xlabel("Episode")
    ax2.set_ylabel("Actor Loss")
    ax2.grid(True)
    
    # 3. Critic损失 (独立显示)
    ax3.plot(episodes, critic_losses, 'g-', alpha=0.7)
    ax3.set_title("Critic损失")
    ax3.set_xlabel("Episode")
    ax3.set_ylabel("Critic Loss")
    ax3.grid(True)
    
    # 4. Alpha损失 (独立显示)
    ax4.plot(episodes, alpha_losses, 'orange', alpha=0.7)
    ax4.set_title("Alpha损失")
    ax4.set_xlabel("Episode")
    ax4.set_ylabel("Alpha Loss")
    ax4.grid(True)
    
    # 5. 组合价值
    ax5.plot(episodes, portfolio_values, 'g-', alpha=0.7)
    ax5.axhline(y=1000000, color='r', linestyle='--', alpha=0.5, label='初始资金')
    ax5.set_title("组合价值")
    ax5.set_xlabel("Episode")
    ax5.set_ylabel("Portfolio Value")
    ax5.legend()
    ax5.grid(True)
    
    # 6. 夏普比率
    ax6.plot(episodes, sharpe_ratios, 'm-', alpha=0.7)
    ax6.axhline(y=1.0, color='r', linestyle='--', alpha=0.5, label='基准线(1.0)')
    ax6.set_title("夏普比率")
    ax6.set_xlabel("Episode")
    ax6.set_ylabel("Sharpe Ratio")
    ax6.legend()
    ax6.grid(True)
    
    # 7. 最大回撤
    drawdowns_pct = [dd * 100 for dd in max_drawdowns]
    ax7.plot(episodes, drawdowns_pct, 'red', alpha=0.7)
    ax7.fill_between(episodes, drawdowns_pct, 0, alpha=0.3, color='red')
    ax7.set_title("最大回撤")
    ax7.set_xlabel("Episode")
    ax7.set_ylabel("Max Drawdown (%)")
    ax7.grid(True)
    
    # 8. 收益率
    returns_pct = [ret * 100 for ret in returns]
    ax8.plot(episodes, returns_pct, 'purple', alpha=0.7)
    ax8.axhline(y=0, color='k', linestyle='-', alpha=0.3)
    ax8.set_title("总收益率")
    ax8.set_xlabel("Episode")
    ax8.set_ylabel("Total Return (%)")
    ax8.grid(True)
    
    fig.tight_layout()
    
    # 保存图表
    fig.savefig('test_chart_layout.png', dpi=150, bbox_inches='tight')
    print("图表已保存为 test_chart_layout.png")
    
    # 显示数量级对比
    print("\n损失函数数量级对比：")
    print(f"Actor Loss 范围: [{np.min(actor_losses):.2f}, {np.max(actor_losses):.2f}]")
    print(f"Critic Loss 范围: [{np.min(critic_losses):.2f}, {np.max(critic_losses):.2f}]")
    print(f"Alpha Loss 范围: [{np.min(alpha_losses):.2f}, {np.max(alpha_losses):.2f}]")
    
    print("\n✅ 新布局优势：")
    print("1. 每个损失函数都有独立的Y轴刻度，可以清楚看到各自的变化趋势")
    print("2. 不同数量级的损失不会相互干扰显示效果")
    print("3. 更容易识别每个损失函数的收敛情况")
    print("4. 图表布局更加清晰，信息密度适中")

if __name__ == "__main__":
    test_chart_layout()
