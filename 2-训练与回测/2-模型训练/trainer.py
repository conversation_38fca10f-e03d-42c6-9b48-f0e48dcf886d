import numpy as np
import torch
import logging
from typing import Dict, List, Tuple, Callable, Optional
import time
from pathlib import Path
import pickle
from collections import deque
import matplotlib.pyplot as plt

from sac_model import SACAgent
from trading_environment import TradingEnvironment

class SACTrainer:
    """SAC训练器"""
    
    def __init__(
        self,
        env: TradingEnvironment,
        agent: SACAgent,
        episodes: int = 1000,
        batch_size: int = 64,
        update_frequency: int = 1,
        eval_frequency: int = 50,
        save_frequency: int = 100,
        warmup_episodes: int = 2,
        log_callback: Optional[Callable] = None,
        progress_callback: Optional[Callable] = None
    ):
        self.env = env
        self.agent = agent
        self.episodes = episodes
        self.batch_size = batch_size
        self.update_frequency = update_frequency
        self.eval_frequency = eval_frequency
        self.save_frequency = save_frequency
        self.warmup_episodes = warmup_episodes
        self.log_callback = log_callback
        self.progress_callback = progress_callback
        
        # 训练统计
        self.training_stats = {
            'episode_rewards': [],
            'episode_lengths': [],
            'actor_losses': [],
            'critic_losses': [],
            'alpha_losses': [],
            'portfolio_values': [],
            'sharpe_ratios': [],
            'max_drawdowns': [],
            'total_returns': []
        }
        
        # 最佳模型跟踪
        self.best_sharpe_ratio = -np.inf
        self.best_model_path = None
        
        # 日志
        self.logger = logging.getLogger(__name__)
        
    def train(self, stop_flag: Optional[Callable] = None) -> Dict[str, List[float]]:
        """开始训练"""
        self.logger.info(f"开始SAC训练，共{self.episodes}轮")
        self.logger.info(f"训练参数: batch_size={self.batch_size}, warmup_episodes={self.warmup_episodes}")
        self.logger.info(f"环境参数: 股票数量={len(self.env.stock_codes)}, 序列长度={self.env.sequence_length}")
        
        start_time = time.time()
        
        for episode in range(self.episodes):
            if stop_flag and stop_flag():
                self.logger.info(f"训练在第{episode}轮被停止")
                break
                
            episode_start_time = time.time()
            
            # 执行一轮训练
            episode_stats = self.train_episode(episode)
            
            episode_time = time.time() - episode_start_time
            
            # 更新统计信息
            self.update_training_stats(episode_stats)
            
            # 详细日志输出
            if episode < 10 or episode % 5 == 0:  # 前10轮每轮输出，之后每5轮输出
                self.logger.info(
                    f"Episode {episode+1}/{self.episodes}: "
                    f"Reward={episode_stats['reward']:.6f}, "
                    f"Actor_Loss={episode_stats['actor_loss']:.6f}, "
                    f"Critic_Loss={episode_stats['critic_loss']:.6f}, "
                    f"Alpha_Loss={episode_stats['alpha_loss']:.6f}, "
                    f"Portfolio_Value={episode_stats['portfolio_value']:.2f}, "
                    f"Time={episode_time:.2f}s"
                )
                
                # 经验回放缓冲区状态
                buffer_size = len(self.agent.replay_buffer)
                self.logger.info(
                    f"  缓冲区状态: {buffer_size}/{self.agent.replay_buffer.capacity}, "
                    f"学习状态: {'学习中' if episode >= self.warmup_episodes and buffer_size >= self.batch_size else '预热/等待'}"
                )
            
            # 定期评估
            if episode % self.eval_frequency == 0 and episode > 0:
                eval_stats = self.evaluate()
                self.logger.info(
                    f"=== 评估结果 Episode {episode} ==="
                )
                self.logger.info(
                    f"  平均奖励: {eval_stats.get('avg_reward', 0):.6f}"
                )
                self.logger.info(
                    f"  总收益率: {eval_stats.get('total_return', 0):.4f}"
                )
                self.logger.info(
                    f"  夏普比率: {eval_stats.get('sharpe_ratio', 0):.4f}"
                )
                self.logger.info(
                    f"  最大回撤: {eval_stats.get('max_drawdown', 0):.4f}"
                )
                
                # 保存最佳模型
                if eval_stats.get('sharpe_ratio', -np.inf) > self.best_sharpe_ratio:
                    self.best_sharpe_ratio = eval_stats['sharpe_ratio']
                    self.save_best_model(episode, eval_stats)
                    self.logger.info(f"  *** 保存最佳模型，夏普比率: {self.best_sharpe_ratio:.4f} ***")
                    
            # 定期保存检查点
            if episode % self.save_frequency == 0 and episode > 0:
                self.save_checkpoint(episode)
                self.logger.info(f"保存检查点: Episode {episode}")
                
            # 进度回调
            if self.progress_callback:
                self.progress_callback(episode, self.episodes, episode_stats)
                
            # 日志回调
            if self.log_callback and episode % 10 == 0:
                self.log_callback(episode, episode_stats)
                
        total_time = time.time() - start_time
        avg_time_per_episode = total_time / max(1, episode + 1)
        self.logger.info(f"训练完成，总耗时{total_time:.2f}秒，平均每轮{avg_time_per_episode:.2f}秒")
        
        return self.training_stats
    
    def train_episode(self, episode: int) -> Dict[str, float]:
        """训练一轮"""
        state = self.env.reset()
        episode_reward = 0.0
        episode_length = 0
        losses = {'actor': [], 'critic': [], 'alpha': []}
        step_rewards = []  # 记录每步奖励
        
        # 详细日志：环境重置信息
        if episode < 5:  # 前5个episode显示详细信息
            self.logger.info(f"  Episode {episode+1} 开始，环境已重置")
            self.logger.info(f"  初始状态维度: {state.shape if hasattr(state, 'shape') else len(state)}")
            self.logger.info(f"  当前交易日期: {self.env.trading_dates[self.env.current_step] if self.env.current_step < len(self.env.trading_dates) else 'N/A'}")
            self.logger.info(f"  初始组合价值: {self.env.portfolio_value:.2f}")
        
        while True:
            # 选择动作
            if episode < self.warmup_episodes:
                # 预热期：随机动作
                action = np.random.randn(self.env.get_action_dim())
                if episode < 3 and episode_length < 5:  # 前3个episode的前5步显示动作信息
                    self.logger.info(f"    Step {episode_length+1}: 使用随机动作 (预热期)")
            else:
                # 获取因子序列（使用第一只股票作为示例）
                sample_stock = self.env.stock_codes[0] if self.env.stock_codes else 'default'
                factor_sequence = self.env.get_factor_sequence(sample_stock)
                action = self.agent.select_action(state, factor_sequence, evaluate=False)
                if episode < 5 and episode_length < 5:  # 前5个episode的前5步显示动作信息
                    self.logger.info(f"    Step {episode_length+1}: 智能体选择动作，动作维度: {len(action)}")
                
            # 执行动作
            next_state, reward, done, info = self.env.step(action)
            step_rewards.append(reward)
            
            # 详细日志：步骤信息
            if episode < 3 and episode_length < 10:  # 前3个episode的前10步
                self.logger.info(
                    f"    Step {episode_length+1}: Reward={reward:.6f}, "
                    f"Portfolio_Value={self.env.portfolio_value:.2f}, "
                    f"Done={done}"
                )
            
            # 存储经验
            if episode >= self.warmup_episodes:
                # 获取当前和下一个状态的因子序列
                sample_stock = self.env.stock_codes[0] if self.env.stock_codes else 'default'
                current_factor_sequence = self.env.get_factor_sequence(sample_stock)
                
                # 为下一个状态获取因子序列（需要临时更新环境状态）
                current_step = self.env.current_step
                self.env.current_step = min(current_step + 1, len(self.env.trading_dates) - 1)
                next_factor_sequence = self.env.get_factor_sequence(sample_stock)
                self.env.current_step = current_step  # 恢复原状态
                
                self.agent.replay_buffer.push(
                    state, action, reward, next_state, float(done),
                    current_factor_sequence, next_factor_sequence
                )
                
                if episode < 5 and episode_length == 0:  # 每个episode第一次存储经验时
                    self.logger.info(f"    开始存储经验到回放缓冲区")
                
            # 更新网络
            if (episode >= self.warmup_episodes and 
                len(self.agent.replay_buffer) >= self.batch_size and
                episode_length % self.update_frequency == 0):
                
                update_info = self.agent.update_parameters(self.batch_size)
                if update_info:
                    losses['actor'].append(update_info.get('actor_loss', 0))
                    losses['critic'].append(update_info.get('critic_loss', 0))
                    losses['alpha'].append(update_info.get('alpha_loss', 0))
                    
                    if episode < 5 and len(losses['actor']) == 1:  # 每个episode第一次更新参数时
                        self.logger.info(
                            f"    首次参数更新: Actor_Loss={update_info.get('actor_loss', 0):.6f}, "
                            f"Critic_Loss={update_info.get('critic_loss', 0):.6f}"
                        )
                    
            state = next_state
            episode_reward += reward
            episode_length += 1
            
            if done:
                break
                
        # 计算平均损失
        avg_losses = {
            'actor': np.mean(losses['actor']) if losses['actor'] else 0.0,
            'critic': np.mean(losses['critic']) if losses['critic'] else 0.0,
            'alpha': np.mean(losses['alpha']) if losses['alpha'] else 0.0
        }
        
        # 获取环境性能指标
        env_stats = self.env.get_performance_summary()
        
        # 详细日志：episode结束统计
        if episode < 5:  # 前5个episode显示详细统计
            self.logger.info(f"  Episode {episode+1} 结束统计:")
            self.logger.info(f"    总步数: {episode_length}")
            self.logger.info(f"    总奖励: {episode_reward:.6f}")
            self.logger.info(f"    平均步奖励: {episode_reward/max(1, episode_length):.6f}")
            self.logger.info(f"    奖励范围: [{min(step_rewards):.6f}, {max(step_rewards):.6f}]")
            self.logger.info(f"    参数更新次数: Actor={len(losses['actor'])}, Critic={len(losses['critic'])}")
            self.logger.info(f"    最终组合价值: {env_stats.get('final_portfolio_value', 0):.2f}")
            self.logger.info(f"    总收益率: {env_stats.get('total_return', 0):.4f}")
            
            # 持仓信息
            if hasattr(self.env, 'positions') and self.env.positions:
                position_count = len(self.env.positions)
                self.logger.info(f"    当前持仓数量: {position_count}")
                if position_count > 0:
                    total_weight = sum(pos.weight for pos in self.env.positions.values())
                    self.logger.info(f"    总持仓权重: {total_weight:.4f}")
        
        return {
            'reward': episode_reward,
            'length': episode_length,
            'actor_loss': avg_losses['actor'],
            'critic_loss': avg_losses['critic'],
            'alpha_loss': avg_losses['alpha'],
            'portfolio_value': env_stats.get('final_portfolio_value', 0),
            'total_return': env_stats.get('total_return', 0),
            'sharpe_ratio': env_stats.get('sharpe_ratio', 0),
            'max_drawdown': env_stats.get('max_drawdown', 0)
        }
    
    def evaluate(self, num_episodes: int = 1) -> Dict[str, float]:
        """评估模型性能"""
        eval_rewards = []
        eval_returns = []
        eval_sharpe_ratios = []
        eval_drawdowns = []
        
        for _ in range(num_episodes):
            state = self.env.reset()
            episode_reward = 0.0
            
            while True:
                # 获取因子序列
                sample_stock = self.env.stock_codes[0] if self.env.stock_codes else 'default'
                factor_sequence = self.env.get_factor_sequence(sample_stock)
                
                # 评估模式：使用确定性动作
                action = self.agent.select_action(state, factor_sequence, evaluate=True)
                
                next_state, reward, done, info = self.env.step(action)
                
                state = next_state
                episode_reward += reward
                
                if done:
                    break
                    
            # 收集评估统计
            env_stats = self.env.get_performance_summary()
            eval_rewards.append(episode_reward)
            eval_returns.append(env_stats.get('total_return', 0))
            eval_sharpe_ratios.append(env_stats.get('sharpe_ratio', 0))
            eval_drawdowns.append(env_stats.get('max_drawdown', 0))
            
        return {
            'avg_reward': np.mean(eval_rewards),
            'total_return': np.mean(eval_returns),
            'sharpe_ratio': np.mean(eval_sharpe_ratios),
            'max_drawdown': np.mean(eval_drawdowns)
        }
    
    def update_training_stats(self, episode_stats: Dict[str, float]):
        """更新训练统计信息"""
        self.training_stats['episode_rewards'].append(episode_stats['reward'])
        self.training_stats['episode_lengths'].append(episode_stats['length'])
        self.training_stats['actor_losses'].append(episode_stats['actor_loss'])
        self.training_stats['critic_losses'].append(episode_stats['critic_loss'])
        self.training_stats['alpha_losses'].append(episode_stats['alpha_loss'])
        self.training_stats['portfolio_values'].append(episode_stats['portfolio_value'])
        self.training_stats['sharpe_ratios'].append(episode_stats['sharpe_ratio'])
        self.training_stats['max_drawdowns'].append(episode_stats['max_drawdown'])
        self.training_stats['total_returns'].append(episode_stats['total_return'])
        
    def save_best_model(self, episode: int, eval_stats: Dict[str, float]):
        """保存最佳模型"""
        model_dir = Path("../../0-数据汇总/5-训练模型")
        model_dir.mkdir(parents=True, exist_ok=True)
        
        model_path = model_dir / f"best_sac_model_episode_{episode}.pth"
        
        # 保存模型
        self.agent.save_model(str(model_path))
        
        # 保存训练统计
        stats_path = model_dir / f"best_training_stats_episode_{episode}.pkl"
        with open(stats_path, 'wb') as f:
            pickle.dump({
                'training_stats': self.training_stats,
                'eval_stats': eval_stats,
                'episode': episode
            }, f)
            
        self.best_model_path = model_path
        self.logger.info(f"保存最佳模型: {model_path}")
        
    def save_checkpoint(self, episode: int):
        """保存检查点"""
        model_dir = Path("../../0-数据汇总/5-训练模型")
        model_dir.mkdir(parents=True, exist_ok=True)
        
        checkpoint_path = model_dir / f"sac_checkpoint_episode_{episode}.pth"
        
        # 保存完整的检查点
        checkpoint = {
            'episode': episode,
            'agent_state': {
                'actor_state_dict': self.agent.actor.state_dict(),
                'critic_state_dict': self.agent.critic.state_dict(),
                'critic_target_state_dict': self.agent.critic_target.state_dict(),
                'gru_extractor_state_dict': self.agent.gru_extractor.state_dict(),
                'actor_optimizer_state_dict': self.agent.actor_optimizer.state_dict(),
                'critic_optimizer_state_dict': self.agent.critic_optimizer.state_dict(),
                'gru_optimizer_state_dict': self.agent.gru_optimizer.state_dict(),
            },
            'training_stats': self.training_stats,
            'best_sharpe_ratio': self.best_sharpe_ratio
        }
        
        if self.agent.automatic_entropy_tuning:
            checkpoint['agent_state']['log_alpha'] = self.agent.log_alpha
            checkpoint['agent_state']['alpha_optimizer_state_dict'] = self.agent.alpha_optimizer.state_dict()
            
        torch.save(checkpoint, checkpoint_path)
        self.logger.info(f"保存检查点: {checkpoint_path}")
        
    def load_checkpoint(self, checkpoint_path: str) -> int:
        """加载检查点"""
        checkpoint = torch.load(checkpoint_path, map_location=self.agent.device)
        
        # 恢复智能体状态
        agent_state = checkpoint['agent_state']
        self.agent.actor.load_state_dict(agent_state['actor_state_dict'])
        self.agent.critic.load_state_dict(agent_state['critic_state_dict'])
        self.agent.critic_target.load_state_dict(agent_state['critic_target_state_dict'])
        self.agent.gru_extractor.load_state_dict(agent_state['gru_extractor_state_dict'])
        
        self.agent.actor_optimizer.load_state_dict(agent_state['actor_optimizer_state_dict'])
        self.agent.critic_optimizer.load_state_dict(agent_state['critic_optimizer_state_dict'])
        self.agent.gru_optimizer.load_state_dict(agent_state['gru_optimizer_state_dict'])
        
        if self.agent.automatic_entropy_tuning and 'log_alpha' in agent_state:
            self.agent.log_alpha = agent_state['log_alpha']
            self.agent.alpha_optimizer.load_state_dict(agent_state['alpha_optimizer_state_dict'])
            
        # 恢复训练统计
        self.training_stats = checkpoint['training_stats']
        self.best_sharpe_ratio = checkpoint['best_sharpe_ratio']
        
        episode = checkpoint['episode']
        self.logger.info(f"从第{episode}轮恢复训练")
        
        return episode
    
    def plot_training_curves(self, save_path: Optional[str] = None):
        """绘制训练曲线"""
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle('SAC Training Curves', fontsize=16)
        
        # 奖励曲线
        axes[0, 0].plot(self.training_stats['episode_rewards'])
        axes[0, 0].set_title('Episode Rewards')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('Reward')
        axes[0, 0].grid(True)
        
        # 损失曲线
        axes[0, 1].plot(self.training_stats['actor_losses'], label='Actor Loss')
        axes[0, 1].plot(self.training_stats['critic_losses'], label='Critic Loss')
        axes[0, 1].set_title('Training Losses')
        axes[0, 1].set_xlabel('Episode')
        axes[0, 1].set_ylabel('Loss')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # 组合价值
        axes[0, 2].plot(self.training_stats['portfolio_values'])
        axes[0, 2].set_title('Portfolio Value')
        axes[0, 2].set_xlabel('Episode')
        axes[0, 2].set_ylabel('Value')
        axes[0, 2].grid(True)
        
        # 夏普比率
        axes[1, 0].plot(self.training_stats['sharpe_ratios'])
        axes[1, 0].set_title('Sharpe Ratio')
        axes[1, 0].set_xlabel('Episode')
        axes[1, 0].set_ylabel('Sharpe Ratio')
        axes[1, 0].grid(True)
        
        # 最大回撤
        axes[1, 1].plot(self.training_stats['max_drawdowns'])
        axes[1, 1].set_title('Max Drawdown')
        axes[1, 1].set_xlabel('Episode')
        axes[1, 1].set_ylabel('Drawdown')
        axes[1, 1].grid(True)
        
        # 总收益率
        axes[1, 2].plot(self.training_stats['total_returns'])
        axes[1, 2].set_title('Total Return')
        axes[1, 2].set_xlabel('Episode')
        axes[1, 2].set_ylabel('Return')
        axes[1, 2].grid(True)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            
        return fig
    
    def get_training_summary(self) -> Dict[str, float]:
        """获取训练摘要"""
        if not self.training_stats['episode_rewards']:
            return {}
            
        return {
            'total_episodes': len(self.training_stats['episode_rewards']),
            'avg_reward': np.mean(self.training_stats['episode_rewards']),
            'best_reward': np.max(self.training_stats['episode_rewards']),
            'final_portfolio_value': self.training_stats['portfolio_values'][-1] if self.training_stats['portfolio_values'] else 0,
            'best_sharpe_ratio': self.best_sharpe_ratio,
            'avg_sharpe_ratio': np.mean(self.training_stats['sharpe_ratios']) if self.training_stats['sharpe_ratios'] else 0,
            'final_total_return': self.training_stats['total_returns'][-1] if self.training_stats['total_returns'] else 0,
            'avg_actor_loss': np.mean(self.training_stats['actor_losses']) if self.training_stats['actor_losses'] else 0,
            'avg_critic_loss': np.mean(self.training_stats['critic_losses']) if self.training_stats['critic_losses'] else 0
        }

class GPUOptimizer:
    """GPU优化器"""
    
    @staticmethod
    def optimize_for_rtx3060():
        """针对RTX3060优化设置"""
        if torch.cuda.is_available():
            # 设置内存分配策略
            torch.cuda.empty_cache()
            
            # 启用混合精度训练
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
            
            # 设置内存增长策略
            torch.cuda.set_per_process_memory_fraction(0.9)  # 使用90%显存
            
            logging.getLogger(__name__).info("GPU优化设置已应用")
            
            return {
                'device': 'cuda',
                'memory_fraction': 0.9,
                'mixed_precision': True
            }
        else:
            logging.getLogger(__name__).warning("CUDA不可用，使用CPU训练")
            return {'device': 'cpu'}
    
    @staticmethod
    def get_gpu_info() -> Dict[str, any]:
        """获取GPU信息"""
        if torch.cuda.is_available():
            return {
                'available': True,
                'device_count': torch.cuda.device_count(),
                'current_device': torch.cuda.current_device(),
                'device_name': torch.cuda.get_device_name(),
                'memory_allocated': torch.cuda.memory_allocated(),
                'memory_reserved': torch.cuda.memory_reserved(),
                'max_memory_allocated': torch.cuda.max_memory_allocated()
            }
        else:
            return {'available': False}