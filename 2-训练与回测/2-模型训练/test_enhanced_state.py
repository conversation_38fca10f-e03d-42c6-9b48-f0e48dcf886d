#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强状态向量的脚本
验证修改后的get_state方法是否正确包含因子数据
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
import logging
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from trading_environment import TradingEnvironment

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_enhanced_state.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)

def load_test_data():
    """加载测试数据"""
    logger = logging.getLogger(__name__)
    
    # 获取正确的路径
    current_dir = Path(__file__).parent
    project_root = current_dir.parent.parent
    
    # 加载股票数据
    stock_dir = project_root / "0-数据汇总" / "1-原始股票数据"
    stock_data = {}
    
    logger.info(f"股票数据目录: {stock_dir}")
    
    if not stock_dir.exists():
        logger.error(f"股票数据目录不存在: {stock_dir}")
        return {}, None
        
    csv_files = list(stock_dir.glob("*.csv"))[:5]  # 只加载前5只股票进行测试
    logger.info(f"找到 {len(csv_files)} 个CSV文件")
    
    for csv_file in csv_files:
        try:
            df = pd.read_csv(csv_file, encoding='utf-8')
            df.columns = df.columns.str.lower()
            
            required_columns = ['date', 'open', 'high', 'low', 'close', 'volume']
            if all(col in df.columns for col in required_columns):
                df['date'] = pd.to_datetime(df['date'])
                df = df.sort_values('date').reset_index(drop=True)
                
                if len(df) >= 100:
                    stock_code = csv_file.stem.replace('_hfq', '')
                    stock_data[stock_code] = df
                    logger.info(f"加载股票 {stock_code}: {len(df)} 条记录")
                    
        except Exception as e:
            logger.error(f"加载文件 {csv_file} 失败: {e}")
            
    logger.info(f"成功加载 {len(stock_data)} 只股票数据")
    
    # 加载因子数据
    factor_dir = project_root / "0-数据汇总" / "4-模型训练数据"
    logger.info(f"因子数据目录: {factor_dir}")
    
    if not factor_dir.exists():
        logger.error(f"因子数据目录不存在: {factor_dir}")
        return stock_data, None
        
    factor_files = list(factor_dir.glob("train_data_*.pkl"))
    logger.info(f"找到 {len(factor_files)} 个因子数据文件")
    
    if factor_files:
        factor_file = factor_files[0]
        logger.info(f"加载因子数据: {factor_file}")
        
        with open(factor_file, 'rb') as f:
            factor_data = pickle.load(f)
            
        logger.info(f"因子数据类型: {type(factor_data)}")
        if isinstance(factor_data, dict):
            logger.info(f"因子数据包含 {len(factor_data)} 只股票")
    else:
        logger.error("未找到因子数据文件")
        factor_data = None
        
    return stock_data, factor_data

def test_enhanced_state():
    """测试增强状态向量"""
    logger = setup_logging()
    logger.info("开始测试增强状态向量")
    
    # 加载数据
    stock_data, factor_data = load_test_data()
    
    if not stock_data or factor_data is None:
        logger.error("数据加载失败，无法进行测试")
        return False
        
    try:
        # 创建交易环境
        logger.info("创建交易环境...")
        trading_env = TradingEnvironment(
            stock_data=stock_data,
            factor_data=factor_data,
            initial_capital=1000000.0,
            max_positions=3,  # 减少持仓数量以简化测试
            start_date='2020-01-01'
        )
        
        logger.info(f"环境创建成功: {len(trading_env.stock_codes)} 只股票, {len(trading_env.trading_dates)} 个交易日")
        
        # 测试状态维度
        state_dim = trading_env.get_state_dim()
        factor_dim = trading_env.get_factor_dim()
        logger.info(f"状态维度: {state_dim}")
        logger.info(f"因子维度: {factor_dim}")
        
        # 获取初始状态
        initial_state = trading_env.get_state()
        logger.info(f"初始状态形状: {initial_state.shape}")
        logger.info(f"初始状态维度匹配: {len(initial_state) == state_dim}")
        
        # 分析状态向量组成
        market_features = 5
        position_features = trading_env.max_positions * 3
        return_features = 5
        current_factor_features = factor_dim
        position_factor_features = factor_dim
        market_factor_features = factor_dim * 4
        
        logger.info("状态向量组成分析:")
        logger.info(f"  市场特征: {market_features}")
        logger.info(f"  持仓特征: {position_features}")
        logger.info(f"  收益率特征: {return_features}")
        logger.info(f"  当前因子特征: {current_factor_features}")
        logger.info(f"  持仓因子特征: {position_factor_features}")
        logger.info(f"  市场因子统计特征: {market_factor_features}")
        logger.info(f"  总计: {market_features + position_features + return_features + current_factor_features + position_factor_features + market_factor_features}")
        
        # 测试状态向量的各个部分
        start_idx = 0
        
        # 市场特征
        market_part = initial_state[start_idx:start_idx + market_features]
        logger.info(f"市场特征部分: {market_part}")
        start_idx += market_features
        
        # 持仓特征
        position_part = initial_state[start_idx:start_idx + position_features]
        logger.info(f"持仓特征部分: {position_part}")
        start_idx += position_features
        
        # 收益率特征
        return_part = initial_state[start_idx:start_idx + return_features]
        logger.info(f"收益率特征部分: {return_part}")
        start_idx += return_features
        
        # 当前因子特征
        current_factor_part = initial_state[start_idx:start_idx + current_factor_features]
        logger.info(f"当前因子特征部分: {current_factor_part[:5]}... (显示前5个)")
        start_idx += current_factor_features
        
        # 持仓因子特征
        position_factor_part = initial_state[start_idx:start_idx + position_factor_features]
        logger.info(f"持仓因子特征部分: {position_factor_part[:5]}... (显示前5个)")
        start_idx += position_factor_features
        
        # 市场因子统计特征
        market_factor_part = initial_state[start_idx:start_idx + market_factor_features]
        logger.info(f"市场因子统计特征部分: {market_factor_part[:5]}... (显示前5个)")
        
        # 执行几步交易，观察状态变化
        logger.info("\n执行几步交易，观察状态变化...")
        for step in range(3):
            # 随机动作
            action = np.random.randn(trading_env.get_action_dim())
            next_state, reward, done, info = trading_env.step(action)
            
            logger.info(f"步骤 {step + 1}:")
            logger.info(f"  状态形状: {next_state.shape}")
            logger.info(f"  奖励: {reward:.6f}")
            logger.info(f"  组合价值: {info['portfolio_value']:.2f}")
            logger.info(f"  持仓数量: {info['positions']}")
            
            # 检查因子特征是否有变化
            current_factor_part_new = next_state[market_features + position_features + return_features:
                                                market_features + position_features + return_features + current_factor_features]
            factor_changed = not np.allclose(current_factor_part, current_factor_part_new, atol=1e-6)
            logger.info(f"  因子特征是否变化: {factor_changed}")
            
            if done:
                break
                
        logger.info("✅ 增强状态向量测试成功！")
        return True
        
    except Exception as e:
        logger.error(f"测试过程出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = test_enhanced_state()
    if success:
        print("\n🎉 增强状态向量测试成功！")
    else:
        print("\n❌ 增强状态向量测试失败！")
