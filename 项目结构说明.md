# 股票策略项目结构说明

本项目已重新组织为三个主要模块，每个模块包含相关的子功能：

## 1. 数据准备模块

### 1-1 最新数据下载
- `demo_precompute.py` - 数据预计算演示
- `test_data_paths.py` - 数据路径测试
- `debug_date_format.py` - 日期格式调试
- `data/` - 原始数据存储目录

### 1-2 个股因子生成
- `precompute_factors.py` - 因子预计算主程序
- `update_new_factors.py` - 新因子更新程序
- `demo_factors.py` - 因子计算演示
- `demo_new_factors.py` - 新因子演示
- `test_indicators.py` - 指标测试
- `test_new_indicators.py` - 新指标测试
- `verify_new_factors.py` - 新因子验证
- `fix_cache_files.py` - 缓存文件修复
- `indicators/` - 技术指标计算模块
- `factor_cache/` - 因子缓存数据

### 1-3 排序因子生成
- `calculate_factor_percentiles.py` - 因子百分比排序计算
- `calculate_factor_percentiles_fast.py` - 快速百分比排序计算
- `calculate_factor_percentiles_optimized.py` - 优化版百分比排序计算
- `verify_percentiles.py` - 百分比排序验证
- `check_factor_file.py` - 因子文件检查
- `check_percentile_progress.py` - 百分比排序进度检查
- `check_progress.py` - 进度检查工具

## 2. 训练与回测模块

### 2-1 数据提取
- `stock_env.py` - 股票环境类
- `stock_env_precomputed.py` - 预计算股票环境类
- `test_filename_format.py` - 文件名格式测试

### 2-2 模型训练
- `train.py` - 模型训练主程序
- `agent.py` - 智能体定义
- `config.py` - 配置文件
- `models/` - 训练好的模型存储
- `logs/` - 训练日志

### 2-3 回测
- `backtest.py` - 回测主程序
- `example.py` - 回测示例
- `results/` - 回测结果
- `reports/` - 回测报告

### 2-4 逐段回测
- 此目录预留给逐段回测功能的实现
- 用于实现滚动训练和回测的功能

## 3. 实盘交易提醒模块

- `main.py` - 主程序入口
- `Tools/` - 实用工具
  - `check_torch.py` - PyTorch环境检查

## 项目根目录文件

- `README.md` - 项目说明文档
- `PRECOMPUTE_GUIDE.md` - 预计算指南
- `requirements.txt` - 项目依赖
- `__pycache__/` - Python缓存文件
- `.idea/` - IDE配置文件

## 使用流程

1. **数据准备阶段**：
   - 运行 `1-数据准备/1-最新数据下载/` 中的脚本下载最新数据
   - 运行 `1-数据准备/2-个股因子生成/` 中的脚本生成个股因子
   - 运行 `1-数据准备/3-排序因子生成/` 中的脚本生成排序因子

2. **训练与回测阶段**：
   - 使用 `2-训练与回测/1-数据提取/` 中的环境类提取训练和测试数据
   - 运行 `2-训练与回测/2-模型训练/` 中的训练脚本
   - 使用 `2-训练与回测/3-回测/` 中的回测脚本进行策略验证
   - 实现 `2-训练与回测/4-逐段回测/` 中的滚动回测功能

3. **实盘交易阶段**：
   - 运行 `3-实盘交易提醒/main.py` 获取交易信号和提醒

## 注意事项

- 各模块之间通过数据文件进行交互
- 确保在运行后续模块前，前置模块的数据已准备完成
- 可以根据需要单独运行某个模块的功能