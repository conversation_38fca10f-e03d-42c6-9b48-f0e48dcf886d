# RLStrategy V2.1 - 强化学习量化交易框架

本项目是一个基于强化学习（SAC算法）的端到端量化交易框架。它整合了数据准备、模型训练、策略回测和实盘交易提醒的完整流程，旨在为量化研究者和开发者提供一个高效、灵活的实验平台。

## 核心特性

- **端到端工作流**: 提供从数据下载到实盘提醒的全套解决方案。
- **模块化设计**: 项目结构清晰，分为数据、训练、回测、实盘等独立模块，易于扩展和维护。
- **统一的执行入口**: 通过 `run_project.py` 脚本控制整个项目流程，操作便捷。
- **高性能因子计算**: 内置预计算因子系统，大幅提升数据加载和训练初始化速度。
- **环境兼容性**: 支持 GPU 和 CPU 环境，并提供详细的安装和检查指南。

## 项目结构

```
RLStrategy_V2.1（SAC算法）/
│
├── 0-数据汇总/              # 存放所有原始数据、中间数据和结果
├── 1-数据准备/              # 数据下载、清洗、因子计算和预处理
│   ├── 1-最新数据下载/
│   ├── 2-个股因子生成/
│   └── 3-排序因子生成/
├── 2-模型训练/              # 强化学习模型训练与评估
│   ├── 1-数据预处理/
│   ├── 2-模型构建/
│   └── 3-模型训练/
├── 3-策略回测/              # 策略回测引擎和结果分析
│   ├── 1-回测引擎/
│   └── 2-回测结果/
├── 4-实盘交易/              # 实盘交易接口与交易信号提醒
│
├── run_project.py           # 项目主控制脚本
├── requirements.txt         # GPU版本依赖
├── requirements-cpu.txt     # CPU版本依赖
├── INSTALL.md               # 详细安装指南
├── PRECOMPUTE_GUIDE.md      # 预计算因子系统指南
└── ...
```

## 快速开始

### 1. 环境安装

项目支持 GPU (推荐) 和 CPU 两种环境。详细的安装步骤、驱动要求和故障排除见 **[INSTALL.md](INSTALL.md)**。

**GPU 版本 (需 CUDA 12.1+)**
```bash
pip install -r requirements.txt
```

**CPU 版本**
```bash
pip install -r requirements-cpu.txt
```

### 2. 环境检查

安装完成后，运行以下脚本验证环境是否配置正确：

```bash
# 验证 PyTorch 和 CUDA 是否正常
python GPU环境检测.py

# 验证项目基本依赖和目录结构
python run_project.py --mode check
```

### 3. 运行项目

本项目通过 `run_project.py` 控制，支持多种运行模式。

**运行完整流程**

执行从数据准备到训练回测的完整流程。
```bash
python run_project.py --mode all
```

**分步执行**

您也可以根据需要只运行特定的模块：

- **仅准备数据**:
  ```bash
  python run_project.py --mode data
  ```
- **仅训练和回测**:
  ```bash
  python run_project.py --mode train
  ```
- **仅运行滚动回测**:
  ```bash
  python run_project.py --mode rolling
  ```
- **仅运行实盘交易提醒**:
  ```bash
  python run_project.py --mode trade
  ```

## 工作流详解

### 1. 数据准备 (`1-数据准备`)

此阶段负责准备模型所需的全部数据。
- **`1-最新数据下载`**: 下载最新的股票行情数据。
- **`2-个股因子生成`**: 计算个股的技术指标和因子。
- **`3-排序因子生成`**: 对因子进行截面排序，生成排序因子。

此模块支持GUI操作，可运行 `python 1-数据准备/run_gui.py` 启动图形化界面。

### 2. 预计算因子系统

为了提升性能，项目包含一个强大的预计算因子系统。它能将常用的技术指标缓存到磁盘，使训练环境的启动时间从数十秒缩短到几秒。

首次使用或数据更新后，建议重新生成预计算文件。详细用法请参考 **[PRECOMPUTE_GUIDE.md](PRECOMPUTE_GUIDE.md)**。

### 3. 模型训练与回测 (`2-模型训练` & `3-策略回测`)

此阶段负责训练强化学习智能体并评估其表现。
- **`run_training_pipeline.py`**: 核心训练脚本，整合了数据加载、模型训练和初步回测。
- **`rolling_backtest.py`**: 执行更严格的滚动回测，模拟真实场景下的策略表现。

### 4. 实盘交易 (`4-实盘交易`)

此模块负责接收最新行情，加载已训练好的模型，并生成交易信号提醒。

## 辅助工具

- **`验证完整环境.py`**: 全面检查项目运行所需的所有环境和依赖。
- **`check_new_pkl.py`**: 检查是否有新的因子文件生成。
- **`data_manager.py`**: 位于 `0-数据汇总` 目录，用于统一管理项目数据。

## 贡献

欢迎对项目提出改进意见或贡献代码。您可以：
1.  Fork 本仓库。
2.  创建新的功能分支 (`git checkout -b feature/AmazingFeature`)。
3.  提交您的更改 (`git commit -m 'Add some AmazingFeature'`)。
4.  推送至分支 (`git push origin feature/AmazingFeature`)。
5.  提交 Pull Request。

## 许可证

本项目采用 [MIT](LICENSE) 许可证。
