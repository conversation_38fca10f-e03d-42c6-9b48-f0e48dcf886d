"""
因子计算器主类

提供统一的接口来计算各种技术指标和因子
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any
import warnings
from concurrent.futures import ThreadPoolExecutor, as_completed

from .technical_indicators import (
    RSI, MACD, KDJ, BollingerBands, CCI, WilliamsR,
    ATR, OBV, VWAP, DMI, TurnoverStd, ReturnStd120
)
# 基本面指标已从原始数据中直接提取，不再需要计算
# from .fundamental_indicators import (
#     TurnoverRate, PETTM, PSTTM, PCFNCFTTM, PBMRQ
# )
from .utils import validate_data, normalize_data, handle_missing_values

warnings.filterwarnings('ignore')


class FactorCalculator:
    """
    股票因子计算器
    
    提供统一的接口来计算各种技术指标和因子，支持：
    - 单股票和多股票批量计算
    - 缓存机制提高效率
    - 并行计算支持
    - 灵活的参数配置
    """
    
    def __init__(self, enable_cache: bool = True, max_workers: int = 4):
        """
        初始化因子计算器
        
        Args:
            enable_cache: 是否启用缓存
            max_workers: 并行计算的最大工作线程数
        """
        self.enable_cache = enable_cache
        self.max_workers = max_workers
        
        # 初始化所有指标计算器
        self.indicators = {
            # 技术指标
            'RSI': RSI(),
            'MACD': MACD(),
            'KDJ': KDJ(),
            'BOLL': BollingerBands(),
            'CCI': CCI(),
            'WR': WilliamsR(),
            'ATR': ATR(),
            'OBV': OBV(),
            'VWAP': VWAP(),
            'DMI': DMI(),
            # 统计指标
            'turnover_std20': TurnoverStd(),
            'ReturnStd120': ReturnStd120()
            # 基本面指标已从原始数据中直接提取，不再需要计算
            # 'turn': TurnoverRate(),
            # 'peTTM': PETTM(),
            # 'psTTM': PSTTM(),
            # 'pcfNcfTTM': PCFNCFTTM(),
            # 'pbMRQ': PBMRQ()
        }
        
        # 默认参数配置
        self.default_params = {
            # 技术指标参数
            'RSI': {'window': 14},
            'MACD': {'fast_period': 12, 'slow_period': 26, 'signal_period': 9},
            'KDJ': {'k_period': 9, 'd_period': 3, 'j_factor': 3},
            'BOLL': {'window': 20, 'std_dev': 2.0},
            'CCI': {'window': 20},
            'WR': {'window': 14},
            'ATR': {'window': 14},
            'OBV': {},
            'VWAP': {'window': None},
            'DMI': {'window': 14},
            # 统计指标参数
            'turnover_std20': {'window': 20, 'turnover_col': 'turn'},
            'ReturnStd120': {'window': 120, 'price_col': 'Close'}
            # 基本面指标参数已移除，这些指标从原始数据中直接提取
            # 'turn': {'turnover_col': 'turn'},
            # 'peTTM': {'pe_col': 'peTTM'},
            # 'psTTM': {'ps_col': 'psTTM'},
            # 'pcfNcfTTM': {'pcf_col': 'pcfNcfTTM'},
            # 'pbMRQ': {'pb_col': 'pbMRQ'}
        }
    
    def calculate_single_indicator(self, data: pd.DataFrame, indicator_name: str, 
                                 params: Optional[Dict] = None) -> Union[pd.Series, Dict[str, pd.Series]]:
        """
        计算单个指标
        
        Args:
            data: 股票数据
            indicator_name: 指标名称
            params: 指标参数
        
        Returns:
            指标计算结果
        """
        if indicator_name not in self.indicators:
            raise ValueError(f"不支持的指标: {indicator_name}")
        
        # 验证数据
        validate_data(data)
        
        # 获取参数
        if params is None:
            params = self.default_params.get(indicator_name, {})
        
        # 计算指标
        indicator = self.indicators[indicator_name]
        result = indicator.calculate(data, **params)
        
        return result
    
    def calculate_all_indicators(self, data: pd.DataFrame, 
                               custom_params: Optional[Dict[str, Dict]] = None) -> Dict[str, Any]:
        """
        计算所有指标
        
        Args:
            data: 股票数据
            custom_params: 自定义参数字典
        
        Returns:
            所有指标的计算结果
        """
        validate_data(data)
        
        results = {}
        
        for indicator_name in self.indicators.keys():
            try:
                # 获取参数
                params = self.default_params.get(indicator_name, {})
                if custom_params and indicator_name in custom_params:
                    params.update(custom_params[indicator_name])
                
                # 计算指标
                result = self.calculate_single_indicator(data, indicator_name, params)
                results[indicator_name] = result
                
            except Exception as e:
                print(f"计算指标 {indicator_name} 时出错: {e}")
                results[indicator_name] = None
        
        return results
    
    def calculate_batch(self, data_dict: Dict[str, pd.DataFrame], 
                       indicators: Optional[List[str]] = None,
                       custom_params: Optional[Dict[str, Dict]] = None) -> Dict[str, Dict[str, Any]]:
        """
        批量计算多个股票的指标
        
        Args:
            data_dict: 股票数据字典 {股票代码: DataFrame}
            indicators: 要计算的指标列表，None表示计算所有指标
            custom_params: 自定义参数字典
        
        Returns:
            批量计算结果 {股票代码: {指标名: 结果}}
        """
        if indicators is None:
            indicators = list(self.indicators.keys())
        
        results = {}
        
        if self.max_workers == 1:
            # 单线程计算
            for stock_code, stock_data in data_dict.items():
                results[stock_code] = self._calculate_stock_indicators(
                    stock_data, indicators, custom_params
                )
        else:
            # 多线程并行计算
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                future_to_stock = {
                    executor.submit(
                        self._calculate_stock_indicators, 
                        stock_data, indicators, custom_params
                    ): stock_code
                    for stock_code, stock_data in data_dict.items()
                }
                
                for future in as_completed(future_to_stock):
                    stock_code = future_to_stock[future]
                    try:
                        results[stock_code] = future.result()
                    except Exception as e:
                        print(f"计算股票 {stock_code} 指标时出错: {e}")
                        results[stock_code] = {}
        
        return results
    
    def _calculate_stock_indicators(self, data: pd.DataFrame, indicators: List[str],
                                  custom_params: Optional[Dict[str, Dict]] = None) -> Dict[str, Any]:
        """计算单个股票的指标"""
        results = {}
        
        for indicator_name in indicators:
            try:
                # 获取参数
                params = self.default_params.get(indicator_name, {})
                if custom_params and indicator_name in custom_params:
                    params.update(custom_params[indicator_name])
                
                # 计算指标
                result = self.calculate_single_indicator(data, indicator_name, params)
                results[indicator_name] = result
                
            except Exception as e:
                print(f"计算指标 {indicator_name} 时出错: {e}")
                results[indicator_name] = None
        
        return results
    
    def create_feature_matrix(self, data_dict: Dict[str, pd.DataFrame],
                            indicators: Optional[List[str]] = None,
                            normalize: bool = True,
                            handle_missing: str = 'forward_fill') -> pd.DataFrame:
        """
        创建特征矩阵用于机器学习
        
        Args:
            data_dict: 股票数据字典
            indicators: 要包含的指标列表
            normalize: 是否标准化
            handle_missing: 缺失值处理方法
        
        Returns:
            特征矩阵DataFrame
        """
        # 批量计算指标
        all_results = self.calculate_batch(data_dict, indicators)
        
        # 构建特征矩阵
        feature_list = []
        
        for stock_code, stock_results in all_results.items():
            for indicator_name, indicator_result in stock_results.items():
                if indicator_result is None:
                    continue
                
                if isinstance(indicator_result, dict):
                    # 多列指标（如MACD、KDJ等）
                    for sub_name, sub_series in indicator_result.items():
                        if isinstance(sub_series, pd.Series):
                            feature_series = sub_series.copy()
                            feature_series.name = f"{stock_code}_{indicator_name}_{sub_name}"
                            feature_list.append(feature_series)
                elif isinstance(indicator_result, pd.Series):
                    # 单列指标
                    feature_series = indicator_result.copy()
                    feature_series.name = f"{stock_code}_{indicator_name}"
                    feature_list.append(feature_series)
        
        if not feature_list:
            raise ValueError("没有有效的特征数据")
        
        # 合并所有特征
        feature_matrix = pd.concat(feature_list, axis=1)
        
        # 处理缺失值
        if handle_missing:
            feature_matrix = handle_missing_values(feature_matrix, handle_missing)
        
        # 标准化
        if normalize:
            feature_matrix = normalize_data(feature_matrix, method='zscore')
        
        return feature_matrix
    
    def get_latest_values(self, data_dict: Dict[str, pd.DataFrame],
                         indicators: Optional[List[str]] = None) -> Dict[str, Dict[str, float]]:
        """
        获取最新的指标值
        
        Args:
            data_dict: 股票数据字典
            indicators: 要获取的指标列表
        
        Returns:
            最新指标值字典 {股票代码: {指标名: 最新值}}
        """
        all_results = self.calculate_batch(data_dict, indicators)
        
        latest_values = {}
        
        for stock_code, stock_results in all_results.items():
            latest_values[stock_code] = {}
            
            for indicator_name, indicator_result in stock_results.items():
                if indicator_result is None:
                    continue
                
                if isinstance(indicator_result, dict):
                    # 多列指标
                    for sub_name, sub_series in indicator_result.items():
                        if isinstance(sub_series, pd.Series) and not sub_series.empty:
                            latest_values[stock_code][f"{indicator_name}_{sub_name}"] = sub_series.iloc[-1]
                elif isinstance(indicator_result, pd.Series) and not indicator_result.empty:
                    # 单列指标
                    latest_values[stock_code][indicator_name] = indicator_result.iloc[-1]
        
        return latest_values
    
    def clear_cache(self):
        """清空所有指标的缓存"""
        for indicator in self.indicators.values():
            indicator.clear_cache()
    
    def add_custom_indicator(self, name: str, indicator_class, default_params: Dict = None):
        """
        添加自定义指标
        
        Args:
            name: 指标名称
            indicator_class: 指标类（继承自BaseIndicator）
            default_params: 默认参数
        """
        self.indicators[name] = indicator_class()
        if default_params:
            self.default_params[name] = default_params
