"""
基础指标计算类

提供所有技术指标的基础功能和通用方法
"""

import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Union, Optional, Dict, Any
import warnings

warnings.filterwarnings('ignore')


class BaseIndicator(ABC):
    """技术指标基础类"""
    
    def __init__(self, name: str):
        self.name = name
        self._cache = {}
    
    @abstractmethod
    def calculate(self, data: pd.DataFrame, **kwargs) -> Union[pd.Series, pd.DataFrame]:
        """计算指标的抽象方法"""
        pass
    
    def _validate_data(self, data: pd.DataFrame, required_columns: list) -> bool:
        """验证数据是否包含必要的列"""
        missing_cols = [col for col in required_columns if col not in data.columns]
        if missing_cols:
            raise ValueError(f"数据缺少必要的列: {missing_cols}")
        return True
    
    def _get_cache_key(self, data: pd.DataFrame, **kwargs) -> str:
        """生成缓存键"""
        data_hash = hash(str(data.index.tolist() + data.columns.tolist()))
        params_hash = hash(str(sorted(kwargs.items())))
        return f"{self.name}_{data_hash}_{params_hash}"
    
    def _use_cache(self, data: pd.DataFrame, **kwargs) -> Optional[Union[pd.Series, pd.DataFrame]]:
        """尝试使用缓存"""
        cache_key = self._get_cache_key(data, **kwargs)
        return self._cache.get(cache_key)
    
    def _set_cache(self, data: pd.DataFrame, result: Union[pd.Series, pd.DataFrame], **kwargs):
        """设置缓存"""
        cache_key = self._get_cache_key(data, **kwargs)
        self._cache[cache_key] = result
    
    def clear_cache(self):
        """清空缓存"""
        self._cache.clear()


class MovingAverage:
    """移动平均线工具类"""
    
    @staticmethod
    def sma(data: pd.Series, window: int) -> pd.Series:
        """简单移动平均"""
        return data.rolling(window=window).mean()
    
    @staticmethod
    def ema(data: pd.Series, window: int, alpha: Optional[float] = None) -> pd.Series:
        """指数移动平均"""
        if alpha is None:
            alpha = 2.0 / (window + 1)
        return data.ewm(alpha=alpha, adjust=False).mean()
    
    @staticmethod
    def wma(data: pd.Series, window: int) -> pd.Series:
        """加权移动平均"""
        weights = np.arange(1, window + 1)
        return data.rolling(window=window).apply(
            lambda x: np.dot(x, weights) / weights.sum(), raw=True
        )


class PriceTransforms:
    """价格变换工具类"""
    
    @staticmethod
    def typical_price(high: pd.Series, low: pd.Series, close: pd.Series) -> pd.Series:
        """典型价格 (H+L+C)/3"""
        return (high + low + close) / 3
    
    @staticmethod
    def weighted_close(high: pd.Series, low: pd.Series, close: pd.Series) -> pd.Series:
        """加权收盘价 (H+L+2*C)/4"""
        return (high + low + 2 * close) / 4
    
    @staticmethod
    def true_range(high: pd.Series, low: pd.Series, close: pd.Series) -> pd.Series:
        """真实波动幅度"""
        prev_close = close.shift(1)
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        return pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)


class StatisticalFunctions:
    """统计函数工具类"""
    
    @staticmethod
    def rolling_std(data: pd.Series, window: int) -> pd.Series:
        """滚动标准差"""
        return data.rolling(window=window).std()
    
    @staticmethod
    def rolling_var(data: pd.Series, window: int) -> pd.Series:
        """滚动方差"""
        return data.rolling(window=window).var()
    
    @staticmethod
    def rolling_skew(data: pd.Series, window: int) -> pd.Series:
        """滚动偏度"""
        return data.rolling(window=window).skew()
    
    @staticmethod
    def rolling_kurt(data: pd.Series, window: int) -> pd.Series:
        """滚动峰度"""
        return data.rolling(window=window).kurt()
    
    @staticmethod
    def percentile_rank(data: pd.Series, window: int) -> pd.Series:
        """百分位排名"""
        return data.rolling(window=window).rank(pct=True)


class OscillatorUtils:
    """振荡器工具类"""
    
    @staticmethod
    def stochastic_k(high: pd.Series, low: pd.Series, close: pd.Series, window: int) -> pd.Series:
        """随机指标K值"""
        lowest_low = low.rolling(window=window).min()
        highest_high = high.rolling(window=window).max()
        k_percent = 100 * (close - lowest_low) / (highest_high - lowest_low)
        return k_percent
    
    @staticmethod
    def williams_r(high: pd.Series, low: pd.Series, close: pd.Series, window: int) -> pd.Series:
        """威廉指标"""
        highest_high = high.rolling(window=window).max()
        lowest_low = low.rolling(window=window).min()
        wr = -100 * (highest_high - close) / (highest_high - lowest_low)
        return wr
    
    @staticmethod
    def rsi_calculation(data: pd.Series, window: int) -> pd.Series:
        """RSI计算"""
        delta = data.diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        
        avg_gain = gain.rolling(window=window).mean()
        avg_loss = loss.rolling(window=window).mean()
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
