"""
工具函数模块

提供数据验证、标准化、缓存等通用功能
"""

import numpy as np
import pandas as pd
from typing import Union, List, Dict, Optional, Tuple
import warnings

warnings.filterwarnings('ignore')


def validate_data(data: pd.DataFrame, required_columns: List[str] = None) -> bool:
    """
    验证数据格式和完整性
    
    Args:
        data: 股票数据DataFrame
        required_columns: 必需的列名列表
    
    Returns:
        bool: 验证是否通过
    
    Raises:
        ValueError: 数据验证失败时抛出异常
    """
    if data is None or data.empty:
        raise ValueError("数据为空")
    
    # 默认必需列
    if required_columns is None:
        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
    
    # 检查必需列
    missing_columns = [col for col in required_columns if col not in data.columns]
    if missing_columns:
        raise ValueError(f"缺少必需的列: {missing_columns}")
    
    # 检查数据类型
    numeric_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
    for col in numeric_columns:
        if col in data.columns and not pd.api.types.is_numeric_dtype(data[col]):
            raise ValueError(f"列 {col} 必须是数值类型")
    
    # 检查价格逻辑
    if all(col in data.columns for col in ['High', 'Low', 'Close']):
        invalid_prices = (data['High'] < data['Low']) | (data['Close'] > data['High']) | (data['Close'] < data['Low'])
        if invalid_prices.any():
            warnings.warn("发现不合理的价格数据")
    
    # 检查成交量
    if 'Volume' in data.columns:
        negative_volume = data['Volume'] < 0
        if negative_volume.any():
            warnings.warn("发现负成交量数据")
    
    return True


def normalize_data(data: Union[pd.Series, pd.DataFrame], method: str = 'zscore') -> Union[pd.Series, pd.DataFrame]:
    """
    数据标准化
    
    Args:
        data: 需要标准化的数据
        method: 标准化方法 ('zscore', 'minmax', 'robust')
    
    Returns:
        标准化后的数据
    """
    if method == 'zscore':
        # Z-score标准化
        return (data - data.mean()) / (data.std() + 1e-8)
    
    elif method == 'minmax':
        # Min-Max标准化
        return (data - data.min()) / (data.max() - data.min() + 1e-8)
    
    elif method == 'robust':
        # 鲁棒标准化（使用中位数和四分位距）
        median = data.median()
        q75 = data.quantile(0.75)
        q25 = data.quantile(0.25)
        iqr = q75 - q25
        return (data - median) / (iqr + 1e-8)
    
    else:
        raise ValueError(f"不支持的标准化方法: {method}")


def handle_missing_values(data: pd.DataFrame, method: str = 'forward_fill') -> pd.DataFrame:
    """
    处理缺失值
    
    Args:
        data: 包含缺失值的数据
        method: 处理方法 ('forward_fill', 'backward_fill', 'interpolate', 'drop')
    
    Returns:
        处理后的数据
    """
    if method == 'forward_fill':
        return data.fillna(method='ffill')
    
    elif method == 'backward_fill':
        return data.fillna(method='bfill')
    
    elif method == 'interpolate':
        return data.interpolate()
    
    elif method == 'drop':
        return data.dropna()
    
    else:
        raise ValueError(f"不支持的缺失值处理方法: {method}")


def calculate_returns(prices: pd.Series, method: str = 'simple') -> pd.Series:
    """
    计算收益率
    
    Args:
        prices: 价格序列
        method: 计算方法 ('simple', 'log')
    
    Returns:
        收益率序列
    """
    if method == 'simple':
        return prices.pct_change()
    elif method == 'log':
        return np.log(prices / prices.shift(1))
    else:
        raise ValueError(f"不支持的收益率计算方法: {method}")


def rolling_correlation(x: pd.Series, y: pd.Series, window: int) -> pd.Series:
    """
    计算滚动相关系数
    
    Args:
        x, y: 两个时间序列
        window: 滚动窗口大小
    
    Returns:
        滚动相关系数序列
    """
    return x.rolling(window=window).corr(y)


def rolling_beta(stock_returns: pd.Series, market_returns: pd.Series, window: int) -> pd.Series:
    """
    计算滚动Beta系数
    
    Args:
        stock_returns: 股票收益率
        market_returns: 市场收益率
        window: 滚动窗口大小
    
    Returns:
        滚动Beta系数序列
    """
    covariance = stock_returns.rolling(window=window).cov(market_returns)
    market_variance = market_returns.rolling(window=window).var()
    return covariance / market_variance


def detect_outliers(data: pd.Series, method: str = 'iqr', threshold: float = 1.5) -> pd.Series:
    """
    检测异常值
    
    Args:
        data: 数据序列
        method: 检测方法 ('iqr', 'zscore')
        threshold: 阈值
    
    Returns:
        布尔序列，True表示异常值
    """
    if method == 'iqr':
        q1 = data.quantile(0.25)
        q3 = data.quantile(0.75)
        iqr = q3 - q1
        lower_bound = q1 - threshold * iqr
        upper_bound = q3 + threshold * iqr
        return (data < lower_bound) | (data > upper_bound)
    
    elif method == 'zscore':
        z_scores = np.abs((data - data.mean()) / data.std())
        return z_scores > threshold
    
    else:
        raise ValueError(f"不支持的异常值检测方法: {method}")


def smooth_series(data: pd.Series, method: str = 'ema', window: int = 5, alpha: float = 0.3) -> pd.Series:
    """
    平滑时间序列
    
    Args:
        data: 原始数据
        method: 平滑方法 ('sma', 'ema', 'savgol')
        window: 窗口大小
        alpha: EMA的平滑参数
    
    Returns:
        平滑后的序列
    """
    if method == 'sma':
        return data.rolling(window=window).mean()
    
    elif method == 'ema':
        return data.ewm(alpha=alpha).mean()
    
    elif method == 'savgol':
        from scipy.signal import savgol_filter
        return pd.Series(savgol_filter(data, window, 3), index=data.index)
    
    else:
        raise ValueError(f"不支持的平滑方法: {method}")


def create_feature_matrix(data: Dict[str, pd.DataFrame], feature_columns: List[str]) -> pd.DataFrame:
    """
    创建特征矩阵
    
    Args:
        data: 股票数据字典 {股票代码: DataFrame}
        feature_columns: 特征列名列表
    
    Returns:
        特征矩阵DataFrame
    """
    feature_matrix = []
    
    for stock_code, stock_data in data.items():
        for col in feature_columns:
            if col in stock_data.columns:
                feature_series = stock_data[col].copy()
                feature_series.name = f"{stock_code}_{col}"
                feature_matrix.append(feature_series)
    
    return pd.concat(feature_matrix, axis=1)


def calculate_factor_exposure(returns: pd.DataFrame, factors: pd.DataFrame) -> pd.DataFrame:
    """
    计算因子暴露度
    
    Args:
        returns: 股票收益率矩阵
        factors: 因子收益率矩阵
    
    Returns:
        因子暴露度矩阵
    """
    from sklearn.linear_model import LinearRegression
    
    exposures = []
    
    for stock in returns.columns:
        stock_returns = returns[stock].dropna()
        aligned_factors = factors.loc[stock_returns.index].dropna()
        
        if len(aligned_factors) > 0:
            reg = LinearRegression()
            reg.fit(aligned_factors, stock_returns.loc[aligned_factors.index])
            exposures.append(pd.Series(reg.coef_, index=aligned_factors.columns, name=stock))
    
    return pd.concat(exposures, axis=1).T
