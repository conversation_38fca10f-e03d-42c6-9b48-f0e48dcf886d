"""
预计算因子管理器

提供因子预计算、增量更新、缓存管理等功能
"""

import os
import pickle
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import warnings
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import hashlib
import json
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from .factor_calculator import FactorCalculator
from .utils import validate_data, handle_missing_values

try:
    from config import get_data_dir, get_cache_dir, find_stock_file, PRECOMPUTE_CONFIG
except ImportError:
    # 如果配置文件不存在，使用默认值
    def get_data_dir():
        return "../日线数据"
    def get_cache_dir():
        # 使用项目根目录下的正确路径
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        return os.path.join(project_root, "0-数据汇总", "2-个股因子数据")
    def find_stock_file(stock_code, data_dir=None):
        return None
    PRECOMPUTE_CONFIG = {"default_max_workers": 4}

warnings.filterwarnings('ignore')


class PrecomputeManager:
    """
    预计算因子管理器
    
    功能：
    - 批量预计算股票因子
    - 增量更新机制
    - 因子缓存管理
    - 快速数据加载
    """
    
    def __init__(self,
                 cache_dir: str = None,
                 data_dir: str = None,
                 max_workers: int = 4,
                 chunk_size: int = 1000):
        """
        初始化预计算管理器
        
        Args:
            cache_dir: 缓存目录
            data_dir: 原始数据目录
            max_workers: 并行工作线程数
            chunk_size: 数据分块大小
        """
        self.cache_dir = cache_dir or get_cache_dir()
        self.data_dir = data_dir or get_data_dir()
        self.max_workers = max_workers
        self.chunk_size = chunk_size
        
        # 创建缓存目录
        os.makedirs(self.cache_dir, exist_ok=True)
        os.makedirs(os.path.join(self.cache_dir, "metadata"), exist_ok=True)
        
        # 初始化因子计算器
        self.calculator = FactorCalculator(enable_cache=True, max_workers=max_workers)
        
        # 元数据文件路径
        self.metadata_file = os.path.join(self.cache_dir, "metadata", "cache_info.json")
        
        # 加载元数据
        self.metadata = self._load_metadata()
    
    def _load_metadata(self) -> Dict:
        """加载缓存元数据"""
        if os.path.exists(self.metadata_file):
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载元数据失败: {e}")
        
        return {
            "stocks": {},
            "last_update": None,
            "indicators": [],
            "version": "1.0"
        }
    
    def _save_metadata(self):
        """保存缓存元数据"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, indent=2, ensure_ascii=False, default=str)
        except Exception as e:
            print(f"保存元数据失败: {e}")
    
    def _get_stock_cache_path(self, stock_code: str) -> str:
        """获取股票因子缓存文件路径"""
        # 标准化股票代码格式
        if '.' in stock_code:
            # 如果已经包含市场前缀，直接使用
            filename = f"{stock_code.lower()}_factors.pkl"
        else:
            # 如果没有市场前缀，添加市场前缀
            market_code = self._get_market_code(stock_code)
            filename = f"{market_code.lower()}.{stock_code}_factors.pkl"

        # cache_dir已经包含factors目录，不需要再添加
        return os.path.join(self.cache_dir, filename)

    def _get_market_code(self, stock_code: str) -> str:
        """根据股票代码获取市场代码"""
        if not stock_code:
            return "SZ"

        # 如果已经包含市场前缀，直接返回
        if '.' in stock_code:
            return stock_code.split('.')[0].upper()

        # 根据股票代码首位数字判断市场
        first_digit = stock_code[0]
        market_mapping = {
            "0": "SZ",  # 深圳主板
            "3": "SZ",  # 创业板
            "6": "SH",  # 上海主板
            "8": "BJ",  # 北京交易所
            "4": "SZ",  # 深圳B股
            "9": "SH"   # 上海B股
        }
        return market_mapping.get(first_digit, "SZ")
    
    def _get_data_hash(self, data: pd.DataFrame) -> str:
        """计算数据哈希值"""
        data_str = f"{len(data)}_{data.index[0]}_{data.index[-1]}_{data.iloc[-1].sum()}"
        return hashlib.md5(data_str.encode()).hexdigest()
    
    def load_stock_data(self, stock_code: str) -> Optional[pd.DataFrame]:
        """
        加载单个股票的原始数据

        Args:
            stock_code: 股票代码 (支持格式: "000001" 或 "SZ.000001")

        Returns:
            股票数据DataFrame
        """
        try:
            # 标准化股票代码 - 移除市场前缀用于文件查找
            clean_stock_code = stock_code
            if '.' in stock_code:
                clean_stock_code = stock_code.split('.')[1]

            # 获取市场代码
            market_code = self._get_market_code(clean_stock_code)

            # 支持多种文件名格式 (按优先级排序，CSV格式优先)
            possible_filenames = [
                f"{market_code.lower()}.{clean_stock_code}_hfq.csv",   # sz.000001_hfq.csv (CSV格式)
                f"{market_code}.{clean_stock_code}_hfq.csv",           # SZ.000001_hfq.csv (CSV格式大写)
                f"{clean_stock_code}_hfq.csv",                         # 000001_hfq.csv (CSV格式)
                f"{market_code.lower()}.{clean_stock_code}_hfq.txt",   # sz.000001_hfq.txt (TXT格式)
                f"{clean_stock_code}_hfq.txt",                         # 000001_hfq.txt (TXT格式)
                f"{market_code}.{clean_stock_code}_hfq.txt",           # SZ.000001_hfq.txt (TXT格式大写)
                f"{market_code}.{clean_stock_code}__hfq.txt",          # SZ.000001__hfq.txt (兼容旧格式)
                f"{market_code.lower()}.{clean_stock_code}__hfq.txt",  # sz.000001__hfq.txt (兼容旧格式)
                f"{clean_stock_code}__hfq.txt",                        # 000001__hfq.txt (兼容旧格式)
            ]

            # 使用配置文件的查找函数
            filepath = find_stock_file(clean_stock_code, self.data_dir)

            if filepath is None:
                # 回退到原来的查找方法
                for filename in possible_filenames:
                    test_path = os.path.join(self.data_dir, filename)
                    if os.path.exists(test_path):
                        filepath = test_path
                        break

            if filepath is None:
                print(f"未找到股票 {stock_code} 的数据文件")
                print(f"数据目录: {self.data_dir}")
                print(f"尝试的文件名: {possible_filenames}")
                return None
            
            # 读取数据
            df = pd.read_csv(filepath)
            
            # 标准化列名
            column_mapping = {
                'date': 'Date',
                'open': 'Open', 
                'high': 'High',
                'low': 'Low', 
                'close': 'Close',
                'volume': 'Volume'
            }
            df = df.rename(columns=column_mapping)
            
            # 转换日期格式
            if 'Date' in df.columns:
                df['Date'] = pd.to_datetime(df['Date'])
                df.set_index('Date', inplace=True)
            
            # 过滤停牌数据
            if 'tradestatus' in df.columns:
                df = df[df['tradestatus'] == 1]
            
            # 确保必要列存在
            required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
            if not all(col in df.columns for col in required_cols):
                print(f"股票 {stock_code} 缺少必要数据列")
                return None
            
            # 包含所有可用的列，不仅仅是必要列
            # 这样可以保留换手率(turn)等其他有用的列
            available_cols = required_cols.copy()
            
            # 添加其他常用列（如果存在）
            optional_cols = ['turn', 'pctChg', 'amount', 'tradestatus', 'peTTM', 'psTTM', 'pcfNcfTTM', 'pbMRQ', 'isST', 'preclose', 'adjustflag']
            for col in optional_cols:
                if col in df.columns:
                    available_cols.append(col)
            
            return df[available_cols].sort_index()
            
        except Exception as e:
            print(f"加载股票 {stock_code} 数据失败: {e}")
            return None
    
    def precompute_stock_factors(self, 
                                stock_code: str, 
                                indicators: Optional[List[str]] = None,
                                force_update: bool = False) -> bool:
        """
        预计算单个股票的因子
        
        Args:
            stock_code: 股票代码
            indicators: 要计算的指标列表
            force_update: 是否强制更新
            
        Returns:
            是否成功
        """
        try:
            # 加载原始数据
            data = self.load_stock_data(stock_code)
            if data is None:
                return False
            
            # 检查是否需要更新
            cache_path = self._get_stock_cache_path(stock_code)
            data_hash = self._get_data_hash(data)
            
            # 检查是否有新的指标需要计算
            need_update = force_update
            if not force_update and os.path.exists(cache_path):
                try:
                    # 加载现有因子数据
                    with open(cache_path, 'rb') as f:
                        existing_factors_df = pickle.load(f)
                    existing_factors = set(existing_factors_df.columns)
                    
                    # 检查是否有新指标需要计算
                    if indicators is None:
                        indicators = list(self.calculator.indicators.keys())
                    
                    # 展开多列指标名称
                    required_factors = set()
                    for indicator in indicators:
                        if indicator in ['DMI', 'MACD', 'KDJ', 'BOLL']:
                            if indicator == 'DMI':
                                required_factors.update(['DMI_PDI', 'DMI_MDI', 'DMI_ADX'])
                            elif indicator == 'MACD':
                                required_factors.update(['MACD_MACD', 'MACD_Signal', 'MACD_Histogram'])
                            elif indicator == 'KDJ':
                                required_factors.update(['KDJ_K', 'KDJ_D', 'KDJ_J'])
                            elif indicator == 'BOLL':
                                required_factors.update(['BOLL_Upper', 'BOLL_Middle', 'BOLL_Lower'])
                        else:
                            required_factors.add(indicator)
                    
                    missing_factors = required_factors - existing_factors
                    if not missing_factors:
                        print(f"股票 {stock_code} 所需因子已存在，跳过计算")
                        print(f"  - 现有因子: {sorted(existing_factors)}")
                        print(f"  - 所需因子: {sorted(required_factors)}")
                        return True
                    else:
                        print(f"股票 {stock_code} 需要计算新因子: {sorted(missing_factors)}")
                        need_update = True
                except Exception as e:
                    print(f"检查现有因子失败: {e}，将重新计算")
                    need_update = True
            else:
                need_update = True
            
            if not need_update:
                return True
            
            print(f"开始计算股票 {stock_code} 的因子...")
            
            # 计算因子
            if indicators is None:
                indicators = list(self.calculator.indicators.keys())
            
            factors_result = self.calculator.calculate_batch(
                {stock_code: data}, 
                indicators
            )[stock_code]
            
            # 构建完整因子DataFrame（包含基础数据，用于显示和统计）
            full_factor_data = {'Close': data['Close'], 'Volume': data['Volume']}
            
            # 构建纯因子DataFrame（只包含计算出的因子，用于保存）
            pure_factor_data = {}
            
            for indicator_name, indicator_result in factors_result.items():
                if indicator_result is None:
                    continue
                
                if isinstance(indicator_result, dict):
                    # 多列指标
                    for sub_name, sub_series in indicator_result.items():
                        if isinstance(sub_series, pd.Series):
                            factor_name = f"{indicator_name}_{sub_name}"
                            full_factor_data[factor_name] = sub_series
                            pure_factor_data[factor_name] = sub_series
                elif isinstance(indicator_result, pd.Series):
                    # 单列指标
                    full_factor_data[indicator_name] = indicator_result
                    pure_factor_data[indicator_name] = indicator_result
            
            # 创建完整因子DataFrame（用于显示）
            factors_df = pd.DataFrame(full_factor_data, index=data.index)
            
            # 创建纯因子DataFrame（用于保存）
            pure_factors_df = pd.DataFrame(pure_factor_data, index=data.index)
            
            # 处理缺失值
            factors_df = handle_missing_values(factors_df, method='forward_fill')
            pure_factors_df = handle_missing_values(pure_factors_df, method='forward_fill')
            
            # 加载现有的因子数据（如果存在）
            existing_factors_df = None
            if os.path.exists(cache_path) and not force_update:
                try:
                    with open(cache_path, 'rb') as f:
                        existing_factors_df = pickle.load(f)
                    print(f"加载现有因子数据，包含 {len(existing_factors_df.columns)} 个因子")
                except Exception as e:
                    print(f"加载现有因子数据失败: {e}，将重新创建")
                    existing_factors_df = None
            
            # 合并现有因子和新计算的因子
            if existing_factors_df is not None:
                # 将新因子添加到现有因子中
                for col in pure_factors_df.columns:
                    existing_factors_df[col] = pure_factors_df[col]
                final_factors_df = existing_factors_df
                print(f"合并后包含 {len(final_factors_df.columns)} 个因子")
            else:
                final_factors_df = pure_factors_df
                print(f"创建新的因子文件，包含 {len(final_factors_df.columns)} 个因子")
            
            # 保存合并后的因子数据到缓存
            with open(cache_path, 'wb') as f:
                pickle.dump(final_factors_df, f)
            
            # 更新元数据（记录保存的纯因子数量）
            self.metadata["stocks"][stock_code] = {
                "data_hash": data_hash,
                "last_update": datetime.now().isoformat(),
                "data_start": data.index[0].isoformat(),
                "data_end": data.index[-1].isoformat(),
                "factor_count": len(final_factors_df.columns),  # 记录最终保存的因子数量
                "record_count": len(final_factors_df)
            }
            
            if indicators not in [self.metadata.get("indicators", [])]:
                self.metadata["indicators"] = indicators
            
            # 详细显示计算出的因子
            factor_names = [col for col in factors_df.columns if col not in ['Close', 'Volume', 'turn']]
            pure_factor_names = list(pure_factors_df.columns)
            final_factor_names = list(final_factors_df.columns)
            
            print(f"股票 {stock_code} 因子计算完成，显示 {len(factors_df.columns)} 个列，本次计算 {len(pure_factors_df.columns)} 个纯因子，累计保存 {len(final_factors_df.columns)} 个因子")
            print(f"  - 基础数据列（仅显示）: {[col for col in factors_df.columns if col in ['Close', 'Volume', 'turn']]}")
            print(f"  - 本次计算因子: {pure_factor_names}")
            print(f"  - 累计保存因子: {final_factor_names}")
            
            # 按指标类型分组显示
            single_indicators = []
            multi_indicators = {}
            
            for factor_name in pure_factor_names:
                if '_' in factor_name:
                    base_name = factor_name.split('_')[0]
                    if base_name in ['MACD', 'KDJ', 'BOLL', 'DMI']:
                        if base_name not in multi_indicators:
                            multi_indicators[base_name] = []
                        multi_indicators[base_name].append(factor_name.split('_')[1])
                    else:
                        single_indicators.append(factor_name)
                else:
                    single_indicators.append(factor_name)
            
            if single_indicators:
                print(f"  - 单列指标: {single_indicators}")
            
            for base_name, sub_names in multi_indicators.items():
                print(f"  - {base_name}多列指标: {sub_names}")
            return True
            
        except Exception as e:
            print(f"计算股票 {stock_code} 因子失败: {e}")
            return False
    
    def precompute_batch_factors(self, 
                                stock_list: List[str],
                                indicators: Optional[List[str]] = None,
                                force_update: bool = False) -> Dict[str, bool]:
        """
        批量预计算股票因子
        
        Args:
            stock_list: 股票代码列表
            indicators: 要计算的指标列表
            force_update: 是否强制更新
            
        Returns:
            计算结果字典 {股票代码: 是否成功}
        """
        print(f"开始批量预计算 {len(stock_list)} 只股票的因子...")
        
        results = {}
        
        if self.max_workers == 1:
            # 单线程处理
            for stock_code in stock_list:
                results[stock_code] = self.precompute_stock_factors(
                    stock_code, indicators, force_update
                )
        else:
            # 多线程并行处理
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                future_to_stock = {
                    executor.submit(
                        self.precompute_stock_factors, 
                        stock_code, indicators, force_update
                    ): stock_code
                    for stock_code in stock_list
                }
                
                for future in as_completed(future_to_stock):
                    stock_code = future_to_stock[future]
                    try:
                        results[stock_code] = future.result()
                    except Exception as e:
                        print(f"处理股票 {stock_code} 时出错: {e}")
                        results[stock_code] = False
        
        # 更新全局元数据
        self.metadata["last_update"] = datetime.now().isoformat()
        self._save_metadata()
        
        # 统计结果
        success_count = sum(results.values())
        print(f"批量预计算完成: {success_count}/{len(stock_list)} 成功")
        
        return results
    
    def load_precomputed_factors(self, 
                                stock_code: str,
                                start_date: Optional[str] = None,
                                end_date: Optional[str] = None) -> Optional[pd.DataFrame]:
        """
        加载预计算的因子数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            因子数据DataFrame
        """
        try:
            cache_path = self._get_stock_cache_path(stock_code)
            
            if not os.path.exists(cache_path):
                print(f"股票 {stock_code} 的因子缓存不存在")
                return None
            
            # 加载数据
            with open(cache_path, 'rb') as f:
                factors_df = pickle.load(f)
            
            # 日期过滤
            if start_date:
                factors_df = factors_df[factors_df.index >= pd.to_datetime(start_date)]
            if end_date:
                factors_df = factors_df[factors_df.index <= pd.to_datetime(end_date)]
            
            return factors_df
            
        except Exception as e:
            print(f"加载股票 {stock_code} 预计算因子失败: {e}")
            return None
    
    def load_batch_factors(self, 
                          stock_list: List[str],
                          start_date: Optional[str] = None,
                          end_date: Optional[str] = None) -> Dict[str, pd.DataFrame]:
        """
        批量加载预计算因子
        
        Args:
            stock_list: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            因子数据字典 {股票代码: DataFrame}
        """
        results = {}
        
        for stock_code in stock_list:
            factors_df = self.load_precomputed_factors(stock_code, start_date, end_date)
            if factors_df is not None:
                results[stock_code] = factors_df
        
        print(f"成功加载 {len(results)}/{len(stock_list)} 只股票的预计算因子")
        return results

    def incremental_update(self,
                          stock_code: str,
                          new_data: pd.DataFrame,
                          indicators: Optional[List[str]] = None) -> bool:
        """
        增量更新股票因子

        Args:
            stock_code: 股票代码
            new_data: 新增数据
            indicators: 要计算的指标列表

        Returns:
            是否成功
        """
        try:
            # 加载现有因子数据
            existing_factors = self.load_precomputed_factors(stock_code)

            if existing_factors is None:
                # 如果没有现有数据，直接全量计算
                print(f"股票 {stock_code} 无现有因子数据，执行全量计算")
                return self.precompute_stock_factors(stock_code, indicators)

            # 检查新数据的日期范围
            last_date = existing_factors.index[-1]
            new_data = new_data[new_data.index > last_date]

            if len(new_data) == 0:
                print(f"股票 {stock_code} 无新增数据")
                return True

            print(f"股票 {stock_code} 发现 {len(new_data)} 条新数据，开始增量更新...")

            # 为了计算技术指标，需要包含一些历史数据
            lookback_days = 60  # 回看60天的数据
            start_date = last_date - timedelta(days=lookback_days)

            # 重新加载包含历史数据的原始数据
            full_data = self.load_stock_data(stock_code)
            if full_data is None:
                return False

            # 只计算需要更新的部分
            update_data = full_data[full_data.index >= start_date]

            # 计算因子
            if indicators is None:
                indicators = list(self.calculator.indicators.keys())

            factors_result = self.calculator.calculate_batch(
                {stock_code: update_data},
                indicators
            )[stock_code]

            # 构建新的因子数据
            factor_data = {'Close': update_data['Close'], 'Volume': update_data['Volume']}

            for indicator_name, indicator_result in factors_result.items():
                if indicator_result is None:
                    continue

                if isinstance(indicator_result, dict):
                    for sub_name, sub_series in indicator_result.items():
                        if isinstance(sub_series, pd.Series):
                            factor_data[f"{indicator_name}_{sub_name}"] = sub_series
                elif isinstance(indicator_result, pd.Series):
                    factor_data[indicator_name] = indicator_result

            new_factors_df = pd.DataFrame(factor_data, index=update_data.index)
            new_factors_df = handle_missing_values(new_factors_df, method='forward_fill')

            # 只保留新增的部分
            new_factors_df = new_factors_df[new_factors_df.index > last_date]

            # 合并数据
            updated_factors = pd.concat([existing_factors, new_factors_df])
            updated_factors = updated_factors.sort_index()

            # 保存更新后的数据
            cache_path = self._get_stock_cache_path(stock_code)
            with open(cache_path, 'wb') as f:
                pickle.dump(updated_factors, f)

            # 更新元数据
            data_hash = self._get_data_hash(full_data)
            self.metadata["stocks"][stock_code].update({
                "data_hash": data_hash,
                "last_update": datetime.now().isoformat(),
                "data_end": updated_factors.index[-1].isoformat(),
                "record_count": len(updated_factors)
            })

            print(f"股票 {stock_code} 增量更新完成，新增 {len(new_factors_df)} 条记录")
            return True

        except Exception as e:
            print(f"股票 {stock_code} 增量更新失败: {e}")
            return False

    def batch_incremental_update(self,
                                stock_list: List[str],
                                indicators: Optional[List[str]] = None) -> Dict[str, bool]:
        """
        批量增量更新

        Args:
            stock_list: 股票代码列表
            indicators: 要计算的指标列表

        Returns:
            更新结果字典
        """
        print(f"开始批量增量更新 {len(stock_list)} 只股票...")

        results = {}

        for stock_code in stock_list:
            # 加载最新数据
            new_data = self.load_stock_data(stock_code)
            if new_data is not None:
                results[stock_code] = self.incremental_update(stock_code, new_data, indicators)
            else:
                results[stock_code] = False

        # 更新全局元数据
        self.metadata["last_update"] = datetime.now().isoformat()
        self._save_metadata()

        success_count = sum(results.values())
        print(f"批量增量更新完成: {success_count}/{len(stock_list)} 成功")

        return results

    def get_cache_info(self) -> Dict:
        """获取缓存信息"""
        info = {
            "cache_dir": self.cache_dir,
            "total_stocks": len(self.metadata["stocks"]),
            "last_update": self.metadata.get("last_update"),
            "indicators": self.metadata.get("indicators", []),
            "stocks": {}
        }

        for stock_code, stock_meta in self.metadata["stocks"].items():
            cache_path = self._get_stock_cache_path(stock_code)
            file_size = os.path.getsize(cache_path) if os.path.exists(cache_path) else 0

            info["stocks"][stock_code] = {
                "last_update": stock_meta.get("last_update"),
                "data_range": f"{stock_meta.get('data_start')} ~ {stock_meta.get('data_end')}",
                "factor_count": stock_meta.get("factor_count"),
                "record_count": stock_meta.get("record_count"),
                "file_size_mb": round(file_size / 1024 / 1024, 2)
            }

        return info

    def cleanup_cache(self, keep_days: int = 30):
        """
        清理过期缓存

        Args:
            keep_days: 保留天数
        """
        cutoff_date = datetime.now() - timedelta(days=keep_days)
        removed_count = 0

        for stock_code in list(self.metadata["stocks"].keys()):
            stock_meta = self.metadata["stocks"][stock_code]
            last_update = datetime.fromisoformat(stock_meta.get("last_update", "1970-01-01"))

            if last_update < cutoff_date:
                cache_path = self._get_stock_cache_path(stock_code)
                if os.path.exists(cache_path):
                    os.remove(cache_path)

                del self.metadata["stocks"][stock_code]
                removed_count += 1

        if removed_count > 0:
            self._save_metadata()
            print(f"清理了 {removed_count} 个过期缓存文件")
        else:
            print("没有需要清理的过期缓存")

    def create_feature_matrix(self,
                             stock_list: List[str],
                             start_date: Optional[str] = None,
                             end_date: Optional[str] = None,
                             normalize: bool = True) -> Optional[pd.DataFrame]:
        """
        从预计算因子创建特征矩阵

        Args:
            stock_list: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            normalize: 是否标准化

        Returns:
            特征矩阵DataFrame
        """
        try:
            # 加载所有股票的因子数据
            factors_data = self.load_batch_factors(stock_list, start_date, end_date)

            if not factors_data:
                print("没有可用的因子数据")
                return None

            # 构建特征矩阵
            feature_list = []

            for stock_code, stock_factors in factors_data.items():
                for col in stock_factors.columns:
                    if col not in ['Close', 'Volume']:  # 排除价格和成交量
                        feature_series = stock_factors[col].copy()
                        feature_series.name = f"{stock_code}_{col}"
                        feature_list.append(feature_series)

            if not feature_list:
                print("没有有效的特征数据")
                return None

            # 合并特征
            feature_matrix = pd.concat(feature_list, axis=1)

            # 处理缺失值
            feature_matrix = handle_missing_values(feature_matrix, method='forward_fill')

            # 标准化
            if normalize:
                from .utils import normalize_data
                feature_matrix = normalize_data(feature_matrix, method='zscore')

            print(f"创建特征矩阵: {feature_matrix.shape}")
            return feature_matrix

        except Exception as e:
            print(f"创建特征矩阵失败: {e}")
            return None
