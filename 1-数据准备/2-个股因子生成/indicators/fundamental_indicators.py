"""
基本面指标实现模块

包含从原始股票数据中提取的基本面因子：
- turn: 换手率
- peTTM: 市盈率TTM
- psTTM: 市销率TTM  
- pcfNcfTTM: 市现率TTM
- pbMRQ: 市净率MRQ
"""

import numpy as np
import pandas as pd
from typing import Union, Dict, Optional
from .base import BaseIndicator


class FundamentalIndicator(BaseIndicator):
    """基本面指标基类"""
    
    def __init__(self, name: str):
        super().__init__(name)
    
    def _clean_fundamental_data(self, data: pd.Series) -> pd.Series:
        """
        清理基本面数据
        
        Args:
            data: 原始数据序列
            
        Returns:
            清理后的数据序列
        """
        # 处理异常值
        cleaned_data = data.copy()
        
        # 替换无穷大值为NaN
        cleaned_data = cleaned_data.replace([np.inf, -np.inf], np.nan)
        
        # 对于估值指标，过滤掉异常的负值和过大的值
        if self.name in ['peTTM', 'psTTM', 'pcfNcfTTM', 'pbMRQ']:
            # 负值设为NaN（估值指标不应该为负）
            cleaned_data = cleaned_data.where(cleaned_data > 0, np.nan)
            
            # 过滤极端值（超过99.5%分位数的值）
            if not cleaned_data.isna().all():
                upper_bound = cleaned_data.quantile(0.995)
                cleaned_data = cleaned_data.where(cleaned_data <= upper_bound, np.nan)
        
        return cleaned_data


class TurnoverRate(FundamentalIndicator):
    """换手率指标"""
    
    def __init__(self):
        super().__init__("turn")
    
    def calculate(self, data: pd.DataFrame, turnover_col: str = 'turn') -> pd.Series:
        """
        提取换手率数据

        Args:
            data: 股票数据
            turnover_col: 换手率列名

        Returns:
            换手率序列
        """
        # 检查列是否存在，如果不存在则尝试其他可能的列名
        available_cols = data.columns.tolist()

        if turnover_col not in available_cols:
            # 尝试其他可能的列名
            possible_names = ['turn', 'turnover', 'turnover_rate']
            found_col = None
            for name in possible_names:
                if name in available_cols:
                    found_col = name
                    break

            if found_col is None:
                # 如果找不到换手率列，返回全NaN序列
                print(f"⚠️ 未找到换手率列，尝试过的列名: {possible_names}")
                return pd.Series(index=data.index, dtype=float, name='turn')
            else:
                turnover_col = found_col

        self._validate_data(data, [turnover_col])

        # 尝试使用缓存
        cached_result = self._use_cache(data, turnover_col=turnover_col)
        if cached_result is not None:
            return cached_result

        turnover = data[turnover_col].copy()
        turnover = self._clean_fundamental_data(turnover)

        # 设置缓存
        self._set_cache(data, turnover, turnover_col=turnover_col)

        return turnover


class PETTM(FundamentalIndicator):
    """市盈率TTM指标"""
    
    def __init__(self):
        super().__init__("peTTM")
    
    def calculate(self, data: pd.DataFrame, pe_col: str = 'peTTM') -> pd.Series:
        """
        提取市盈率TTM数据

        Args:
            data: 股票数据
            pe_col: 市盈率列名

        Returns:
            市盈率TTM序列
        """
        # 检查列是否存在
        if pe_col not in data.columns:
            print(f"⚠️ 未找到市盈率列 {pe_col}")
            return pd.Series(index=data.index, dtype=float, name='peTTM')

        self._validate_data(data, [pe_col])

        # 尝试使用缓存
        cached_result = self._use_cache(data, pe_col=pe_col)
        if cached_result is not None:
            return cached_result

        pe_ttm = data[pe_col].copy()
        pe_ttm = self._clean_fundamental_data(pe_ttm)

        # 设置缓存
        self._set_cache(data, pe_ttm, pe_col=pe_col)

        return pe_ttm


class PSTTM(FundamentalIndicator):
    """市销率TTM指标"""
    
    def __init__(self):
        super().__init__("psTTM")
    
    def calculate(self, data: pd.DataFrame, ps_col: str = 'psTTM') -> pd.Series:
        """
        提取市销率TTM数据
        
        Args:
            data: 股票数据
            ps_col: 市销率列名
            
        Returns:
            市销率TTM序列
        """
        self._validate_data(data, [ps_col])
        
        # 尝试使用缓存
        cached_result = self._use_cache(data, ps_col=ps_col)
        if cached_result is not None:
            return cached_result
        
        ps_ttm = data[ps_col].copy()
        ps_ttm = self._clean_fundamental_data(ps_ttm)
        
        # 设置缓存
        self._set_cache(data, ps_ttm, ps_col=ps_col)
        
        return ps_ttm


class PCFNCFTTM(FundamentalIndicator):
    """市现率TTM指标"""
    
    def __init__(self):
        super().__init__("pcfNcfTTM")
    
    def calculate(self, data: pd.DataFrame, pcf_col: str = 'pcfNcfTTM') -> pd.Series:
        """
        提取市现率TTM数据
        
        Args:
            data: 股票数据
            pcf_col: 市现率列名
            
        Returns:
            市现率TTM序列
        """
        self._validate_data(data, [pcf_col])
        
        # 尝试使用缓存
        cached_result = self._use_cache(data, pcf_col=pcf_col)
        if cached_result is not None:
            return cached_result
        
        pcf_ttm = data[pcf_col].copy()
        pcf_ttm = self._clean_fundamental_data(pcf_ttm)
        
        # 设置缓存
        self._set_cache(data, pcf_ttm, pcf_col=pcf_col)
        
        return pcf_ttm


class PBMRQ(FundamentalIndicator):
    """市净率MRQ指标"""
    
    def __init__(self):
        super().__init__("pbMRQ")
    
    def calculate(self, data: pd.DataFrame, pb_col: str = 'pbMRQ') -> pd.Series:
        """
        提取市净率MRQ数据
        
        Args:
            data: 股票数据
            pb_col: 市净率列名
            
        Returns:
            市净率MRQ序列
        """
        self._validate_data(data, [pb_col])
        
        # 尝试使用缓存
        cached_result = self._use_cache(data, pb_col=pb_col)
        if cached_result is not None:
            return cached_result
        
        pb_mrq = data[pb_col].copy()
        pb_mrq = self._clean_fundamental_data(pb_mrq)
        
        # 设置缓存
        self._set_cache(data, pb_mrq, pb_col=pb_col)
        
        return pb_mrq
