"""
技术指标实现模块

包含RSI、MACD、KDJ、BOLL、CCI、WR等常用技术指标的实现
"""

import numpy as np
import pandas as pd
from typing import Union, Tuple, Dict, Optional, List
from .base import BaseIndicator, MovingAverage, PriceTransforms, OscillatorUtils


class RSI(BaseIndicator):
    """相对强弱指标 (Relative Strength Index)"""
    
    def __init__(self):
        super().__init__("RSI")
    
    def calculate(self, data: pd.DataFrame, window: int = 14, price_col: str = 'Close') -> pd.Series:
        """
        计算RSI指标
        
        Args:
            data: 股票数据
            window: 计算窗口
            price_col: 价格列名
        
        Returns:
            RSI值序列
        """
        self._validate_data(data, [price_col])
        
        # 尝试使用缓存
        cached_result = self._use_cache(data, window=window, price_col=price_col)
        if cached_result is not None:
            return cached_result
        
        prices = data[price_col]
        rsi = OscillatorUtils.rsi_calculation(prices, window)
        
        # 设置缓存
        self._set_cache(data, rsi, window=window, price_col=price_col)
        
        return rsi


class MACD(BaseIndicator):
    """移动平均收敛散度指标 (Moving Average Convergence Divergence)"""
    
    def __init__(self):
        super().__init__("MACD")
    
    def calculate(self, data: pd.DataFrame, fast_period: int = 12, slow_period: int = 26, 
                 signal_period: int = 9, price_col: str = 'Close') -> Dict[str, pd.Series]:
        """
        计算MACD指标
        
        Args:
            data: 股票数据
            fast_period: 快线周期
            slow_period: 慢线周期
            signal_period: 信号线周期
            price_col: 价格列名
        
        Returns:
            包含MACD、Signal、Histogram的字典
        """
        self._validate_data(data, [price_col])
        
        # 尝试使用缓存
        cache_key = f"macd_{fast_period}_{slow_period}_{signal_period}_{price_col}"
        cached_result = self._use_cache(data, cache_key=cache_key)
        if cached_result is not None:
            return cached_result
        
        prices = data[price_col]
        
        # 计算快慢EMA
        ema_fast = MovingAverage.ema(prices, fast_period)
        ema_slow = MovingAverage.ema(prices, slow_period)
        
        # MACD线
        macd_line = ema_fast - ema_slow
        
        # 信号线
        signal_line = MovingAverage.ema(macd_line, signal_period)
        
        # MACD柱状图
        histogram = macd_line - signal_line
        
        result = {
            'MACD': macd_line,
            'Signal': signal_line,
            'Histogram': histogram
        }
        
        # 设置缓存
        self._set_cache(data, result, cache_key=cache_key)
        
        return result


class KDJ(BaseIndicator):
    """随机指标 (Stochastic Oscillator)"""
    
    def __init__(self):
        super().__init__("KDJ")
    
    def calculate(self, data: pd.DataFrame, k_period: int = 9, d_period: int = 3, 
                 j_factor: int = 3) -> Dict[str, pd.Series]:
        """
        计算KDJ指标
        
        Args:
            data: 股票数据
            k_period: K值计算周期
            d_period: D值平滑周期
            j_factor: J值计算因子
        
        Returns:
            包含K、D、J值的字典
        """
        self._validate_data(data, ['High', 'Low', 'Close'])
        
        # 尝试使用缓存
        cached_result = self._use_cache(data, k_period=k_period, d_period=d_period, j_factor=j_factor)
        if cached_result is not None:
            return cached_result
        
        high = data['High']
        low = data['Low']
        close = data['Close']
        
        # 计算RSV (Raw Stochastic Value)
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        rsv = 100 * (close - lowest_low) / (highest_high - lowest_low)
        
        # 计算K值 (使用EMA平滑)
        k_values = rsv.ewm(alpha=1/d_period).mean()
        
        # 计算D值 (K值的EMA)
        d_values = k_values.ewm(alpha=1/d_period).mean()
        
        # 计算J值
        j_values = j_factor * k_values - (j_factor - 1) * d_values
        
        result = {
            'K': k_values,
            'D': d_values,
            'J': j_values
        }
        
        # 设置缓存
        self._set_cache(data, result, k_period=k_period, d_period=d_period, j_factor=j_factor)
        
        return result


class BollingerBands(BaseIndicator):
    """布林带指标 (Bollinger Bands)"""
    
    def __init__(self):
        super().__init__("BOLL")
    
    def calculate(self, data: pd.DataFrame, window: int = 20, std_dev: float = 2.0, 
                 price_col: str = 'Close') -> Dict[str, pd.Series]:
        """
        计算布林带指标
        
        Args:
            data: 股票数据
            window: 移动平均窗口
            std_dev: 标准差倍数
            price_col: 价格列名
        
        Returns:
            包含Upper、Middle、Lower的字典
        """
        self._validate_data(data, [price_col])
        
        # 尝试使用缓存
        cached_result = self._use_cache(data, window=window, std_dev=std_dev, price_col=price_col)
        if cached_result is not None:
            return cached_result
        
        prices = data[price_col]
        
        # 中轨（移动平均线）
        middle_band = MovingAverage.sma(prices, window)
        
        # 标准差
        rolling_std = prices.rolling(window=window).std()
        
        # 上轨和下轨
        upper_band = middle_band + (rolling_std * std_dev)
        lower_band = middle_band - (rolling_std * std_dev)
        
        result = {
            'Upper': upper_band,
            'Middle': middle_band,
            'Lower': lower_band
        }
        
        # 设置缓存
        self._set_cache(data, result, window=window, std_dev=std_dev, price_col=price_col)
        
        return result


class CCI(BaseIndicator):
    """商品通道指标 (Commodity Channel Index)"""
    
    def __init__(self):
        super().__init__("CCI")
    
    def calculate(self, data: pd.DataFrame, window: int = 20) -> pd.Series:
        """
        计算CCI指标
        
        Args:
            data: 股票数据
            window: 计算窗口
        
        Returns:
            CCI值序列
        """
        self._validate_data(data, ['High', 'Low', 'Close'])
        
        # 尝试使用缓存
        cached_result = self._use_cache(data, window=window)
        if cached_result is not None:
            return cached_result
        
        # 典型价格
        typical_price = PriceTransforms.typical_price(data['High'], data['Low'], data['Close'])
        
        # 移动平均
        sma_tp = MovingAverage.sma(typical_price, window)
        
        # 平均绝对偏差
        mad = typical_price.rolling(window=window).apply(
            lambda x: np.mean(np.abs(x - np.mean(x))), raw=True
        )
        
        # CCI计算
        cci = (typical_price - sma_tp) / (0.015 * mad)
        
        # 设置缓存
        self._set_cache(data, cci, window=window)
        
        return cci


class WilliamsR(BaseIndicator):
    """威廉指标 (Williams %R)"""
    
    def __init__(self):
        super().__init__("WR")
    
    def calculate(self, data: pd.DataFrame, window: int = 14) -> pd.Series:
        """
        计算威廉指标
        
        Args:
            data: 股票数据
            window: 计算窗口
        
        Returns:
            威廉指标值序列
        """
        self._validate_data(data, ['High', 'Low', 'Close'])
        
        # 尝试使用缓存
        cached_result = self._use_cache(data, window=window)
        if cached_result is not None:
            return cached_result
        
        wr = OscillatorUtils.williams_r(data['High'], data['Low'], data['Close'], window)
        
        # 设置缓存
        self._set_cache(data, wr, window=window)
        
        return wr


class ATR(BaseIndicator):
    """平均真实波动范围 (Average True Range)"""

    def __init__(self):
        super().__init__("ATR")

    def calculate(self, data: pd.DataFrame, window: int = 14) -> pd.Series:
        """
        计算ATR指标

        Args:
            data: 股票数据
            window: 计算窗口

        Returns:
            ATR值序列
        """
        self._validate_data(data, ['High', 'Low', 'Close'])

        # 尝试使用缓存
        cached_result = self._use_cache(data, window=window)
        if cached_result is not None:
            return cached_result

        # 真实波动范围
        tr = PriceTransforms.true_range(data['High'], data['Low'], data['Close'])

        # ATR (真实波动范围的移动平均)
        atr = MovingAverage.ema(tr, window)

        # 设置缓存
        self._set_cache(data, atr, window=window)

        return atr


class OBV(BaseIndicator):
    """能量潮指标 (On-Balance Volume)"""

    def __init__(self):
        super().__init__("OBV")

    def calculate(self, data: pd.DataFrame, price_col: str = 'Close', volume_col: str = 'Volume') -> pd.Series:
        """
        计算OBV指标

        Args:
            data: 股票数据
            price_col: 价格列名
            volume_col: 成交量列名

        Returns:
            OBV值序列
        """
        self._validate_data(data, [price_col, volume_col])

        # 尝试使用缓存
        cached_result = self._use_cache(data, price_col=price_col, volume_col=volume_col)
        if cached_result is not None:
            return cached_result

        prices = data[price_col]
        volume = data[volume_col]

        # 价格变化方向
        price_change = prices.diff()

        # OBV计算
        obv = pd.Series(index=data.index, dtype=float)
        obv.iloc[0] = volume.iloc[0]

        for i in range(1, len(data)):
            if price_change.iloc[i] > 0:
                obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
            elif price_change.iloc[i] < 0:
                obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
            else:
                obv.iloc[i] = obv.iloc[i-1]

        # 设置缓存
        self._set_cache(data, obv, price_col=price_col, volume_col=volume_col)

        return obv


class VWAP(BaseIndicator):
    """成交量加权平均价格 (Volume Weighted Average Price)"""

    def __init__(self):
        super().__init__("VWAP")

    def calculate(self, data: pd.DataFrame, window: Optional[int] = None) -> pd.Series:
        """
        计算VWAP指标

        Args:
            data: 股票数据
            window: 计算窗口，None表示累积VWAP

        Returns:
            VWAP值序列
        """
        self._validate_data(data, ['High', 'Low', 'Close', 'Volume'])

        # 尝试使用缓存
        cached_result = self._use_cache(data, window=window)
        if cached_result is not None:
            return cached_result

        # 典型价格
        typical_price = PriceTransforms.typical_price(data['High'], data['Low'], data['Close'])

        # 价格*成交量
        pv = typical_price * data['Volume']

        if window is None:
            # 累积VWAP
            vwap = pv.cumsum() / data['Volume'].cumsum()
        else:
            # 滚动VWAP
            vwap = pv.rolling(window=window).sum() / data['Volume'].rolling(window=window).sum()

        # 设置缓存
        self._set_cache(data, vwap, window=window)

        return vwap


class DMI(BaseIndicator):
    """动向指标 (Directional Movement Index)"""

    def __init__(self):
        super().__init__("DMI")

    def calculate(self, data: pd.DataFrame, window: int = 14) -> Dict[str, pd.Series]:
        """
        计算DMI指标

        Args:
            data: 股票数据
            window: 计算窗口

        Returns:
            包含PDI、MDI、ADX的字典
        """
        self._validate_data(data, ['High', 'Low', 'Close'])

        # 尝试使用缓存
        cached_result = self._use_cache(data, window=window)
        if cached_result is not None:
            return cached_result

        high = data['High']
        low = data['Low']
        close = data['Close']

        # 计算DM+ 和 DM-
        high_diff = high.diff()
        low_diff = low.diff()

        dm_plus = pd.Series(index=data.index, dtype=float)
        dm_minus = pd.Series(index=data.index, dtype=float)

        dm_plus = np.where((high_diff > low_diff) & (high_diff > 0), high_diff, 0)
        dm_minus = np.where((low_diff > high_diff) & (low_diff > 0), low_diff, 0)

        dm_plus = pd.Series(dm_plus, index=data.index)
        dm_minus = pd.Series(dm_minus, index=data.index)

        # 真实波动范围
        tr = PriceTransforms.true_range(high, low, close)

        # 平滑处理
        tr_smooth = MovingAverage.ema(tr, window)
        dm_plus_smooth = MovingAverage.ema(dm_plus, window)
        dm_minus_smooth = MovingAverage.ema(dm_minus, window)

        # 计算DI+ 和 DI-
        di_plus = 100 * dm_plus_smooth / tr_smooth
        di_minus = 100 * dm_minus_smooth / tr_smooth

        # 计算ADX
        dx = 100 * abs(di_plus - di_minus) / (di_plus + di_minus)
        adx = MovingAverage.ema(dx, window)

        result = {
            'PDI': di_plus,
            'MDI': di_minus,
            'ADX': adx
        }

        # 设置缓存
        self._set_cache(data, result, window=window)

        return result


class TurnoverStd(BaseIndicator):
    """换手率20日波动率指标 (Turnover Standard Deviation)"""
    
    def __init__(self):
        super().__init__("TurnoverStd")
    
    def calculate(self, data: pd.DataFrame, window: int = 20, turnover_col: str = 'turn') -> pd.Series:
        """
        计算换手率20日波动率
        
        Args:
            data: 股票数据
            window: 计算窗口，默认20日
            turnover_col: 换手率列名
        
        Returns:
            换手率波动率序列
        """
        self._validate_data(data, [turnover_col])
        
        # 尝试使用缓存
        cached_result = self._use_cache(data, window=window, turnover_col=turnover_col)
        if cached_result is not None:
            return cached_result
        
        # 计算换手率的滚动标准差
        turnover = data[turnover_col]
        turnover_std = turnover.rolling(window=window).std()
        
        # 设置缓存
        self._set_cache(data, turnover_std, window=window, turnover_col=turnover_col)
        
        return turnover_std


class ReturnStd120(BaseIndicator):
    """120日收益波动率指标 (120-day Return Standard Deviation)"""
    
    def __init__(self):
        super().__init__("ReturnStd120")
    
    def calculate(self, data: pd.DataFrame, window: int = 120, price_col: str = 'Close') -> pd.Series:
        """
        计算120日收益波动率
        
        Args:
            data: 股票数据
            window: 计算窗口，默认120日
            price_col: 价格列名
        
        Returns:
            收益波动率序列
        """
        self._validate_data(data, [price_col])
        
        # 尝试使用缓存
        cached_result = self._use_cache(data, window=window, price_col=price_col)
        if cached_result is not None:
            return cached_result
        
        # 计算日收益率
        prices = data[price_col]
        daily_returns = prices.pct_change()
        
        # 计算收益率的滚动标准差
        return_std = daily_returns.rolling(window=window).std()
        
        # 设置缓存
        self._set_cache(data, return_std, window=window, price_col=price_col)
        
        return return_std


class TechnicalIndicators:
    """
    技术指标统一管理类

    提供统一的接口来访问和使用所有技术指标
    """

    def __init__(self):
        """初始化所有技术指标实例"""
        self.rsi = RSI()
        self.macd = MACD()
        self.kdj = KDJ()
        self.bollinger_bands = BollingerBands()
        self.cci = CCI()
        self.williams_r = WilliamsR()
        self.atr = ATR()
        self.obv = OBV()
        self.vwap = VWAP()
        self.dmi = DMI()
        self.turnover_std20 = TurnoverStd()
        self.return_std120 = ReturnStd120()

        # 指标映射字典
        self._indicators = {
            'RSI': self.rsi,
            'MACD': self.macd,
            'KDJ': self.kdj,
            'BOLL': self.bollinger_bands,
            'CCI': self.cci,
            'WR': self.williams_r,
            'ATR': self.atr,
            'OBV': self.obv,
            'VWAP': self.vwap,
            'DMI': self.dmi,
            'turnover_std20': self.turnover_std20,
            'ReturnStd120': self.return_std120
        }

    def get_indicator(self, name: str) -> BaseIndicator:
        """
        获取指定的技术指标实例

        Args:
            name: 指标名称

        Returns:
            技术指标实例
        """
        if name not in self._indicators:
            raise ValueError(f"不支持的技术指标: {name}")
        return self._indicators[name]

    def get_available_indicators(self) -> List[str]:
        """
        获取所有可用的技术指标名称

        Returns:
            技术指标名称列表
        """
        return list(self._indicators.keys())

    def calculate_indicator(self, name: str, data: pd.DataFrame, **kwargs) -> Union[pd.Series, Dict[str, pd.Series]]:
        """
        计算指定的技术指标

        Args:
            name: 指标名称
            data: 股票数据
            **kwargs: 指标参数

        Returns:
            计算结果
        """
        indicator = self.get_indicator(name)
        return indicator.calculate(data, **kwargs)
