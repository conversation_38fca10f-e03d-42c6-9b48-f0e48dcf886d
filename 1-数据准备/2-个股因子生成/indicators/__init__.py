"""
股票因子计算模块

这个模块提供了全面的股票技术指标和因子计算功能，包括：
- 技术指标：RSI、MACD、KDJ、BOLL、CCI、WR等
- 统计指标：turnover_std20、ReturnStd120等
- 因子计算：动量因子、趋势因子、波动率因子等
- 批量处理：支持多股票同时计算
- 缓存机制：提高计算效率

注意：基本面指标（turn、peTTM、psTTM、pcfNcfTTM、pbMRQ）已从原始数据中直接提取，不再需要计算。

使用示例：
    from indicators import FactorCalculator

    calculator = FactorCalculator()
    factors = calculator.calculate_all_factors(df)
"""

from .factor_calculator import FactorCalculator
from .technical_indicators import TechnicalIndicators
# 基本面指标已从原始数据中直接提取，不再需要计算
# from .fundamental_indicators import (
#     TurnoverRate, PETTM, PSTTM, PCFNCFTTM, PBMRQ
# )
from .base import BaseIndicator
from .utils import validate_data, normalize_data
from .precompute_manager import PrecomputeManager

__version__ = "1.0.0"
__author__ = "Stock Trading AI System"

__all__ = [
    'FactorCalculator',
    'TechnicalIndicators',
    'BaseIndicator',
    'PrecomputeManager',
    'validate_data',
    'normalize_data'
    # 基本面指标类已移除，这些指标从原始数据中直接提取
    # 'TurnoverRate',
    # 'PETTM',
    # 'PSTTM',
    # 'PCFNCFTTM',
    # 'PBMRQ'
]
