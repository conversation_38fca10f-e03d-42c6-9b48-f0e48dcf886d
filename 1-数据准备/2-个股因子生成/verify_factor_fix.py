#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证个股因子生成修复效果

检查修复后的因子计算是否包含所有基本面指标
"""

import os
import sys
import pandas as pd
from typing import List, Dict

# 添加indicators模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'indicators'))

from indicators import PrecomputeManager

def verify_fundamental_indicators():
    """
    验证基本面指标是否正确计算
    """
    print("=== 验证基本面指标修复效果 ===")
    
    # 设置正确的数据和缓存目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    data_dir = os.path.join(current_dir, '..', '..', '0-数据汇总', '1-原始股票数据')
    cache_dir = os.path.join(current_dir, '..', '..', '0-数据汇总', '2-因子数据')
    
    # 初始化管理器
    manager = PrecomputeManager(data_dir=data_dir, cache_dir=cache_dir)
    
    # 测试股票列表（已经计算过因子的股票）
    test_stocks = ['SH.600000', 'SH.600004', 'SH.600007', 'SH.600009', 'SH.600010']
    
    # 预期的基本面指标
    expected_fundamental_indicators = ['turn', 'peTTM', 'psTTM', 'pcfNcfTTM', 'pbMRQ']
    
    print(f"测试股票: {test_stocks}")
    print(f"预期基本面指标: {expected_fundamental_indicators}")
    print()
    
    success_count = 0
    total_count = len(test_stocks)
    
    for stock_code in test_stocks:
        print(f"检查股票 {stock_code}...")
        
        # 加载预计算的因子
        factors_df = manager.load_precomputed_factors(stock_code)
        
        if factors_df is None:
            print(f"  ❌ 未找到预计算因子")
            continue
            
        print(f"  📊 因子数量: {len(factors_df.columns)}")
        print(f"  📅 数据行数: {len(factors_df)}")
        
        # 检查基本面指标是否存在
        missing_indicators = []
        present_indicators = []
        
        for indicator in expected_fundamental_indicators:
            if indicator in factors_df.columns:
                present_indicators.append(indicator)
                # 检查数据是否有效（非全NaN）
                non_nan_count = factors_df[indicator].notna().sum()
                print(f"  ✅ {indicator}: {non_nan_count}/{len(factors_df)} 有效数据")
            else:
                missing_indicators.append(indicator)
                print(f"  ❌ {indicator}: 缺失")
        
        if not missing_indicators:
            print(f"  🎉 股票 {stock_code} 所有基本面指标完整")
            success_count += 1
        else:
            print(f"  ⚠️ 股票 {stock_code} 缺少指标: {missing_indicators}")
        
        print()
    
    print("=== 验证结果汇总 ===")
    print(f"成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count == total_count:
        print("🎉 所有测试股票的基本面指标都已正确计算！")
        print("✅ 修复成功：数据加载逻辑已包含所有基本面字段")
    else:
        print("⚠️ 仍有部分股票存在问题，需要进一步检查")
    
    return success_count == total_count

def verify_data_loading():
    """
    验证数据加载是否包含基本面字段
    """
    print("\n=== 验证数据加载逻辑 ===")
    
    # 设置正确的数据和缓存目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    data_dir = os.path.join(current_dir, '..', '..', '0-数据汇总', '1-原始股票数据')
    cache_dir = os.path.join(current_dir, '..', '..', '0-数据汇总', '2-因子数据')
    
    manager = PrecomputeManager(data_dir=data_dir, cache_dir=cache_dir)
    test_stock = 'SH.600000'
    
    print(f"测试股票: {test_stock}")
    
    # 加载原始数据
    raw_data = manager.load_stock_data(test_stock)
    
    if raw_data is None:
        print("❌ 无法加载原始数据")
        return False
    
    print(f"📊 原始数据列数: {len(raw_data.columns)}")
    print(f"📅 原始数据行数: {len(raw_data)}")
    print(f"📋 数据列名: {list(raw_data.columns)}")
    
    # 检查基本面字段是否存在
    fundamental_fields = ['peTTM', 'psTTM', 'pcfNcfTTM', 'pbMRQ', 'turn']
    
    missing_fields = []
    present_fields = []
    
    for field in fundamental_fields:
        if field in raw_data.columns:
            present_fields.append(field)
            non_nan_count = raw_data[field].notna().sum()
            print(f"✅ {field}: {non_nan_count}/{len(raw_data)} 有效数据")
        else:
            missing_fields.append(field)
            print(f"❌ {field}: 缺失")
    
    if not missing_fields:
        print("🎉 原始数据包含所有基本面字段！")
        return True
    else:
        print(f"⚠️ 原始数据缺少字段: {missing_fields}")
        return False

def main():
    """
    主验证函数
    """
    print("个股因子生成修复验证")
    print("=" * 50)
    
    # 验证数据加载
    data_loading_ok = verify_data_loading()
    
    # 验证因子计算
    factor_calculation_ok = verify_fundamental_indicators()
    
    print("\n=== 最终验证结果 ===")
    print(f"数据加载修复: {'✅ 成功' if data_loading_ok else '❌ 失败'}")
    print(f"因子计算修复: {'✅ 成功' if factor_calculation_ok else '❌ 失败'}")
    
    if data_loading_ok and factor_calculation_ok:
        print("\n🎉 修复验证完全成功！")
        print("现在可以正常运行个股因子生成程序，不会再出现基本面字段缺失的错误。")
    else:
        print("\n⚠️ 修复验证未完全通过，需要进一步检查。")
    
    return data_loading_ok and factor_calculation_ok

if __name__ == "__main__":
    main()