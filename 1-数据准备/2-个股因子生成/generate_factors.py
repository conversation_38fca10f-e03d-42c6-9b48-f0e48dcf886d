#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
个股因子生成脚本 - 供GUI调用

功能：
1. 生成技术指标因子
2. 生成统计指标因子
3. 生成基本面因子
4. 支持单个股票或批量生成
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import talib
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

class FactorGenerator:
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent.parent
        self.raw_data_dir = self.base_dir / "0-数据汇总" / "1-原始股票数据"
        self.factor_data_dir = self.base_dir / "0-数据汇总" / "2-个股因子数据"
        self.factor_data_dir.mkdir(parents=True, exist_ok=True)
        
    def load_stock_data(self, stock_code):
        """
        加载股票数据
        
        Args:
            stock_code (str): 股票代码
            
        Returns:
            pd.DataFrame: 股票数据
        """
        try:
            filepath = self.raw_data_dir / f"{stock_code}_hfq.csv"
            if not filepath.exists():
                print(f"股票数据文件不存在: {filepath}")
                return None
                
            df = pd.read_csv(filepath)
            
            # 标准化列名
            column_mapping = {
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close', 
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount',
                '振幅': 'amplitude',
                '涨跌幅': 'pct_change',
                '涨跌额': 'change',
                '换手率': 'turnover'
            }
            
            df = df.rename(columns=column_mapping)
            
            # 确保数据类型正确
            numeric_columns = ['open', 'close', 'high', 'low', 'volume', 'amount']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                    
            # 按日期排序
            df = df.sort_values('date').reset_index(drop=True)
            
            return df
            
        except Exception as e:
            print(f"加载股票数据失败 {stock_code}: {str(e)}")
            return None
            
    def calculate_technical_factors(self, df):
        """
        计算技术指标因子
        
        Args:
            df (pd.DataFrame): 股票数据
            
        Returns:
            pd.DataFrame: 技术指标因子
        """
        try:
            factors = pd.DataFrame()
            factors['date'] = df['date']
            
            # 价格数据
            close = df['close'].values
            high = df['high'].values
            low = df['low'].values
            volume = df['volume'].values
            
            # RSI指标
            factors['RSI_14'] = talib.RSI(close, timeperiod=14)
            factors['RSI_6'] = talib.RSI(close, timeperiod=6)
            
            # MACD指标
            macd, macdsignal, macdhist = talib.MACD(close)
            factors['MACD'] = macd
            factors['MACD_Signal'] = macdsignal
            factors['MACD_Hist'] = macdhist
            
            # 布林带
            bb_upper, bb_middle, bb_lower = talib.BBANDS(close)
            factors['BB_Upper'] = bb_upper
            factors['BB_Middle'] = bb_middle
            factors['BB_Lower'] = bb_lower
            factors['BB_Width'] = (bb_upper - bb_lower) / bb_middle
            factors['BB_Position'] = (close - bb_lower) / (bb_upper - bb_lower)
            
            # KDJ指标
            k, d = talib.STOCH(high, low, close)
            factors['KDJ_K'] = k
            factors['KDJ_D'] = d
            factors['KDJ_J'] = 3 * k - 2 * d
            
            # 移动平均线
            factors['MA_5'] = talib.SMA(close, timeperiod=5)
            factors['MA_10'] = talib.SMA(close, timeperiod=10)
            factors['MA_20'] = talib.SMA(close, timeperiod=20)
            factors['MA_60'] = talib.SMA(close, timeperiod=60)
            
            # 价格相对位置
            factors['Price_MA5_Ratio'] = close / factors['MA_5']
            factors['Price_MA20_Ratio'] = close / factors['MA_20']
            
            # 成交量指标
            factors['Volume_MA_5'] = talib.SMA(volume.astype(float), timeperiod=5)
            factors['Volume_MA_20'] = talib.SMA(volume.astype(float), timeperiod=20)
            factors['Volume_Ratio'] = volume / factors['Volume_MA_20']
            
            # OBV指标
            factors['OBV'] = talib.OBV(close, volume.astype(float))
            
            return factors
            
        except Exception as e:
            print(f"计算技术指标失败: {str(e)}")
            return pd.DataFrame()
            
    def calculate_statistical_factors(self, df):
        """
        计算统计指标因子
        
        Args:
            df (pd.DataFrame): 股票数据
            
        Returns:
            pd.DataFrame: 统计指标因子
        """
        try:
            factors = pd.DataFrame()
            factors['date'] = df['date']
            
            close = df['close']
            high = df['high']
            low = df['low']
            volume = df['volume']
            
            # 收益率
            factors['Return_1d'] = close.pct_change(1)
            factors['Return_5d'] = close.pct_change(5)
            factors['Return_10d'] = close.pct_change(10)
            factors['Return_20d'] = close.pct_change(20)
            
            # 波动率
            factors['Volatility_5d'] = factors['Return_1d'].rolling(5).std()
            factors['Volatility_20d'] = factors['Return_1d'].rolling(20).std()
            factors['Volatility_60d'] = factors['Return_1d'].rolling(60).std()
            
            # 最高最低价比率
            factors['HL_Ratio_5d'] = high.rolling(5).max() / low.rolling(5).min()
            factors['HL_Ratio_20d'] = high.rolling(20).max() / low.rolling(20).min()
            
            # 价格位置
            factors['Price_Position_20d'] = (close - low.rolling(20).min()) / (high.rolling(20).max() - low.rolling(20).min())
            factors['Price_Position_60d'] = (close - low.rolling(60).min()) / (high.rolling(60).max() - low.rolling(60).min())
            
            # 成交量统计
            factors['Volume_Std_20d'] = volume.rolling(20).std()
            factors['Volume_Skew_20d'] = volume.rolling(20).skew()
            
            # 价格动量
            factors['Momentum_5d'] = close / close.shift(5) - 1
            factors['Momentum_20d'] = close / close.shift(20) - 1
            
            # 反转因子
            factors['Reversal_5d'] = -factors['Return_5d']
            factors['Reversal_20d'] = -factors['Return_20d']
            
            return factors
            
        except Exception as e:
            print(f"计算统计指标失败: {str(e)}")
            return pd.DataFrame()
            
    def calculate_fundamental_factors(self, df):
        """
        计算基本面因子（简化版）
        
        Args:
            df (pd.DataFrame): 股票数据
            
        Returns:
            pd.DataFrame: 基本面因子
        """
        try:
            factors = pd.DataFrame()
            factors['date'] = df['date']
            
            close = df['close']
            volume = df['volume']
            amount = df.get('amount', volume * close)  # 如果没有成交额，用成交量*价格估算
            
            # 市值相关（简化）
            factors['Market_Cap_Proxy'] = close * volume  # 简化的市值代理变量
            
            # 流动性指标
            factors['Liquidity_20d'] = amount.rolling(20).mean()
            factors['Turnover_20d'] = volume.rolling(20).mean()
            
            # 价格水平
            factors['Price_Level'] = close
            factors['Price_Rank_60d'] = close.rolling(60).rank(pct=True)
            
            # 成交额占比
            factors['Amount_Ratio_5d'] = amount / amount.rolling(5).mean()
            factors['Amount_Ratio_20d'] = amount / amount.rolling(20).mean()
            
            return factors
            
        except Exception as e:
            print(f"计算基本面指标失败: {str(e)}")
            return pd.DataFrame()
            
    def generate_all_factors(self, stock_code):
        """
        生成所有因子
        
        Args:
            stock_code (str): 股票代码
            
        Returns:
            bool: 是否成功
        """
        try:
            print(f"正在生成 {stock_code} 的因子...")
            
            # 加载股票数据
            df = self.load_stock_data(stock_code)
            if df is None or df.empty:
                print(f"{stock_code} 数据为空，跳过")
                return False
                
            # 计算各类因子
            tech_factors = self.calculate_technical_factors(df)
            stat_factors = self.calculate_statistical_factors(df)
            fund_factors = self.calculate_fundamental_factors(df)
            
            # 合并所有因子
            all_factors = tech_factors.copy()
            
            # 添加统计因子（排除日期列）
            for col in stat_factors.columns:
                if col != 'date':
                    all_factors[col] = stat_factors[col]
                    
            # 添加基本面因子（排除日期列）
            for col in fund_factors.columns:
                if col != 'date':
                    all_factors[col] = fund_factors[col]
                    
            # 添加股票代码
            all_factors['stock_code'] = stock_code
            
            # 重新排列列顺序
            cols = ['date', 'stock_code'] + [col for col in all_factors.columns if col not in ['date', 'stock_code']]
            all_factors = all_factors[cols]
            
            # 保存因子数据
            output_file = self.factor_data_dir / f"{stock_code}_factors.csv"
            all_factors.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            print(f"{stock_code} 因子生成完成，共 {len(all_factors)} 条记录，{len(all_factors.columns)-2} 个因子")
            return True
            
        except Exception as e:
            print(f"生成 {stock_code} 因子失败: {str(e)}")
            return False
            
    def generate_factors_batch(self, stock_codes=None, pool_name=None):
        """
        批量生成因子
        
        Args:
            stock_codes (list): 股票代码列表
            pool_name (str): 股票池名称
        """
        try:
            if stock_codes is None:
                if pool_name:
                    # 从股票池文件读取
                    codes_file = self.base_dir / "0-数据汇总" / "1-原始股票数据" / f"{pool_name}_codes.txt"
                    if codes_file.exists():
                        with open(codes_file, 'r', encoding='utf-8') as f:
                            stock_codes = [line.strip() for line in f if line.strip()]
                    else:
                        print(f"股票池文件不存在: {codes_file}")
                        return
                else:
                    # 从原始数据目录获取所有股票
                    stock_codes = []
                    for file in self.raw_data_dir.glob("*_hfq.csv"):
                        code = file.stem.replace('_hfq', '')
                        stock_codes.append(code)
                        
            if not stock_codes:
                print("没有找到股票代码")
                return
                
            success_count = 0
            total_count = len(stock_codes)
            
            print(f"开始批量生成因子，共 {total_count} 只股票")
            
            for i, stock_code in enumerate(stock_codes, 1):
                print(f"进度: {i}/{total_count} - {stock_code}")
                
                if self.generate_all_factors(stock_code):
                    success_count += 1
                    
            print(f"因子生成完成: {success_count}/{total_count} 成功")
            
        except Exception as e:
            print(f"批量生成因子过程发生错误: {str(e)}")

def main():
    """
    主函数 - 支持命令行参数
    """
    import argparse
    
    parser = argparse.ArgumentParser(description='个股因子生成工具')
    parser.add_argument('--stock', help='单个股票代码')
    parser.add_argument('--pool', choices=['sz50', 'hs300', 'zz500'], help='股票池名称')
    parser.add_argument('--all', action='store_true', help='生成所有股票的因子')
    
    args = parser.parse_args()
    
    generator = FactorGenerator()
    
    if args.stock:
        # 生成单个股票因子
        generator.generate_all_factors(args.stock)
    elif args.pool:
        # 生成指定股票池因子
        generator.generate_factors_batch(pool_name=args.pool)
    elif args.all:
        # 生成所有股票因子
        generator.generate_factors_batch()
    else:
        # 默认生成测试数据
        print("测试模式：生成前5只股票的因子")
        test_codes = []
        raw_data_dir = Path(__file__).parent.parent.parent / "0-数据汇总" / "1-原始股票数据"
        for file in list(raw_data_dir.glob("*_hfq.csv"))[:5]:
            code = file.stem.replace('_hfq', '')
            test_codes.append(code)
            
        if test_codes:
            generator.generate_factors_batch(stock_codes=test_codes)
        else:
            print("没有找到股票数据文件")
            
if __name__ == "__main__":
    main()