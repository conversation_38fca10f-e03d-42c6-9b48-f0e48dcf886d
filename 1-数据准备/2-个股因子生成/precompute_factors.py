"""
预计算因子管理脚本

用于批量预计算股票因子、增量更新、缓存管理等
"""

import argparse
import os
import sys
from datetime import datetime, timedelta
from typing import List, Optional
import pandas as pd

from indicators import PrecomputeManager


def get_stock_list_from_data_dir(data_dir: str) -> List[str]:
    """从数据目录获取股票列表"""
    stock_list = []

    if not os.path.exists(data_dir):
        print(f"数据目录不存在: {data_dir}")
        return stock_list

    for filename in os.listdir(data_dir):
        # 支持CSV文件格式
        if filename.endswith('_hfq.csv'):
            # 处理格式: sh.600000_hfq.csv -> sh.600000
            stock_code = filename.replace('_hfq.csv', '')

            # 文件名已包含市场前缀（如sh.600000, sz.000001），直接使用
            # 将小写前缀转换为大写以保持一致性
            if stock_code.startswith('sh.'):
                stock_code = stock_code.replace('sh.', 'SH.')
            elif stock_code.startswith('sz.'):
                stock_code = stock_code.replace('sz.', 'SZ.')
            
            stock_list.append(stock_code)

    print(f"从数据目录发现 {len(stock_list)} 只股票")
    return sorted(stock_list)


def precompute_all_factors(manager: PrecomputeManager, 
                          stock_list: List[str],
                          force_update: bool = False):
    """预计算所有股票的因子"""
    print("=== 开始预计算所有股票因子 ===")
    
    # 定义要计算的指标
    # 技术指标
    technical_indicators = ['RSI', 'MACD', 'KDJ', 'BOLL', 'CCI', 'WR', 'ATR', 'OBV', 'VWAP', 'DMI']

    # 基于原始数据的统计因子
    statistical_indicators = ['turnover_std20', 'ReturnStd120']

    # 基本面因子已从原始数据中直接提取，不再需要计算
    # fundamental_indicators = ['turn', 'peTTM', 'psTTM', 'pcfNcfTTM', 'pbMRQ']

    # 合并所有指标（仅包含需要计算的指标）
    indicators = technical_indicators + statistical_indicators
    
    print(f"计算指标: {indicators}")
    print(f"股票数量: {len(stock_list)}")
    print(f"强制更新: {force_update}")
    
    start_time = datetime.now()
    
    # 批量预计算
    results = manager.precompute_batch_factors(
        stock_list=stock_list,
        indicators=indicators,
        force_update=force_update
    )
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    # 统计结果
    success_count = sum(results.values())
    failed_stocks = [stock for stock, success in results.items() if not success]
    
    print(f"\n=== 预计算完成 ===")
    print(f"总耗时: {duration}")
    print(f"成功: {success_count}/{len(stock_list)}")
    
    if failed_stocks:
        print(f"失败的股票: {failed_stocks[:10]}{'...' if len(failed_stocks) > 10 else ''}")


def incremental_update_factors(manager: PrecomputeManager, 
                              stock_list: List[str]):
    """增量更新股票因子"""
    print("=== 开始增量更新股票因子 ===")
    
    indicators = ['RSI', 'MACD', 'KDJ', 'BOLL', 'CCI', 'WR', 'ATR', 'OBV', 'VWAP', 'DMI']
    
    start_time = datetime.now()
    
    # 批量增量更新
    results = manager.batch_incremental_update(
        stock_list=stock_list,
        indicators=indicators
    )
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    # 统计结果
    success_count = sum(results.values())
    
    print(f"\n=== 增量更新完成 ===")
    print(f"总耗时: {duration}")
    print(f"成功: {success_count}/{len(stock_list)}")


def show_cache_info(manager: PrecomputeManager):
    """显示缓存信息"""
    print("=== 缓存信息 ===")
    
    info = manager.get_cache_info()
    
    print(f"缓存目录: {info['cache_dir']}")
    print(f"股票总数: {info['total_stocks']}")
    print(f"最后更新: {info['last_update']}")
    print(f"计算指标: {info['indicators']}")
    
    if info['stocks']:
        print(f"\n前10只股票的缓存信息:")
        for i, (stock_code, stock_info) in enumerate(list(info['stocks'].items())[:10]):
            print(f"  {i+1:2d}. {stock_code}: "
                  f"{stock_info['factor_count']}因子, "
                  f"{stock_info['record_count']}记录, "
                  f"{stock_info['file_size_mb']}MB")
        
        if len(info['stocks']) > 10:
            print(f"  ... 还有 {len(info['stocks']) - 10} 只股票")
        
        # 统计总大小
        total_size = sum(stock_info['file_size_mb'] for stock_info in info['stocks'].values())
        print(f"\n缓存总大小: {total_size:.2f} MB")


def cleanup_cache(manager: PrecomputeManager, keep_days: int = 30):
    """清理过期缓存"""
    print(f"=== 清理 {keep_days} 天前的缓存 ===")
    manager.cleanup_cache(keep_days)


def test_precomputed_loading(manager: PrecomputeManager, 
                            stock_list: List[str],
                            sample_size: int = 5):
    """测试预计算因子加载"""
    print("=== 测试预计算因子加载 ===")
    
    # 随机选择几只股票测试
    import random
    test_stocks = random.sample(stock_list, min(sample_size, len(stock_list)))
    
    print(f"测试股票: {test_stocks}")
    
    start_time = datetime.now()
    
    # 加载预计算因子
    factors_data = manager.load_batch_factors(test_stocks)
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    print(f"加载耗时: {duration}")
    print(f"成功加载: {len(factors_data)}/{len(test_stocks)}")
    
    # 显示数据信息
    for stock_code, factors_df in factors_data.items():
        print(f"  {stock_code}: {factors_df.shape}, "
              f"{factors_df.index[0].date()} ~ {factors_df.index[-1].date()}")
    
    # 测试创建特征矩阵
    if factors_data:
        print("\n测试创建特征矩阵...")
        feature_matrix = manager.create_feature_matrix(
            list(factors_data.keys()),
            normalize=True
        )
        
        if feature_matrix is not None:
            print(f"特征矩阵形状: {feature_matrix.shape}")
            print(f"特征矩阵时间范围: {feature_matrix.index[0].date()} ~ {feature_matrix.index[-1].date()}")


def benchmark_performance(manager: PrecomputeManager, stock_list: List[str]):
    """性能基准测试"""
    print("=== 性能基准测试 ===")
    
    # 选择测试股票
    test_stocks = stock_list[:10] if len(stock_list) >= 10 else stock_list
    print(f"测试股票数量: {len(test_stocks)}")
    
    # 测试预计算加载速度
    print("\n1. 测试预计算因子加载速度...")
    start_time = datetime.now()
    factors_data = manager.load_batch_factors(test_stocks)
    load_time = datetime.now() - start_time
    print(f"   加载时间: {load_time}")
    
    if not factors_data:
        print("   没有预计算数据，跳过后续测试")
        return
    
    # 测试特征矩阵创建速度
    print("\n2. 测试特征矩阵创建速度...")
    start_time = datetime.now()
    feature_matrix = manager.create_feature_matrix(list(factors_data.keys()))
    matrix_time = datetime.now() - start_time
    print(f"   创建时间: {matrix_time}")
    
    if feature_matrix is not None:
        print(f"   矩阵大小: {feature_matrix.shape}")
        print(f"   内存占用: {feature_matrix.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
    
    # 对比实时计算速度
    print("\n3. 对比实时计算速度...")
    from indicators import FactorCalculator
    
    calculator = FactorCalculator()
    
    # 加载原始数据
    raw_data = {}
    for stock_code in list(factors_data.keys())[:3]:  # 只测试3只股票
        stock_data = manager.load_stock_data(stock_code)
        if stock_data is not None:
            raw_data[stock_code] = stock_data.tail(100)  # 只用最近100天数据
    
    if raw_data:
        start_time = datetime.now()
        realtime_results = calculator.calculate_batch(raw_data, ['RSI', 'MACD', 'KDJ'])
        realtime_time = datetime.now() - start_time
        print(f"   实时计算时间: {realtime_time}")
        print(f"   预计算加速比: {realtime_time / load_time:.1f}x")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='预计算因子管理工具')
    parser.add_argument('command', choices=[
        'precompute', 'update', 'info', 'cleanup', 'test', 'benchmark'
    ], help='执行的命令')
    
    parser.add_argument('--stocks', type=str, help='股票代码列表，逗号分隔')
    # 使用绝对路径指向正确的数据目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    default_data_dir = os.path.join(current_dir, '..', '..', '0-数据汇总', '1-原始股票数据')
    default_cache_dir = os.path.join(current_dir, '..', '..', '0-数据汇总', '2-因子数据')
    
    parser.add_argument('--data-dir', type=str, default=default_data_dir,
                       help='数据目录路径')
    parser.add_argument('--cache-dir', type=str, default=default_cache_dir,
                       help='缓存目录路径')
    parser.add_argument('--force', action='store_true', help='强制更新')
    parser.add_argument('--keep-days', type=int, default=30, help='缓存保留天数')
    parser.add_argument('--max-workers', type=int, default=4, help='并行工作线程数')
    
    args = parser.parse_args()
    
    # 创建预计算管理器
    manager = PrecomputeManager(
        cache_dir=args.cache_dir,
        data_dir=args.data_dir,
        max_workers=args.max_workers
    )
    
    # 获取股票列表
    if args.stocks:
        stock_list = [s.strip() for s in args.stocks.split(',')]
    else:
        stock_list = get_stock_list_from_data_dir(args.data_dir)
    
    if not stock_list:
        print("没有找到股票数据")
        return
    
    # 执行命令
    try:
        if args.command == 'precompute':
            precompute_all_factors(manager, stock_list, args.force)
        
        elif args.command == 'update':
            incremental_update_factors(manager, stock_list)
        
        elif args.command == 'info':
            show_cache_info(manager)
        
        elif args.command == 'cleanup':
            cleanup_cache(manager, args.keep_days)
        
        elif args.command == 'test':
            test_precomputed_loading(manager, stock_list)
        
        elif args.command == 'benchmark':
            benchmark_performance(manager, stock_list)
        
        print("\n操作完成！")
        
    except KeyboardInterrupt:
        print("\n操作被用户中断")
    except Exception as e:
        print(f"\n操作失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
