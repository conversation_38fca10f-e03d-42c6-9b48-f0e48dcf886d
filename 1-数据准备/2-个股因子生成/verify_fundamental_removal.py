#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证基本面指标移除效果

验证内容：
1. 确认因子计算器不再计算基本面指标
2. 确认原始数据中包含基本面指标
3. 验证新的因子数量是否正确
"""

import os
import sys
import pandas as pd
from typing import List, Dict

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from indicators import FactorCalculator, PrecomputeManager


def verify_factor_calculator_indicators():
    """
    验证因子计算器中的指标列表
    """
    print("=== 验证因子计算器指标列表 ===")
    
    calculator = FactorCalculator()
    
    # 获取所有指标名称
    indicator_names = list(calculator.indicators.keys())
    
    print(f"当前支持的指标数量: {len(indicator_names)}")
    print(f"指标列表: {indicator_names}")
    
    # 检查基本面指标是否已移除
    fundamental_indicators = ['turn', 'peTTM', 'psTTM', 'pcfNcfTTM', 'pbMRQ']
    removed_indicators = []
    remaining_indicators = []
    
    for indicator in fundamental_indicators:
        if indicator in indicator_names:
            remaining_indicators.append(indicator)
        else:
            removed_indicators.append(indicator)
    
    print(f"\n已移除的基本面指标: {removed_indicators}")
    print(f"仍在计算的基本面指标: {remaining_indicators}")
    
    # 验证结果
    if len(remaining_indicators) == 0:
        print("✅ 基本面指标移除成功！")
        return True
    else:
        print("❌ 仍有基本面指标在计算中")
        return False


def verify_raw_data_contains_fundamentals(stock_codes: List[str] = None):
    """
    验证原始数据中包含基本面指标
    """
    print("\n=== 验证原始数据包含基本面指标 ===")
    
    if stock_codes is None:
        stock_codes = ['SH.600000', 'SH.600004', 'SH.600007']
    
    # 使用正确的数据目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    data_dir = os.path.join(current_dir, '..', '..', '0-数据汇总', '1-原始股票数据')
    cache_dir = os.path.join(current_dir, '..', '..', '0-数据汇总', '2-因子数据')
    
    manager = PrecomputeManager(data_dir=data_dir, cache_dir=cache_dir)
    
    fundamental_indicators = ['turn', 'peTTM', 'psTTM', 'pcfNcfTTM', 'pbMRQ']
    
    success_count = 0
    
    for stock_code in stock_codes:
        print(f"\n检查股票 {stock_code}:")
        
        # 加载原始数据
        data = manager.load_stock_data(stock_code)
        
        if data is None:
            print(f"  ❌ 无法加载数据")
            continue
        
        print(f"  数据行数: {len(data)}")
        print(f"  数据列数: {len(data.columns)}")
        
        # 检查基本面指标列
        available_fundamentals = []
        missing_fundamentals = []
        
        for indicator in fundamental_indicators:
            if indicator in data.columns:
                available_fundamentals.append(indicator)
                non_null_count = data[indicator].notna().sum()
                print(f"  ✅ {indicator}: {non_null_count}/{len(data)} 非空值")
            else:
                missing_fundamentals.append(indicator)
                print(f"  ❌ {indicator}: 列不存在")
        
        if len(missing_fundamentals) == 0:
            print(f"  ✅ 股票 {stock_code} 包含所有基本面指标")
            success_count += 1
        else:
            print(f"  ❌ 股票 {stock_code} 缺少基本面指标: {missing_fundamentals}")
    
    success_rate = success_count / len(stock_codes) * 100
    print(f"\n原始数据验证结果: {success_count}/{len(stock_codes)} ({success_rate:.1f}%) 股票包含完整基本面指标")
    
    return success_count == len(stock_codes)


def verify_computed_factors(stock_codes: List[str] = None):
    """
    验证计算出的因子数量和内容
    """
    print("\n=== 验证计算因子数量和内容 ===")
    
    if stock_codes is None:
        stock_codes = ['SH.600000']
    
    # 使用正确的数据目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    data_dir = os.path.join(current_dir, '..', '..', '0-数据汇总', '1-原始股票数据')
    cache_dir = os.path.join(current_dir, '..', '..', '0-数据汇总', '2-因子数据')
    
    manager = PrecomputeManager(data_dir=data_dir, cache_dir=cache_dir)
    
    for stock_code in stock_codes:
        print(f"\n检查股票 {stock_code} 的计算因子:")
        
        # 加载预计算因子
        factors_df = manager.load_precomputed_factors(stock_code)
        
        if factors_df is None:
            print(f"  ❌ 无法加载预计算因子")
            continue
        
        print(f"  因子数量: {len(factors_df.columns)}")
        print(f"  数据行数: {len(factors_df)}")
        
        # 分析因子类型
        technical_factors = []
        statistical_factors = []
        percentile_factors = []
        other_factors = []
        
        for col in factors_df.columns:
            if col.endswith('_percentile'):
                percentile_factors.append(col)
            elif col in ['RSI', 'CCI', 'WR', 'ATR', 'OBV', 'VWAP', 'DMI_PDI', 'DMI_MDI', 'DMI_ADX', 
                        'MACD_MACD', 'MACD_Signal', 'MACD_Histogram',
                        'KDJ_K', 'KDJ_D', 'KDJ_J',
                        'BOLL_Upper', 'BOLL_Middle', 'BOLL_Lower']:
                technical_factors.append(col)
            elif col in ['turnover_std20', 'ReturnStd120']:
                statistical_factors.append(col)
            else:
                other_factors.append(col)
        
        print(f"  技术指标因子: {len(technical_factors)} 个")
        print(f"  统计指标因子: {len(statistical_factors)} 个")
        print(f"  百分位因子: {len(percentile_factors)} 个")
        print(f"  其他因子: {len(other_factors)} 个")
        
        if other_factors:
            print(f"  其他因子列表: {other_factors}")
        
        # 验证不包含基本面指标
        fundamental_indicators = ['turn', 'peTTM', 'psTTM', 'pcfNcfTTM', 'pbMRQ']
        found_fundamentals = [col for col in factors_df.columns if col in fundamental_indicators]
        
        if found_fundamentals:
            print(f"  ❌ 发现基本面指标仍在计算: {found_fundamentals}")
        else:
            print(f"  ✅ 确认不包含基本面指标")
    
    return True


def main():
    """
    主验证函数
    """
    print("🔍 基本面指标移除验证")
    print("=" * 60)
    
    # 1. 验证因子计算器指标列表
    calculator_ok = verify_factor_calculator_indicators()
    
    # 2. 验证原始数据包含基本面指标
    raw_data_ok = verify_raw_data_contains_fundamentals()
    
    # 3. 验证计算因子内容
    computed_factors_ok = verify_computed_factors()
    
    print("\n" + "=" * 60)
    print("📋 验证结果总结")
    print("=" * 60)
    
    if calculator_ok:
        print("✅ 因子计算器: 基本面指标已成功移除")
    else:
        print("❌ 因子计算器: 仍有基本面指标在计算")
    
    if raw_data_ok:
        print("✅ 原始数据: 包含完整的基本面指标")
    else:
        print("❌ 原始数据: 缺少基本面指标")
    
    if computed_factors_ok:
        print("✅ 计算因子: 验证通过")
    else:
        print("❌ 计算因子: 验证失败")
    
    if all([calculator_ok, raw_data_ok, computed_factors_ok]):
        print("\n🎉 所有验证通过！基本面指标移除成功")
        print("💡 现在系统只计算技术指标和统计指标，基本面指标直接从原始数据提取")
    else:
        print("\n⚠️ 部分验证未通过，请检查相关问题")
    
    print("=" * 60)


if __name__ == "__main__":
    main()