#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为所有股票添加新因子脚本

为已有的预计算因子缓存添加turnover_std20和ReturnStd120两个新因子
"""

import os
import sys
import pickle
import pandas as pd
from typing import List, Dict
from datetime import datetime
import glob

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from indicators.precompute_manager import PrecomputeManager
from indicators.factor_calculator import FactorCalculator


def get_all_cached_stocks() -> List[str]:
    """
    获取所有已缓存的股票代码
    """
    cache_dir = "factor_cache/factors"
    if not os.path.exists(cache_dir):
        return []
    
    stock_files = glob.glob(os.path.join(cache_dir, "*.pkl"))
    stock_codes = []
    
    for file_path in stock_files:
        filename = os.path.basename(file_path)
        # 从文件名提取股票代码，格式如：sh.600000_factors.pkl
        if filename.endswith("_factors.pkl"):
            stock_code = filename.replace("_factors.pkl", "")
            stock_codes.append(stock_code)
    
    return sorted(stock_codes)


def check_factor_exists(stock_code: str, factor_name: str) -> bool:
    """
    检查股票是否已有指定因子
    """
    try:
        cache_path = f"factor_cache/factors/{stock_code}_factors.pkl"
        if not os.path.exists(cache_path):
            return False
            
        with open(cache_path, 'rb') as f:
            factors_df = pickle.load(f)
            
        return factor_name in factors_df.columns
    except Exception as e:
        print(f"检查因子 {factor_name} 失败 ({stock_code}): {e}")
        return False


def update_stock_factors(stock_code: str, new_factors: List[str]) -> bool:
    """
    为单个股票添加新因子
    """
    try:
        # 检查哪些因子需要添加
        factors_to_add = []
        for factor in new_factors:
            if not check_factor_exists(stock_code, factor):
                factors_to_add.append(factor)
        
        if not factors_to_add:
            print(f"股票 {stock_code} 已包含所有新因子，跳过")
            return True
            
        print(f"为股票 {stock_code} 添加因子: {factors_to_add}")
        
        # 使用预计算管理器进行强制更新
        manager = PrecomputeManager()
        
        # 使用强制更新模式重新计算所有因子（包括新因子）
        success = manager.precompute_stock_factors(
            stock_code=stock_code,
            indicators=['RSI', 'MACD', 'KDJ', 'BOLL', 'CCI', 'WR', 'ATR', 'OBV', 'VWAP', 'DMI', 'turnover_std20', 'ReturnStd120'],
            force_update=True
        )
        
        if success:
            print(f"股票 {stock_code} 因子更新成功")
        else:
            print(f"股票 {stock_code} 因子更新失败")
            
        return success
        
    except Exception as e:
        print(f"更新股票 {stock_code} 因子失败: {e}")
        return False


def batch_update_factors(stock_list: List[str], new_factors: List[str], batch_size: int = 50):
    """
    批量更新股票因子
    """
    total_stocks = len(stock_list)
    success_count = 0
    failed_stocks = []
    
    print(f"开始批量更新 {total_stocks} 只股票的因子...")
    print(f"新增因子: {new_factors}")
    print(f"批次大小: {batch_size}")
    print("=" * 60)
    
    # 分批处理
    for i in range(0, total_stocks, batch_size):
        batch_stocks = stock_list[i:i + batch_size]
        batch_num = i // batch_size + 1
        total_batches = (total_stocks + batch_size - 1) // batch_size
        
        print(f"\n处理第 {batch_num}/{total_batches} 批次 ({len(batch_stocks)} 只股票)")
        print("-" * 40)
        
        batch_success = 0
        for j, stock_code in enumerate(batch_stocks, 1):
            print(f"[{i+j:3d}/{total_stocks}] 处理 {stock_code}...", end=" ")
            
            if update_stock_factors(stock_code, new_factors):
                batch_success += 1
                success_count += 1
                print("✓")
            else:
                failed_stocks.append(stock_code)
                print("✗")
        
        print(f"批次完成: {batch_success}/{len(batch_stocks)} 成功")
    
    print("\n" + "=" * 60)
    print(f"批量更新完成!")
    print(f"总计: {success_count}/{total_stocks} 成功")
    print(f"成功率: {success_count/total_stocks*100:.1f}%")
    
    if failed_stocks:
        print(f"\n失败的股票 ({len(failed_stocks)} 只):")
        for i, stock in enumerate(failed_stocks, 1):
            print(f"  {i:2d}. {stock}")
            if i % 10 == 0:  # 每10个换行
                print()
    
    return success_count, failed_stocks


def main():
    """
    主函数
    """
    print("股票因子批量更新工具")
    print("=" * 60)
    
    # 要添加的新因子
    new_factors = ['turnover_std20', 'ReturnStd120']
    
    # 获取所有已缓存的股票
    all_stocks = get_all_cached_stocks()
    
    if not all_stocks:
        print("未找到任何已缓存的股票数据")
        return
    
    print(f"发现 {len(all_stocks)} 只已缓存的股票")
    print(f"要添加的因子: {new_factors}")
    
    # 检查有多少股票需要更新
    stocks_need_update = []
    for stock_code in all_stocks:
        need_update = False
        for factor in new_factors:
            if not check_factor_exists(stock_code, factor):
                need_update = True
                break
        if need_update:
            stocks_need_update.append(stock_code)
    
    print(f"需要更新的股票: {len(stocks_need_update)} 只")
    
    if not stocks_need_update:
        print("所有股票都已包含新因子，无需更新")
        return
    
    # 确认是否继续
    response = input(f"\n是否继续更新 {len(stocks_need_update)} 只股票? (y/N): ")
    if response.lower() not in ['y', 'yes']:
        print("操作已取消")
        return
    
    # 开始批量更新
    start_time = datetime.now()
    success_count, failed_stocks = batch_update_factors(stocks_need_update, new_factors)
    end_time = datetime.now()
    
    # 输出最终统计
    duration = end_time - start_time
    print(f"\n总耗时: {duration}")
    print(f"平均每只股票: {duration.total_seconds()/len(stocks_need_update):.2f} 秒")
    
    if failed_stocks:
        print(f"\n建议重新运行脚本处理失败的股票")


if __name__ == "__main__":
    main()