#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据准备GUI启动脚本
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

try:
    from data_preparation_gui import main
    
    if __name__ == "__main__":
        print("启动股票数据准备工具...")
        main()
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保所有依赖模块已正确安装")
    input("按回车键退出...")
except Exception as e:
    print(f"程序运行错误: {e}")
    input("按回车键退出...")