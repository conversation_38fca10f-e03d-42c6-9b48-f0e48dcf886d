#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新计算所有股票的技术指标因子
确保所有股票都有完整和一致的技术指标
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 添加路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)
sys.path.append(os.path.join(project_root, "2-训练与回测"))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "2-个股因子生成"))

# 导入配置
import importlib.util
config_spec = importlib.util.spec_from_file_location("config", os.path.join(project_root, "2-训练与回测", "config.py"))
config_module = importlib.util.module_from_spec(config_spec)
config_spec.loader.exec_module(config_module)
DATA_CONFIG = config_module.DATA_CONFIG
get_available_stocks = config_module.get_available_stocks
get_stock_file_path = config_module.get_stock_file_path

from indicators import FactorCalculator

# 线程锁用于安全打印
print_lock = threading.Lock()

def safe_print(*args, **kwargs):
    """线程安全的打印函数"""
    with print_lock:
        print(*args, **kwargs)

def calculate_stock_technical_factors(stock_code: str, calculator: FactorCalculator) -> dict:
    """
    计算单只股票的技术指标因子
    
    Args:
        stock_code: 股票代码
        calculator: 因子计算器
        
    Returns:
        计算结果字典
    """
    result = {
        'stock_code': stock_code,
        'success': False,
        'factor_count': 0,
        'error': None,
        'factors': []
    }
    
    try:
        safe_print(f"🔄 开始计算股票: {stock_code}")
        
        # 获取股票数据文件
        file_path = get_stock_file_path(stock_code)
        if not os.path.exists(file_path):
            result['error'] = f"数据文件不存在: {file_path}"
            return result
        
        # 读取数据
        df = pd.read_csv(file_path)
        
        # 检查必需的列
        required_cols = ['date', 'open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            result['error'] = f"缺少必需的列: {missing_cols}"
            return result
        
        # 数据预处理
        df['date'] = pd.to_datetime(df['date'])
        df.set_index('date', inplace=True)
        df.sort_index(inplace=True)
        
        # 重命名列以匹配计算器期望的格式
        df = df.rename(columns={
            'open': 'Open',
            'high': 'High', 
            'low': 'Low',
            'close': 'Close',
            'volume': 'Volume'
        })
        
        # 定义所有要计算的因子
        all_factors = [
            # 技术指标
            'RSI', 'MACD', 'KDJ', 'BOLL', 'CCI', 'WR', 'ATR', 'OBV', 'VWAP', 'DMI',
            # 统计指标
            'turnover_std20', 'ReturnStd120',
            # 基本面指标
            'turn', 'peTTM', 'psTTM', 'pcfNcfTTM', 'pbMRQ'
        ]
        
        # 计算所有因子
        factor_results = {}
        success_factors = []
        
        for factor_name in all_factors:
            try:
                factor_result = calculator.calculate_single_indicator(df, factor_name)
                
                if factor_result is not None:
                    if isinstance(factor_result, dict):
                        # 多值指标（如MACD、KDJ等）
                        for key, series in factor_result.items():
                            factor_key = f"{factor_name}_{key}"
                            factor_results[factor_key] = series
                            success_factors.append(factor_key)
                    else:
                        # 单值指标
                        factor_results[factor_name] = factor_result
                        success_factors.append(factor_name)
                    
                    safe_print(f"    ✅ {stock_code} - {factor_name}: 计算成功")
                else:
                    safe_print(f"    ⚠️ {stock_code} - {factor_name}: 计算结果为空")
                    
            except Exception as e:
                safe_print(f"    ❌ {stock_code} - {factor_name}: 计算失败 - {e}")
        
        if not factor_results:
            result['error'] = "没有成功计算任何因子"
            return result
        
        # 合并所有因子到一个DataFrame
        factor_df = pd.DataFrame(factor_results)
        factor_df.index.name = 'date'
        
        # 保存因子数据
        output_dir = str(DATA_CONFIG['factor_cache_dir'])
        os.makedirs(output_dir, exist_ok=True)
        
        output_file = os.path.join(output_dir, f"{stock_code}_factors.pkl")
        
        with open(output_file, 'wb') as f:
            pickle.dump(factor_df, f)
        
        result['success'] = True
        result['factor_count'] = len(factor_df.columns)
        result['factors'] = success_factors
        
        safe_print(f"  ✅ {stock_code}: 完成 - {len(factor_df.columns)}个因子特征")
        
        return result
        
    except Exception as e:
        result['error'] = str(e)
        safe_print(f"  ❌ {stock_code}: 处理失败 - {e}")
        return result

def recalculate_all_technical_factors(max_workers: int = 8, target_stocks: list = None):
    """
    重新计算所有股票的技术指标因子
    
    Args:
        max_workers: 最大并发线程数
        target_stocks: 目标股票列表，None表示所有股票
    """
    print("🚀 重新计算所有股票的技术指标因子")
    print("=" * 80)
    
    # 获取目标股票
    if target_stocks is None:
        available_stocks = get_available_stocks()
        target_stocks = available_stocks
    
    print(f"目标股票数: {len(target_stocks)}")
    print(f"并发线程数: {max_workers}")
    
    # 创建因子计算器（每个线程一个）
    def create_calculator():
        return FactorCalculator()
    
    # 批量处理
    results = []
    success_count = 0
    total_count = len(target_stocks)
    
    start_time = datetime.now()
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 为每个任务创建独立的计算器
        future_to_stock = {
            executor.submit(calculate_stock_technical_factors, stock_code, create_calculator()): stock_code 
            for stock_code in target_stocks
        }
        
        # 处理完成的任务
        for future in as_completed(future_to_stock):
            stock_code = future_to_stock[future]
            try:
                result = future.result()
                results.append(result)
                
                if result['success']:
                    success_count += 1
                
                # 显示进度
                progress = len(results) / total_count * 100
                safe_print(f"进度: {len(results)}/{total_count} ({progress:.1f}%) - 成功: {success_count}")
                
            except Exception as e:
                safe_print(f"❌ {stock_code}: 任务执行失败 - {e}")
                results.append({
                    'stock_code': stock_code,
                    'success': False,
                    'error': str(e)
                })
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    # 统计结果
    print("\n" + "=" * 80)
    print("📋 重新计算结果统计")
    print("=" * 80)
    
    print(f"总处理时间: {duration}")
    print(f"总股票数: {total_count}")
    print(f"成功股票数: {success_count}")
    print(f"失败股票数: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    # 分析因子数量分布
    factor_counts = {}
    for result in results:
        if result['success']:
            count = result['factor_count']
            factor_counts[count] = factor_counts.get(count, 0) + 1
    
    if factor_counts:
        print(f"\n📊 因子数量分布:")
        for count, num_stocks in sorted(factor_counts.items()):
            print(f"  {count}个因子: {num_stocks}只股票")
    
    # 显示失败的股票
    failed_stocks = [r for r in results if not r['success']]
    if failed_stocks:
        print(f"\n❌ 失败的股票 ({len(failed_stocks)}只):")
        for result in failed_stocks[:10]:  # 只显示前10个
            print(f"  {result['stock_code']}: {result.get('error', '未知错误')}")
        if len(failed_stocks) > 10:
            print(f"  ... 还有 {len(failed_stocks) - 10} 只股票失败")
    
    return results

def verify_factor_consistency():
    """验证因子一致性"""
    print("\n🔍 验证因子一致性...")
    
    factor_dir = str(DATA_CONFIG['factor_cache_dir'])
    factor_files = [f for f in os.listdir(factor_dir) if f.endswith('_factors.pkl')]
    
    if not factor_files:
        print("❌ 未找到因子文件")
        return False
    
    # 检查前20个文件的因子列
    sample_files = factor_files[:20]
    factor_columns = []
    
    for filename in sample_files:
        stock_code = filename.replace('_factors.pkl', '')
        file_path = os.path.join(factor_dir, filename)
        
        try:
            with open(file_path, 'rb') as f:
                df = pickle.load(f)
            
            factor_columns.append((stock_code, set(df.columns)))
            print(f"  {stock_code}: {len(df.columns)}个因子")
            
        except Exception as e:
            print(f"  ❌ {stock_code}: 读取失败 - {e}")
    
    # 检查因子列一致性
    if factor_columns:
        first_stock, first_factors = factor_columns[0]
        consistent = True
        
        for stock_code, factors in factor_columns[1:]:
            if factors != first_factors:
                consistent = False
                diff = factors.symmetric_difference(first_factors)
                print(f"  ⚠️ {stock_code} 与 {first_stock} 因子不一致: {diff}")
        
        if consistent:
            print(f"✅ 因子列完全一致 ({len(first_factors)}个因子)")
            print(f"  因子列表: {sorted(list(first_factors))}")
        else:
            print(f"❌ 因子列不一致")
        
        return consistent
    
    return False

def main():
    """主函数"""
    print("=" * 80)
    print("🔄 重新计算所有股票的技术指标因子")
    print("=" * 80)
    
    # 1. 重新计算所有因子
    results = recalculate_all_technical_factors(max_workers=8)
    
    # 2. 验证因子一致性
    consistency_ok = verify_factor_consistency()
    
    print("\n" + "=" * 80)
    print("📋 最终结果总结")
    print("=" * 80)
    
    success_count = sum(1 for r in results if r['success'])
    total_count = len(results)
    
    if success_count > 0 and consistency_ok:
        print("🎉 技术指标因子重新计算成功！")
        print("💡 下一步可以:")
        print("   1. 重新生成排序因子:")
        print("      cd 3-排序因子生成")
        print("      python calculate_factor_percentiles_optimized.py")
        print("   2. 开始增强因子训练:")
        print("      cd ../2-训练与回测")
        print("      python run_enhanced_training.py --num-stocks 30")
    else:
        print("⚠️ 技术指标因子计算存在问题:")
        if success_count == 0:
            print("   - 没有股票成功计算因子")
        elif not consistency_ok:
            print("   - 因子列不一致")
        print("   建议检查错误信息并重新运行")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
