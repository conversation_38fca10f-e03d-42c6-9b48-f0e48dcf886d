2025-08-05 17:45:24,249 - INFO - 开始更新上证50股票清单...
2025-08-05 17:45:24,302 - INFO - baostock登录成功
2025-08-05 17:45:24,383 - INFO - 成功获取上证50成分股，共 50 只股票
2025-08-05 17:45:24,388 - INFO - 股票清单已保存到: D:\开发项目\Stock Models\RLStrategy_V2.0（SAC算法）\0-数据汇总\1-原始股票数据\sz50_stocks.csv
2025-08-05 17:45:24,391 - INFO - 股票代码清单已保存到: D:\开发项目\Stock Models\RLStrategy_V2.0（SAC算法）\0-数据汇总\1-原始股票数据\sz50_codes.txt
2025-08-05 17:45:24,391 - INFO - 股票代码示例: ['sh.600028', 'sh.600030', 'sh.600031', 'sh.600036', 'sh.600048']
2025-08-05 17:45:24,400 - INFO - baostock登出成功
2025-08-05 17:45:24,407 - INFO - 从文件加载了 50 只股票代码
2025-08-05 18:35:01,865 - INFO - 开始更新上证50股票清单...
2025-08-05 18:35:01,912 - INFO - baostock登录成功
2025-08-05 18:35:01,997 - INFO - 成功获取上证50成分股，共 50 只股票
2025-08-05 18:35:02,001 - INFO - 股票清单已保存到: D:\开发项目\Stock Models\RLStrategy_V2.0（SAC算法）\0-数据汇总\1-原始股票数据\sz50_stocks.csv
2025-08-05 18:35:02,003 - INFO - 股票代码清单已保存到: D:\开发项目\Stock Models\RLStrategy_V2.0（SAC算法）\0-数据汇总\1-原始股票数据\sz50_codes.txt
2025-08-05 18:35:02,003 - INFO - 股票代码示例: ['sh.600028', 'sh.600030', 'sh.600031', 'sh.600036', 'sh.600048']
2025-08-05 18:35:03,971 - INFO - baostock登出成功
2025-08-05 18:35:03,971 - INFO - 从文件加载了 50 只股票代码
