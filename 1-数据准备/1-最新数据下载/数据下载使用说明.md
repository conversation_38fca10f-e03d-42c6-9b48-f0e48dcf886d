# 股票数据下载模块使用说明

## 📋 概述

本模块使用 baostock 接口下载A股历史K线数据，支持沪深300、上证50、中证500三大指数成分股的完整数据下载。<mcreference link="http://baostock.com/baostock/index.php/A%E8%82%A1K%E7%BA%BF%E6%95%B0%E6%8D%AE" index="0">0</mcreference>

## ✅ 数据字段验证

**已验证所有下载的数据包含完整的18个必需字段：**

| 字段名 | 说明 | 示例值 |
|--------|------|--------|
| `date` | 交易日期 | 2024-01-15 |
| `code` | 股票代码 | sh.600000 |
| `open` | 开盘价（后复权） | 10.50 |
| `high` | 最高价（后复权） | 10.80 |
| `low` | 最低价（后复权） | 10.40 |
| `close` | 收盘价（后复权） | 10.75 |
| `preclose` | 前收盘价 | 10.46 |
| `volume` | 成交量 | 1000000 |
| `amount` | 成交额 | 10750000.0 |
| `adjustflag` | 复权类型 | 1 |
| `turn` | 换手率(%) | 2.5 |
| `tradestatus` | 交易状态 | 1 |
| `pctChg` | 涨跌幅(%) | 2.87 |
| `peTTM` | 市盈率TTM | 15.6 |
| `psTTM` | 市销率TTM | 2.3 |
| `pcfNcfTTM` | 市现率TTM | 8.9 |
| `pbMRQ` | 市净率MRQ | 1.2 |
| `isST` | 是否ST股票 | 0 |

## 🚀 快速开始

### 1. 更新股票清单

```bash
# 更新沪深300成分股清单
python get_hs300_stocks.py

# 更新上证50成分股清单
python get_sz50_stocks.py

# 更新中证500成分股清单
python get_zz500_stocks.py
```

### 2. 下载股票数据

```bash
# 测试模式（下载前10只股票）
python download_latest_data.py test

# 下载沪深300成分股
python download_latest_data.py hs300

# 下载上证50成分股
python download_latest_data.py sz50

# 下载中证500成分股
python download_latest_data.py zz500

# 更新最近30天数据
python download_latest_data.py update

# 全新下载（清除进度重新开始）
python download_latest_data.py fresh
```

### 3. 验证数据完整性

```bash
# 验证所有下载数据的字段完整性
python verify_data_fields.py
```

## 📊 当前数据状态

- **总文件数**: 303个股票数据文件
- **字段完整性**: 100%（所有文件都包含18个必需字段）
- **数据格式**: CSV格式，UTF-8编码
- **复权方式**: 后复权（adjustflag=1）
- **数据范围**: 2000年至今

## 📁 文件结构

```
1-最新数据下载/
├── download_latest_data.py     # 主下载脚本
├── get_hs300_stocks.py        # 沪深300清单更新
├── get_sz50_stocks.py         # 上证50清单更新
├── get_zz500_stocks.py        # 中证500清单更新
├── verify_data_fields.py      # 数据验证脚本
├── README.md                  # 详细文档
├── 快速开始.md                # 快速入门
└── 数据下载使用说明.md        # 本文档
```

## 🎯 支持的指数

### 沪深300 (HS300)
- **股票数量**: 300只
- **清单文件**: `hs300_codes.txt`
- **下载命令**: `python download_latest_data.py hs300`

### 上证50 (SZ50)
- **股票数量**: 50只
- **清单文件**: `sz50_codes.txt`
- **下载命令**: `python download_latest_data.py sz50`

### 中证500 (ZZ500)
- **股票数量**: 500只
- **清单文件**: `zz500_codes.txt`
- **下载命令**: `python download_latest_data.py zz500`

## 💡 使用建议

1. **首次使用**: 建议先运行 `test` 模式验证环境
2. **定期更新**: 建议每周运行一次 `update` 模式
3. **清单更新**: 每月更新一次股票清单
4. **数据验证**: 下载完成后运行验证脚本
5. **存储空间**: 全量数据约需要数GB空间

## ⚠️ 注意事项

- **网络要求**: 需要稳定的网络连接访问baostock服务
- **下载时间**: 完整下载可能需要数小时
- **数据延迟**: baostock数据可能有1-2天延迟
- **API限制**: 请合理控制下载频率
- **基本面数据**: peTTM、psTTM、pcfNcfTTM、pbMRQ等字段已包含

## 🔧 编程接口

```python
from download_latest_data import StockDataDownloader

# 创建下载器
downloader = StockDataDownloader()

# 下载指定股票
if downloader.login_baostock():
    success = downloader.download_stock_data('sh.600000')
    downloader.logout_baostock()
    print(f"下载结果: {success}")

# 批量下载
result = downloader.download_all_stocks(
    stock_list_type='hs300',  # 或 'sz50', 'zz500'
    max_stocks=10  # 限制数量（测试用）
)
print(f"下载统计: {result}")
```

## 📞 故障排除

### 常见问题

**Q: 登录baostock失败？**
```bash
pip install baostock --upgrade
```

**Q: 某些股票下载失败？**
- 检查股票代码格式
- 确认股票是否停牌或退市
- 查看错误日志

**Q: 基本面数据为空？**
- 部分股票可能缺少基本面数据
- 这是正常现象，系统会自动处理

**Q: 下载速度慢？**
- baostock服务器可能繁忙
- 建议错峰下载

## 📈 数据质量保证

✅ **字段完整性**: 100%验证通过  
✅ **数据格式**: 标准CSV格式  
✅ **编码方式**: UTF-8编码  
✅ **复权处理**: 后复权算法  
✅ **基本面数据**: 包含估值指标  
✅ **技术指标**: 支持价格、成交量等  

---

**最后更新**: 2025-08-05  
**验证状态**: ✅ 所有303个文件字段完整  
**数据来源**: baostock.com