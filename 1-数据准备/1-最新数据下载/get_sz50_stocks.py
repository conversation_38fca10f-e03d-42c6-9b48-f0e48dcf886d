#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取上证50成分股清单
使用baostock接口获取上证50成分股信息
保存股票清单到0-数据汇总/1-原始股票数据目录
"""

import baostock as bs
import pandas as pd
import os
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sz50_log.txt', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SZ50StockListManager:
    """上证50股票清单管理器"""
    
    def __init__(self):
        # 使用绝对路径确保数据保存到正确位置
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(current_dir))
        self.data_dir = os.path.join(project_root, "0-数据汇总", "1-原始股票数据")
        self.stock_list_file = os.path.join(self.data_dir, "sz50_stocks.csv")
        self.stock_codes_file = os.path.join(self.data_dir, "sz50_codes.txt")
        self.ensure_data_dir()
        
    def ensure_data_dir(self):
        """确保数据目录存在"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir, exist_ok=True)
            logger.info(f"创建数据目录: {self.data_dir}")
        
    def login_baostock(self) -> bool:
        """登录baostock系统"""
        try:
            lg = bs.login()
            if lg.error_code == '0':
                logger.info("baostock登录成功")
                return True
            else:
                logger.error(f"baostock登录失败: {lg.error_msg}")
                return False
        except Exception as e:
            logger.error(f"baostock登录异常: {str(e)}")
            return False
    
    def logout_baostock(self):
        """登出baostock系统"""
        try:
            bs.logout()
            logger.info("baostock登出成功")
        except Exception as e:
            logger.error(f"baostock登出异常: {str(e)}")
    
    def get_sz50_stocks(self, date: str = None) -> pd.DataFrame:
        """获取上证50成分股
        
        Args:
            date: 查询日期，格式YYYY-MM-DD，为空时默认最新日期
            
        Returns:
            pd.DataFrame: 上证50成分股数据
        """
        try:
            # 获取上证50成分股
            if date:
                rs = bs.query_sz50_stocks(date=date)
            else:
                rs = bs.query_sz50_stocks()
                
            if rs.error_code != '0':
                logger.error(f"获取上证50成分股失败: {rs.error_msg}")
                return pd.DataFrame()
            
            # 收集数据
            sz50_stocks = []
            while rs.next():
                sz50_stocks.append(rs.get_row_data())
            
            if not sz50_stocks:
                logger.warning("未获取到上证50成分股数据")
                return pd.DataFrame()
            
            # 转换为DataFrame
            df = pd.DataFrame(sz50_stocks, columns=rs.fields)
            logger.info(f"成功获取上证50成分股，共 {len(df)} 只股票")
            
            return df
            
        except Exception as e:
            logger.error(f"获取上证50成分股异常: {str(e)}")
            return pd.DataFrame()
    
    def save_stock_list(self, df: pd.DataFrame) -> bool:
        """保存股票清单到文件
        
        Args:
            df: 股票数据DataFrame
            
        Returns:
            bool: 保存是否成功
        """
        try:
            if df.empty:
                logger.error("股票数据为空，无法保存")
                return False
            
            # 保存完整数据到CSV
            df.to_csv(self.stock_list_file, encoding='utf-8', index=False)
            logger.info(f"股票清单已保存到: {self.stock_list_file}")
            
            # 提取股票代码并保存到文本文件
            if 'code' in df.columns:
                stock_codes = df['code'].tolist()
                with open(self.stock_codes_file, 'w', encoding='utf-8') as f:
                    for code in stock_codes:
                        f.write(f"{code}\n")
                logger.info(f"股票代码清单已保存到: {self.stock_codes_file}")
                logger.info(f"股票代码示例: {stock_codes[:5]}")
            
            return True
            
        except Exception as e:
            logger.error(f"保存股票清单异常: {str(e)}")
            return False
    
    def load_stock_codes(self) -> list:
        """从文件加载股票代码清单
        
        Returns:
            list: 股票代码列表
        """
        try:
            if not os.path.exists(self.stock_codes_file):
                logger.warning(f"股票代码文件不存在: {self.stock_codes_file}")
                return []
            
            with open(self.stock_codes_file, 'r', encoding='utf-8') as f:
                codes = [line.strip() for line in f.readlines() if line.strip()]
            
            logger.info(f"从文件加载了 {len(codes)} 只股票代码")
            return codes
            
        except Exception as e:
            logger.error(f"加载股票代码异常: {str(e)}")
            return []
    
    def update_sz50_list(self, date: str = None) -> bool:
        """更新上证50股票清单
        
        Args:
            date: 查询日期，格式YYYY-MM-DD
            
        Returns:
            bool: 更新是否成功
        """
        logger.info("开始更新上证50股票清单...")
        
        # 登录baostock
        if not self.login_baostock():
            return False
        
        try:
            # 获取股票清单
            df = self.get_sz50_stocks(date)
            
            if df.empty:
                logger.error("获取股票清单失败")
                return False
            
            # 保存股票清单
            success = self.save_stock_list(df)
            
            return success
            
        finally:
            # 登出baostock
            self.logout_baostock()

def main():
    """主函数"""
    import sys
    
    manager = SZ50StockListManager()
    
    # 检查命令行参数
    date = None
    if len(sys.argv) > 1:
        date = sys.argv[1]
        logger.info(f"使用指定日期: {date}")
    
    # 更新股票清单
    success = manager.update_sz50_list(date)
    
    if success:
        print("\n✅ 上证50股票清单更新成功！")
        print(f"📁 完整清单: {manager.stock_list_file}")
        print(f"📄 代码清单: {manager.stock_codes_file}")
        
        # 显示部分股票信息
        codes = manager.load_stock_codes()
        if codes:
            print(f"\n📊 共获取 {len(codes)} 只股票")
            print(f"🔍 前10只股票: {codes[:10]}")
    else:
        print("❌ 上证50股票清单更新失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()