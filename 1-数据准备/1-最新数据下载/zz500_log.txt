2025-08-05 17:57:02,139 - INFO - 开始更新中证500股票清单...
2025-08-05 17:57:02,383 - INFO - baostock登录成功
2025-08-05 17:57:03,177 - INFO - 成功获取中证500成分股，共 500 只股票
2025-08-05 17:57:03,181 - INFO - 股票清单已保存到: D:\开发项目\Stock Models\RLStrategy_V2.0（SAC算法）\0-数据汇总\1-原始股票数据\zz500_stocks.csv
2025-08-05 17:57:03,181 - INFO - 股票代码清单已保存到: D:\开发项目\Stock Models\RLStrategy_V2.0（SAC算法）\0-数据汇总\1-原始股票数据\zz500_codes.txt
2025-08-05 17:57:03,181 - INFO - 股票代码示例: ['sh.600004', 'sh.600007', 'sh.600008', 'sh.600021', 'sh.600032']
2025-08-05 17:57:03,415 - INFO - baostock登出成功
2025-08-05 17:57:03,416 - INFO - 从文件加载了 500 只股票代码
2025-08-05 18:35:11,803 - INFO - 开始更新中证500股票清单...
2025-08-05 18:35:12,844 - INFO - baostock登录成功
2025-08-05 18:35:13,587 - INFO - 成功获取中证500成分股，共 500 只股票
2025-08-05 18:35:13,591 - INFO - 股票清单已保存到: D:\开发项目\Stock Models\RLStrategy_V2.0（SAC算法）\0-数据汇总\1-原始股票数据\zz500_stocks.csv
2025-08-05 18:35:13,591 - INFO - 股票代码清单已保存到: D:\开发项目\Stock Models\RLStrategy_V2.0（SAC算法）\0-数据汇总\1-原始股票数据\zz500_codes.txt
2025-08-05 18:35:13,593 - INFO - 股票代码示例: ['sh.600004', 'sh.600007', 'sh.600008', 'sh.600021', 'sh.600032']
2025-08-05 18:35:13,618 - INFO - baostock登出成功
2025-08-05 18:35:13,619 - INFO - 从文件加载了 500 只股票代码
