#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据下载脚本
使用baostock接口下载A股历史K线数据（后复权）
数据保存到0-数据汇总/1-原始股票数据目录
"""

import baostock as bs
import pandas as pd
import os
from datetime import datetime, timedelta
import logging
from typing import List, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('download_log.txt', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class StockDataDownloader:
    """股票数据下载器"""
    
    def __init__(self):
        # 使用绝对路径确保数据保存到正确位置
        import os
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(current_dir))
        self.data_dir = os.path.join(project_root, "0-数据汇总", "1-原始股票数据")
        self.progress_file = os.path.join(current_dir, "download_progress.txt")
        self.ensure_data_dir()
        
    def ensure_data_dir(self):
        """确保数据目录存在"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir, exist_ok=True)
            logger.info(f"创建数据目录: {self.data_dir}")
    
    def load_progress(self) -> set:
        """加载下载进度"""
        completed_stocks = set()
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        stock_code = line.strip()
                        if stock_code:
                            completed_stocks.add(stock_code)
                logger.info(f"加载进度文件，已完成 {len(completed_stocks)} 只股票")
            except Exception as e:
                logger.error(f"读取进度文件失败: {str(e)}")
        return completed_stocks
    
    def save_progress(self, stock_code: str):
        """保存下载进度"""
        try:
            with open(self.progress_file, 'a', encoding='utf-8') as f:
                f.write(f"{stock_code}\n")
        except Exception as e:
            logger.error(f"保存进度失败: {str(e)}")
    
    def clear_progress(self):
        """清除进度文件"""
        try:
            if os.path.exists(self.progress_file):
                os.remove(self.progress_file)
                logger.info("已清除进度文件")
        except Exception as e:
            logger.error(f"清除进度文件失败: {str(e)}")
    
    def is_stock_downloaded(self, stock_code: str) -> bool:
        """检查股票数据是否已存在"""
        filename = self.get_filename(stock_code)
        filepath = os.path.join(self.data_dir, filename)
        return os.path.exists(filepath)
    
    def login_baostock(self) -> bool:
        """登录baostock系统"""
        try:
            lg = bs.login()
            if lg.error_code == '0':
                logger.info("baostock登录成功")
                return True
            else:
                logger.error(f"baostock登录失败: {lg.error_msg}")
                return False
        except Exception as e:
            logger.error(f"baostock登录异常: {str(e)}")
            return False
    
    def logout_baostock(self):
        """登出baostock系统"""
        try:
            bs.logout()
            logger.info("baostock登出成功")
        except Exception as e:
            logger.error(f"baostock登出异常: {str(e)}")
    
    def get_stock_list(self) -> List[str]:
        """获取股票代码列表
        
        优先级：
        1. 沪深300清单（如果存在）
        2. 上证50清单（如果存在）
        3. 中证500清单（如果存在）
        4. baostock全股票列表
        5. 默认股票列表
        """
        # 首先尝试使用沪深300清单
        hs300_codes = self.get_hs300_stock_list()
        if hs300_codes:
            logger.info(f"使用沪深300股票清单，共 {len(hs300_codes)} 只股票")
            return hs300_codes
        
        # 如果没有沪深300清单，尝试使用上证50清单
        sz50_codes = self.get_sz50_stock_list()
        if sz50_codes:
            logger.info(f"使用上证50股票清单，共 {len(sz50_codes)} 只股票")
            return sz50_codes
        
        # 如果没有上证50清单，尝试使用中证500清单
        zz500_codes = self.get_zz500_stock_list()
        if zz500_codes:
            logger.info(f"使用中证500股票清单，共 {len(zz500_codes)} 只股票")
            return zz500_codes
        
        # 如果没有中证500清单，尝试从baostock获取全股票列表
        stock_codes = []
        
        try:
            # 获取沪深A股基本信息
            rs = bs.query_all_stock()
            
            if rs.error_code != '0':
                logger.error(f"获取股票列表失败: {rs.error_msg}")
                return self.get_default_stock_list()
            
            while rs.next():
                row = rs.get_row_data()
                code = row[0]  # 股票代码
                stock_type = row[1]  # 股票类型
                
                # 只获取A股（股票类型为1，且代码以sh.或sz.开头）
                if stock_type == '1' and (code.startswith('sh.') or code.startswith('sz.')):
                    # 排除指数代码（通常是3位数字）
                    stock_num = code.split('.')[1]
                    if len(stock_num) == 6:  # A股代码都是6位数字
                        stock_codes.append(code)
            
            # 如果没有获取到股票，使用默认列表
            if not stock_codes:
                logger.warning("未能从baostock获取股票列表，使用默认股票列表")
                return self.get_default_stock_list()
            
            logger.info(f"从baostock获取到 {len(stock_codes)} 只股票")
            return stock_codes
            
        except Exception as e:
            logger.error(f"获取股票列表异常: {str(e)}")
            return self.get_default_stock_list()
    
    def get_hs300_stock_list(self) -> List[str]:
        """从文件读取沪深300股票代码列表"""
        hs300_file = os.path.join(self.data_dir, "hs300_codes.txt")
        try:
            if os.path.exists(hs300_file):
                with open(hs300_file, 'r', encoding='utf-8') as f:
                    codes = [line.strip() for line in f.readlines() if line.strip()]
                if codes:
                    logger.info(f"从沪深300清单加载了 {len(codes)} 只股票")
                    return codes
            else:
                logger.warning(f"沪深300清单文件不存在: {hs300_file}")
        except Exception as e:
            logger.error(f"读取沪深300清单异常: {str(e)}")
        return []
    
    def get_sz50_stock_list(self) -> List[str]:
        """从文件读取上证50股票代码列表"""
        sz50_file = os.path.join(self.data_dir, "sz50_codes.txt")
        try:
            if os.path.exists(sz50_file):
                with open(sz50_file, 'r', encoding='utf-8') as f:
                    codes = [line.strip() for line in f.readlines() if line.strip()]
                if codes:
                    logger.info(f"从上证50清单加载了 {len(codes)} 只股票")
                    return codes
                else:
                    logger.warning("上证50清单文件为空")
            else:
                logger.warning(f"上证50清单文件不存在: {sz50_file}")
        except Exception as e:
            logger.error(f"读取上证50清单异常: {str(e)}")
        
        # 如果读取失败，返回空列表
        return []
    
    def get_zz500_stock_list(self) -> List[str]:
        """从文件读取中证500股票代码列表"""
        zz500_file = os.path.join(self.data_dir, "zz500_codes.txt")
        try:
            if os.path.exists(zz500_file):
                with open(zz500_file, 'r', encoding='utf-8') as f:
                    codes = [line.strip() for line in f.readlines() if line.strip()]
                if codes:
                    logger.info(f"从中证500清单加载了 {len(codes)} 只股票")
                    return codes
                else:
                    logger.warning("中证500清单文件为空")
            else:
                logger.warning(f"中证500清单文件不存在: {zz500_file}")
        except Exception as e:
            logger.error(f"读取中证500清单异常: {str(e)}")
        
        # 如果读取失败，返回空列表
        return []
    
    def get_default_stock_list(self) -> List[str]:
        """获取默认股票代码列表（主要的大盘股）"""
        # 主要的大盘股和知名股票
        default_stocks = [
            # 银行股
            'sh.600000',  # 浦发银行
            'sh.600036',  # 招商银行
            'sh.601398',  # 工商银行
            'sh.601939',  # 建设银行
            'sh.601988',  # 中国银行
            'sz.000001',  # 平安银行
            
            # 白酒股
            'sh.600519',  # 贵州茅台
            'sz.000858',  # 五粮液
            'sz.000596',  # 古井贡酒
            
            # 科技股
            'sz.000002',  # 万科A
            'sh.600276',  # 恒瑞医药
            'sz.002415',  # 海康威视
            'sz.000725',  # 京东方A
            
            # 其他大盘股
            'sh.600104',  # 上汽集团
            'sh.600887',  # 伊利股份
            'sh.601318',  # 中国平安
            'sh.600585',  # 海螺水泥
            'sz.002594',  # 比亚迪
        ]
        
        logger.info(f"使用默认股票列表，共 {len(default_stocks)} 只股票")
        return default_stocks
    
    def download_stock_data(self, stock_code: str, start_date: str = None, end_date: str = None) -> bool:
        """下载单只股票的历史数据
        
        Args:
            stock_code: 股票代码，如 'sh.600000'
            start_date: 开始日期，格式 'YYYY-MM-DD'
            end_date: 结束日期，格式 'YYYY-MM-DD'
        
        Returns:
            bool: 下载是否成功
        """
        try:
            # 设置默认日期范围
            if not start_date:
                start_date = '2000-01-01'  # 默认从2000年开始
            if not end_date:
                end_date = datetime.now().strftime('%Y-%m-%d')
            
            # 下载日线数据（后复权）
            # 包含所有用户要求的字段：date,code,open,high,low,close,preclose,volume,amount,adjustflag,turn,tradestatus,pctChg,peTTM,psTTM,pcfNcfTTM,pbMRQ,isST
            rs = bs.query_history_k_data_plus(
                stock_code,
                "date,code,open,high,low,close,preclose,volume,amount,adjustflag,turn,tradestatus,pctChg,peTTM,psTTM,pcfNcfTTM,pbMRQ,isST",
                start_date=start_date,
                end_date=end_date,
                frequency="d",  # 日线
                adjustflag="1"  # 1=后复权
            )
            
            if rs.error_code != '0':
                logger.warning(f"下载 {stock_code} 数据失败: {rs.error_msg}")
                return False
            
            # 收集数据
            data_list = []
            while rs.next():
                data_list.append(rs.get_row_data())
            
            if not data_list:
                logger.warning(f"股票 {stock_code} 无数据")
                return False
            
            # 转换为DataFrame
            df = pd.DataFrame(data_list, columns=rs.fields)
            
            # 数据清洗
            df = self.clean_data(df)
            
            # 保存数据
            filename = self.get_filename(stock_code)
            filepath = os.path.join(self.data_dir, filename)
            
            # 保存为CSV格式文件
            df.to_csv(filepath, index=False, encoding='utf-8')
            
            logger.info(f"成功下载并保存 {stock_code} 数据，共 {len(df)} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"下载 {stock_code} 数据异常: {str(e)}")
            return False
    
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗数据"""
        # 转换数据类型
        numeric_columns = ['open', 'high', 'low', 'close', 'preclose', 'volume', 'amount', 'pctChg']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 处理换手率（可能为空字符串）
        if 'turn' in df.columns:
            df['turn'] = df['turn'].apply(lambda x: 0 if x == "" else float(x) if x else 0)
        
        # 按日期排序
        if 'date' in df.columns:
            df = df.sort_values('date')
        
        # 去除重复数据
        df = df.drop_duplicates()
        
        return df
    
    def get_filename(self, stock_code: str) -> str:
        """生成文件名
        
        Args:
            stock_code: 股票代码，如 'sh.600000'
        
        Returns:
            str: 文件名，如 'sh.600000_hfq.csv'
        """
        return f"{stock_code}_hfq.csv"
    
    def download_all_stocks(self, start_date: str = None, end_date: str = None, 
                           max_stocks: Optional[int] = None, resume: bool = True, 
                           stock_list_type: str = None) -> dict:
        """下载所有股票数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            max_stocks: 最大下载股票数量（用于测试）
            resume: 是否启用断点续传
            stock_list_type: 指定股票清单类型 ('hs300', 'sz50', 'zz500', None)
        
        Returns:
            dict: 下载结果统计
        """
        if not self.login_baostock():
            return {'success': 0, 'failed': 0, 'total': 0}
        
        try:
            # 根据指定类型获取股票列表
            if stock_list_type == 'hs300':
                stock_codes = self.get_hs300_stock_list()
                if not stock_codes:
                    logger.error("沪深300股票清单为空")
                    return {'success': 0, 'failed': 0, 'total': 0}
            elif stock_list_type == 'sz50':
                stock_codes = self.get_sz50_stock_list()
                if not stock_codes:
                    logger.error("上证50股票清单为空")
                    return {'success': 0, 'failed': 0, 'total': 0}
            elif stock_list_type == 'zz500':
                stock_codes = self.get_zz500_stock_list()
                if not stock_codes:
                    logger.error("中证500股票清单为空")
                    return {'success': 0, 'failed': 0, 'total': 0}
            else:
                # 使用默认优先级获取股票列表
                stock_codes = self.get_stock_list()
            
            if max_stocks:
                stock_codes = stock_codes[:max_stocks]
                logger.info(f"测试模式：只下载前 {max_stocks} 只股票")
            
            # 加载进度（如果启用断点续传）
            completed_stocks = set()
            if resume:
                completed_stocks = self.load_progress()
                # 过滤掉已完成的股票
                remaining_stocks = []
                for stock_code in stock_codes:
                    if stock_code not in completed_stocks and not self.is_stock_downloaded(stock_code):
                        remaining_stocks.append(stock_code)
                    elif stock_code in completed_stocks or self.is_stock_downloaded(stock_code):
                        logger.debug(f"跳过已下载的股票: {stock_code}")
                
                stock_codes = remaining_stocks
                if completed_stocks:
                    logger.info(f"断点续传：跳过已完成的 {len(completed_stocks)} 只股票，剩余 {len(stock_codes)} 只")
            else:
                # 如果不启用断点续传，清除进度文件
                self.clear_progress()
            
            success_count = 0
            failed_count = 0
            total_count = len(stock_codes)
            skipped_count = len(completed_stocks) if resume else 0
            
            logger.info(f"开始下载 {total_count} 只股票的数据...")
            
            for i, stock_code in enumerate(stock_codes, 1):
                logger.info(f"正在下载 {i}/{total_count}: {stock_code}")
                
                if self.download_stock_data(stock_code, start_date, end_date):
                    success_count += 1
                    # 保存进度
                    if resume:
                        self.save_progress(stock_code)
                else:
                    failed_count += 1
                
                # 每下载50只股票显示一次进度
                if i % 50 == 0:
                    logger.info(f"进度: {i}/{total_count}, 成功: {success_count}, 失败: {failed_count}")
            
            result = {
                'success': success_count,
                'failed': failed_count,
                'total': total_count,
                'skipped': skipped_count
            }
            
            logger.info(f"下载完成！总计: {total_count}, 成功: {success_count}, 失败: {failed_count}, 跳过: {skipped_count}")
            
            # 如果全部成功，清除进度文件
            if failed_count == 0 and resume:
                self.clear_progress()
                logger.info("所有股票下载完成，已清除进度文件")
            
            return result
            
        finally:
            self.logout_baostock()
    
    def update_stock_data(self, stock_codes: List[str] = None, days_back: int = 30) -> dict:
        """更新股票数据（增量更新）
        
        Args:
            stock_codes: 指定股票代码列表，为空则更新所有股票
            days_back: 向前更新的天数
        
        Returns:
            dict: 更新结果统计
        """
        if not self.login_baostock():
            return {'success': 0, 'failed': 0, 'total': 0}
        
        try:
            # 计算更新日期范围
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
            
            if not stock_codes:
                stock_codes = self.get_stock_list()
            
            success_count = 0
            failed_count = 0
            total_count = len(stock_codes)
            
            logger.info(f"开始更新 {total_count} 只股票的数据（{start_date} 到 {end_date}）...")
            
            for i, stock_code in enumerate(stock_codes, 1):
                logger.info(f"正在更新 {i}/{total_count}: {stock_code}")
                
                if self.download_stock_data(stock_code, start_date, end_date):
                    success_count += 1
                else:
                    failed_count += 1
            
            result = {
                'success': success_count,
                'failed': failed_count,
                'total': total_count
            }
            
            logger.info(f"更新完成！总计: {total_count}, 成功: {success_count}, 失败: {failed_count}")
            return result
            
        finally:
            self.logout_baostock()

def main():
    """主函数"""
    downloader = StockDataDownloader()
    
    # 可以选择不同的运行模式
    import sys
    
    if len(sys.argv) > 1:
        mode = sys.argv[1]
        # 检查是否有第二个参数（股票清单类型）
        stock_list_type = None
        if len(sys.argv) > 2:
            second_arg = sys.argv[2]
            if second_arg in ['hs300', 'sz50', 'zz500']:
                stock_list_type = second_arg
        
        if mode == 'test':
            # 测试模式：只下载前10只股票
            logger.info("运行测试模式")
            result = downloader.download_all_stocks(max_stocks=10)
            
        elif mode == 'update':
            # 更新模式：更新最近30天的数据
            logger.info("运行更新模式")
            result = downloader.update_stock_data(days_back=30)
            
        elif mode == 'full':
            # 完整模式：下载所有股票的完整历史数据
            logger.info("运行完整下载模式")
            result = downloader.download_all_stocks()
            
        elif mode == 'hs300':
            # 沪深300模式：下载沪深300成分股
            logger.info("运行沪深300下载模式")
            # 检查是否存在沪深300清单
            hs300_file = os.path.join(downloader.data_dir, "hs300_codes.txt")
            if not os.path.exists(hs300_file):
                print("❌ 沪深300清单文件不存在！")
                print("请先运行: python get_hs300_stocks.py")
                return
            result = downloader.download_all_stocks(stock_list_type='hs300')
            
        elif mode == 'sz50':
            # 上证50模式：下载上证50成分股
            logger.info("运行上证50下载模式")
            # 检查是否存在上证50清单
            sz50_file = os.path.join(downloader.data_dir, "sz50_codes.txt")
            if not os.path.exists(sz50_file):
                print("❌ 上证50清单文件不存在！")
                print("请先运行: python get_sz50_stocks.py")
                return
            result = downloader.download_all_stocks(stock_list_type='sz50')
            
        elif mode == 'zz500':
            # 中证500模式：下载中证500成分股
            logger.info("运行中证500下载模式")
            # 检查是否存在中证500清单
            zz500_file = os.path.join(downloader.data_dir, "zz500_codes.txt")
            if not os.path.exists(zz500_file):
                print("❌ 中证500清单文件不存在！")
                print("请先运行: python get_zz500_stocks.py")
                return
            result = downloader.download_all_stocks(stock_list_type='zz500')
            
        elif mode == 'resume':
            # 断点续传模式：从上次中断的地方继续下载
            if stock_list_type:
                logger.info(f"运行断点续传模式 - {stock_list_type}")
            else:
                logger.info("运行断点续传模式")
            result = downloader.download_all_stocks(resume=True, stock_list_type=stock_list_type)
            
        elif mode == 'fresh':
            # 全新下载模式：清除进度重新开始
            if stock_list_type:
                logger.info(f"运行全新下载模式 - {stock_list_type}")
            else:
                logger.info("运行全新下载模式")
            downloader.clear_progress()
            result = downloader.download_all_stocks(resume=False, stock_list_type=stock_list_type)
            
        else:
            print("使用方法:")
            print("  python download_latest_data.py test    # 测试模式（下载前10只股票）")
            print("  python download_latest_data.py update  # 更新模式（更新最近30天）")
            print("  python download_latest_data.py full    # 完整模式（下载所有历史数据）")
            print("  python download_latest_data.py hs300   # 沪深300模式（下载沪深300成分股）")
            print("  python download_latest_data.py sz50    # 上证50模式（下载上证50成分股）")
            print("  python download_latest_data.py zz500   # 中证500模式（下载中证500成分股）")
            print("  python download_latest_data.py resume  # 断点续传模式（从中断处继续）")
            print("  python download_latest_data.py fresh   # 全新下载模式（清除进度重新开始）")
            print("")
            print("注意：")
            print("  - 使用hs300模式前，请先运行 python get_hs300_stocks.py 获取股票清单")
            print("  - 使用sz50模式前，请先运行 python get_sz50_stocks.py 获取股票清单")
            print("  - 使用zz500模式前，请先运行 python get_zz500_stocks.py 获取股票清单")
            print("  - resume模式会自动跳过已下载的股票，适合网络中断后继续下载")
            print("  - fresh模式会清除所有进度，重新开始下载")
            return
    else:
        # 默认运行沪深300模式（支持断点续传）
        logger.info("运行默认沪深300模式（支持断点续传）")
        # 检查是否存在沪深300清单
        hs300_file = os.path.join(downloader.data_dir, "hs300_codes.txt")
        if not os.path.exists(hs300_file):
            print("❌ 沪深300清单文件不存在！")
            print("请先运行: python get_hs300_stocks.py")
            return
        result = downloader.download_all_stocks(resume=True, stock_list_type='hs300')
    
    print(f"\n下载结果: {result}")

if __name__ == "__main__":
    main()