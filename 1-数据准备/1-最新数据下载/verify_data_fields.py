#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证下载数据的字段完整性
检查所有股票数据文件是否包含必需的字段
"""

import pandas as pd
import os
import logging
from typing import List, Set

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataFieldVerifier:
    """数据字段验证器"""
    
    def __init__(self):
        # 必需的字段列表
        self.required_fields = {
            'date', 'code', 'open', 'high', 'low', 'close', 'preclose',
            'volume', 'amount', 'adjustflag', 'turn', 'tradestatus', 
            'pctChg', 'peTTM', 'psTTM', 'pcfNcfTTM', 'pbMRQ', 'isST'
        }
        
        # 数据目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(current_dir))
        self.data_dir = os.path.join(project_root, "0-数据汇总", "1-原始股票数据")
        
    def get_csv_files(self) -> List[str]:
        """获取所有CSV文件"""
        csv_files = []
        if os.path.exists(self.data_dir):
            for file in os.listdir(self.data_dir):
                if file.endswith('_hfq.csv'):
                    csv_files.append(os.path.join(self.data_dir, file))
        return csv_files
    
    def verify_file_fields(self, file_path: str) -> dict:
        """验证单个文件的字段"""
        try:
            # 只读取第一行来检查字段
            df = pd.read_csv(file_path, nrows=1)
            file_fields = set(df.columns)
            
            missing_fields = self.required_fields - file_fields
            extra_fields = file_fields - self.required_fields
            
            return {
                'file': os.path.basename(file_path),
                'total_fields': len(file_fields),
                'missing_fields': list(missing_fields),
                'extra_fields': list(extra_fields),
                'has_all_required': len(missing_fields) == 0,
                'actual_fields': list(file_fields)
            }
            
        except Exception as e:
            logger.error(f"验证文件 {file_path} 时出错: {str(e)}")
            return {
                'file': os.path.basename(file_path),
                'error': str(e),
                'has_all_required': False
            }
    
    def verify_all_files(self) -> dict:
        """验证所有文件"""
        csv_files = self.get_csv_files()
        
        if not csv_files:
            logger.warning(f"在目录 {self.data_dir} 中未找到CSV文件")
            return {'total_files': 0, 'valid_files': 0, 'results': []}
        
        logger.info(f"找到 {len(csv_files)} 个CSV文件，开始验证...")
        
        results = []
        valid_count = 0
        
        for file_path in csv_files:
            result = self.verify_file_fields(file_path)
            results.append(result)
            
            if result.get('has_all_required', False):
                valid_count += 1
            else:
                logger.warning(f"文件 {result['file']} 字段不完整")
                if 'missing_fields' in result and result['missing_fields']:
                    logger.warning(f"  缺少字段: {result['missing_fields']}")
        
        return {
            'total_files': len(csv_files),
            'valid_files': valid_count,
            'invalid_files': len(csv_files) - valid_count,
            'results': results
        }
    
    def print_summary(self, verification_result: dict):
        """打印验证摘要"""
        print("\n" + "="*60)
        print("📊 数据字段验证报告")
        print("="*60)
        
        print(f"📁 数据目录: {self.data_dir}")
        print(f"📄 总文件数: {verification_result['total_files']}")
        print(f"✅ 字段完整文件: {verification_result['valid_files']}")
        print(f"❌ 字段不完整文件: {verification_result['invalid_files']}")
        
        if verification_result['total_files'] > 0:
            success_rate = (verification_result['valid_files'] / verification_result['total_files']) * 100
            print(f"📈 成功率: {success_rate:.1f}%")
        
        print(f"\n🔍 必需字段 ({len(self.required_fields)} 个):")
        for field in sorted(self.required_fields):
            print(f"  - {field}")
        
        # 显示有问题的文件
        invalid_files = [r for r in verification_result['results'] if not r.get('has_all_required', False)]
        if invalid_files:
            print(f"\n❌ 字段不完整的文件 ({len(invalid_files)} 个):")
            for result in invalid_files[:10]:  # 只显示前10个
                print(f"  📄 {result['file']}")
                if 'missing_fields' in result and result['missing_fields']:
                    print(f"     缺少: {', '.join(result['missing_fields'])}")
                if 'error' in result:
                    print(f"     错误: {result['error']}")
            
            if len(invalid_files) > 10:
                print(f"     ... 还有 {len(invalid_files) - 10} 个文件")
        else:
            print("\n✅ 所有文件字段都完整！")
        
        print("="*60)

def main():
    """主函数"""
    verifier = DataFieldVerifier()
    
    logger.info("开始验证数据字段完整性...")
    result = verifier.verify_all_files()
    
    verifier.print_summary(result)
    
    # 返回验证结果
    if result['total_files'] == 0:
        logger.error("未找到任何数据文件")
        return False
    elif result['invalid_files'] == 0:
        logger.info("所有数据文件字段都完整")
        return True
    else:
        logger.warning(f"有 {result['invalid_files']} 个文件字段不完整")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)