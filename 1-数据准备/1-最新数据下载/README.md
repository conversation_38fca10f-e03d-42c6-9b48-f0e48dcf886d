# 股票数据下载模块

本模块使用baostock接口下载A股历史K线数据（后复权），并保存到项目的数据汇总目录中。

## 功能特点

- 🔄 **自动下载**: 支持下载所有A股的历史K线数据
- 📈 **后复权数据**: 使用baostock的后复权算法，确保数据准确性
- 💾 **统一存储**: 数据保存到 `0-数据汇总/1-原始股票数据` 目录
- 🏷️ **标准命名**: 文件名格式为 `市场.股票代码_hfq.csv`
- 🔄 **增量更新**: 支持增量更新最近的数据
- 📊 **数据清洗**: 自动处理数据类型转换和异常值
- 📝 **详细日志**: 完整的下载日志记录

## 文件说明

- `download_latest_data.py` - 主要的数据下载脚本
- `example_usage.py` - 使用示例脚本
- `README.md` - 本说明文件

## 安装依赖

确保已安装baostock包：

```bash
pip install baostock>=0.8.8
```

或者安装项目的完整依赖：

```bash
pip install -r ../../requirements.txt
```

## 使用方法

### 1. 命令行使用

```bash
# 测试模式（下载前10只股票）
python download_latest_data.py test

# 更新模式（更新最近30天的数据）
python download_latest_data.py update

# 完整模式（下载所有股票的完整历史数据）
python download_latest_data.py full

# 默认模式（等同于update）
python download_latest_data.py
```

### 2. 编程使用

```python
from download_latest_data import StockDataDownloader

# 创建下载器
downloader = StockDataDownloader()

# 下载指定股票
downloader.login_baostock()
downloader.download_stock_data('sh.600000')  # 下载浦发银行
downloader.logout_baostock()

# 批量更新
result = downloader.update_stock_data(days_back=7)  # 更新最近7天
print(f"更新结果: {result}")

# 下载所有股票（测试用）
result = downloader.download_all_stocks(max_stocks=5)
print(f"下载结果: {result}")
```

### 3. 运行示例

```bash
python example_usage.py
```

## 数据格式

### 输出文件格式

- **文件名**: `市场.股票代码_hfq.csv`
- 例如: `sh.600000_hfq.csv` (浦发银行)
- 例如: `sz.000001_hfq.csv` (平安银行)

- **文件格式**: 逗号分隔的CSV文件
- **编码**: UTF-8

### 数据字段

| 字段名 | 说明 | 示例 |
|--------|------|------|
| date | 交易日期 | 2024-01-15 |
| code | 股票代码 | sh.600000 |
| open | 开盘价 | 10.50 |
| high | 最高价 | 10.80 |
| low | 最低价 | 10.40 |
| close | 收盘价 | 10.75 |
| preclose | 前收盘价 | 10.45 |
| volume | 成交量 | 1000000 |
| amount | 成交额 | 10750000.0 |
| adjustflag | 复权标识 | 1 |
| turn | 换手率 | 0.85 |
| tradestatus | 交易状态 | 1 |
| pctChg | 涨跌幅 | 2.87 |
| isST | 是否ST | 0 |

## 数据说明

### 复权方式

- 使用 **后复权** 数据 (adjustflag=1)
- 采用baostock的"涨跌幅复权法"
- 确保价格数据的连续性和可比性

### 数据范围

- **默认起始日期**: 2000-01-01
- **结束日期**: 当前日期
- **数据频率**: 日线数据
- **股票范围**: 沪深A股（排除指数、ETF等）

### 数据质量

- 自动处理停牌数据
- 转换数据类型（数值型字段）
- 处理换手率空值
- 按日期排序
- 去除重复记录

## 目录结构

```
1-最新数据下载/
├── download_latest_data.py    # 主下载脚本
├── example_usage.py          # 使用示例
├── README.md                 # 说明文档
├── download_log.txt          # 下载日志（运行后生成）
└── data/                     # 本地临时数据（可选）
```

下载的数据保存在：
```
../../0-数据汇总/1-原始股票数据/
├── sh.600000_hfq.csv
├── sh.600036_hfq.csv
├── sz.000001_hfq.csv
└── ...
```

## 性能优化

### 下载策略

1. **增量更新**: 默认只更新最近30天的数据
2. **批量处理**: 支持批量下载多只股票
3. **错误处理**: 单只股票失败不影响其他股票下载
4. **进度显示**: 每100只股票显示一次进度

### 建议用法

- **首次使用**: 运行 `python download_latest_data.py test` 测试
- **日常更新**: 运行 `python download_latest_data.py update`
- **完整下载**: 仅在必要时运行 `python download_latest_data.py full`

## 注意事项

1. **网络连接**: 需要稳定的网络连接访问baostock服务
2. **存储空间**: 全量下载需要较大存储空间（数GB）
3. **下载时间**: 全量下载可能需要数小时
4. **API限制**: baostock可能有访问频率限制
5. **数据延迟**: baostock数据可能有1-2天延迟

## 故障排除

### 常见问题

1. **登录失败**
   - 检查网络连接
   - 确认baostock服务状态
   - 重试登录

2. **下载失败**
   - 检查股票代码格式
   - 确认日期范围有效
   - 查看错误日志

3. **数据缺失**
   - 某些股票可能停牌或退市
   - 检查股票是否为A股
   - 确认日期范围

### 日志文件

下载过程中的详细日志保存在 `download_log.txt` 文件中，包括：
- 登录状态
- 下载进度
- 错误信息
- 统计结果

## 更新日志

- **v1.0.0** (2024-12-19)
  - 初始版本
  - 支持baostock数据下载
  - 后复权数据处理
  - 自动数据清洗
  - 多种运行模式

## 相关链接

- [baostock官方文档](http://baostock.com/)
- [A股K线数据接口说明](http://baostock.com/baostock/index.php/A%E8%82%A1K%E7%BA%BF%E6%95%B0%E6%8D%AE)