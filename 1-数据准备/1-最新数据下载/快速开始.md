# 股票数据下载 - 快速开始

## 🚀 一分钟快速上手

### 1. 安装依赖

```bash
# 安装baostock
python -m pip install baostock --upgrade

# 或安装完整项目依赖
python -m pip install -r ../../requirements.txt
```

### 2. 立即开始下载

```bash
# 测试下载（推荐首次使用）
python download_latest_data.py test

# 更新最近数据
python download_latest_data.py update

# 下载完整历史数据（耗时较长）
python download_latest_data.py full
```

## 📊 下载结果

数据将保存到：`../../0-数据汇总/1-原始股票数据/`

文件格式：`市场.股票代码_hfq.csv`
- 例如：`sh.600000_hfq.csv`（浦发银行后复权数据）
- 例如：`sz.000001_hfq.csv`（平安银行后复权数据）

## 🔧 编程使用

```python
from download_latest_data import StockDataDownloader

# 创建下载器
downloader = StockDataDownloader()

# 下载指定股票
if downloader.login_baostock():
    downloader.download_stock_data('sh.600000')  # 浦发银行
    downloader.logout_baostock()

# 批量更新最近7天数据
result = downloader.update_stock_data(days_back=7)
print(f"更新结果: {result}")
```

## 📋 数据字段说明

| 字段 | 说明 | 示例 |
|------|------|------|
| date | 交易日期 | 2024-01-15 |
| code | 股票代码 | sh.600000 |
| open | 开盘价（后复权） | 10.50 |
| high | 最高价（后复权） | 10.80 |
| low | 最低价（后复权） | 10.40 |
| close | 收盘价（后复权） | 10.75 |
| volume | 成交量 | 1000000 |
| amount | 成交额 | 10750000.0 |
| pctChg | 涨跌幅(%) | 2.87 |

## ⚠️ 注意事项

1. **首次使用**：建议先运行 `test` 模式
2. **网络要求**：需要稳定的网络连接
3. **存储空间**：全量下载需要数GB空间
4. **下载时间**：完整下载可能需要数小时
5. **数据延迟**：baostock数据可能有1-2天延迟

## 🆘 常见问题

**Q: 登录失败怎么办？**
A: 运行 `python -m pip install baostock --upgrade` 升级到最新版本

**Q: 下载的数据在哪里？**
A: 在 `../../0-数据汇总/1-原始股票数据/` 目录下

**Q: 如何下载指定股票？**
A: 参考 `example_usage.py` 中的示例代码

**Q: 数据格式是什么？**
A: 制表符分隔的文本文件（TSV），UTF-8编码

## 📞 获取帮助

- 查看详细文档：`README.md`
- 运行示例：`python example_usage.py`
- 测试接口：`python test_baostock.py`