#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据下载脚本 - 供GUI调用

功能：
1. 下载指定股票池的股票列表
2. 下载个股历史数据
3. 支持增量更新和全量下载
"""

import os
import sys
import pandas as pd
import akshare as ak
from datetime import datetime, timedelta
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

class StockDataDownloader:
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent.parent
        self.data_dir = self.base_dir / "0-数据汇总" / "1-原始股票数据"
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
    def download_stock_list(self, pool_name):
        """
        下载股票池列表
        
        Args:
            pool_name (str): 股票池名称 ('sz50', 'hs300', 'zz500')
        """
        try:
            print(f"正在下载 {pool_name} 股票列表...")
            
            if pool_name == 'sz50':
                df = ak.index_stock_cons(symbol="000016")
                filename = "sz50_stocks.csv"
                codes_filename = "sz50_codes.txt"
            elif pool_name == 'hs300':
                df = ak.index_stock_cons(symbol="000300")
                filename = "hs300_stocks.csv"
                codes_filename = "hs300_codes.txt"
            elif pool_name == 'zz500':
                df = ak.index_stock_cons(symbol="000905")
                filename = "zz500_stocks.csv"
                codes_filename = "zz500_codes.txt"
            else:
                raise ValueError(f"不支持的股票池: {pool_name}")
                
            # 保存股票列表
            df.to_csv(self.data_dir / filename, index=False, encoding='utf-8-sig')
            
            # 保存股票代码列表
            if '品种代码' in df.columns:
                codes = df['品种代码'].tolist()
            elif 'code' in df.columns:
                codes = df['code'].tolist()
            else:
                codes = df.iloc[:, 0].tolist()  # 使用第一列作为代码
                
            with open(self.data_dir / codes_filename, 'w', encoding='utf-8') as f:
                for code in codes:
                    f.write(f"{code}\n")
                    
            print(f"{pool_name} 股票列表下载完成，共 {len(codes)} 只股票")
            return codes
            
        except Exception as e:
            print(f"下载 {pool_name} 股票列表失败: {str(e)}")
            return []
            
    def download_stock_data(self, stock_code, start_date=None, end_date=None, force_update=False):
        """
        下载单只股票的历史数据
        
        Args:
            stock_code (str): 股票代码
            start_date (str): 开始日期
            end_date (str): 结束日期
            force_update (bool): 是否强制更新
        """
        try:
            # 设置默认日期
            if end_date is None:
                end_date = datetime.now().strftime('%Y%m%d')
            if start_date is None:
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
                
            # 转换日期格式
            if '-' in start_date:
                start_date = start_date.replace('-', '')
            if '-' in end_date:
                end_date = end_date.replace('-', '')
                
            filename = f"{stock_code}_hfq.csv"
            filepath = self.data_dir / filename
            
            # 检查是否需要更新
            if not force_update and filepath.exists():
                existing_df = pd.read_csv(filepath)
                if not existing_df.empty:
                    last_date = existing_df['日期'].iloc[-1]
                    if last_date >= end_date.replace('-', ''):
                        print(f"{stock_code} 数据已是最新，跳过下载")
                        return True
                        
            print(f"正在下载 {stock_code} 数据...")
            
            # 下载股票数据
            df = ak.stock_zh_a_hist(symbol=stock_code, period="daily", 
                                   start_date=start_date, end_date=end_date, adjust="hfq")
            
            if df.empty:
                print(f"{stock_code} 无数据")
                return False
                
            # 保存数据
            df.to_csv(filepath, index=False, encoding='utf-8-sig')
            print(f"{stock_code} 数据下载完成，共 {len(df)} 条记录")
            
            # 添加延时避免请求过快
            time.sleep(0.1)
            
            return True
            
        except Exception as e:
            print(f"下载 {stock_code} 数据失败: {str(e)}")
            return False
            
    def download_pool_data(self, pool_name, start_date=None, end_date=None, force_update=False, max_stocks=None):
        """
        下载整个股票池的数据
        
        Args:
            pool_name (str): 股票池名称
            start_date (str): 开始日期
            end_date (str): 结束日期
            force_update (bool): 是否强制更新
            max_stocks (int): 最大下载股票数量（用于测试）
        """
        try:
            # 下载股票列表
            stock_codes = self.download_stock_list(pool_name)
            
            if not stock_codes:
                print(f"{pool_name} 股票列表为空")
                return
                
            # 限制下载数量（用于测试）
            if max_stocks:
                stock_codes = stock_codes[:max_stocks]
                print(f"测试模式：只下载前 {max_stocks} 只股票")
                
            success_count = 0
            total_count = len(stock_codes)
            
            print(f"开始下载 {pool_name} 股票数据，共 {total_count} 只股票")
            
            for i, stock_code in enumerate(stock_codes, 1):
                print(f"进度: {i}/{total_count} - {stock_code}")
                
                if self.download_stock_data(stock_code, start_date, end_date, force_update):
                    success_count += 1
                    
            print(f"{pool_name} 数据下载完成: {success_count}/{total_count} 成功")
            
        except Exception as e:
            print(f"下载 {pool_name} 数据过程发生错误: {str(e)}")

def main():
    """
    主函数 - 支持命令行参数
    """
    import argparse
    
    parser = argparse.ArgumentParser(description='股票数据下载工具')
    parser.add_argument('--pool', choices=['sz50', 'hs300', 'zz500'], 
                       help='股票池名称')
    parser.add_argument('--start', help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end', help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--force', action='store_true', help='强制更新')
    parser.add_argument('--test', type=int, help='测试模式，指定下载股票数量')
    
    args = parser.parse_args()
    
    downloader = StockDataDownloader()
    
    if args.pool:
        # 下载指定股票池
        downloader.download_pool_data(
            pool_name=args.pool,
            start_date=args.start,
            end_date=args.end,
            force_update=args.force,
            max_stocks=args.test
        )
    else:
        # 默认下载所有股票池（测试模式）
        pools = ['sz50', 'hs300', 'zz500']
        for pool in pools:
            print(f"\n{'='*50}")
            print(f"开始下载 {pool} 数据")
            print(f"{'='*50}")
            
            downloader.download_pool_data(
                pool_name=pool,
                start_date=args.start,
                end_date=args.end,
                force_update=args.force,
                max_stocks=args.test or 5  # 默认测试模式每个池下载5只股票
            )
            
if __name__ == "__main__":
    main()