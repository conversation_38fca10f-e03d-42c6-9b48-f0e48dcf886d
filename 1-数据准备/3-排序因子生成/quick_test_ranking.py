#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试排序因子生成的脚本
只处理少量数据来验证修复效果
"""

import os
import sys
import pickle
import pandas as pd
import numpy as np
from datetime import datetime
from tqdm import tqdm

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import DATA_CONFIG

def quick_test_ranking_factors():
    """
    快速测试排序因子生成
    """
    print("=" * 60)
    print("快速测试排序因子生成")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 获取目录路径
    stock_data_dir = DATA_CONFIG['stock_data_dir']
    factor_cache_dir = DATA_CONFIG['factor_cache_dir']
    ranking_factor_dir = DATA_CONFIG['ranking_factor_dir']
    
    # 确保排序因子目录存在
    os.makedirs(ranking_factor_dir, exist_ok=True)
    
    # 获取前5个股票文件进行测试
    factor_files = [f for f in os.listdir(factor_cache_dir) if f.endswith('_factors.pkl')][:5]
    
    if not factor_files:
        print("❌ 没有找到因子文件")
        return False
    
    print(f"测试股票数量: {len(factor_files)}")
    
    # 分析可用因子
    print("\n分析可用因子...")
    
    # 从第一个文件获取因子列表
    sample_file = os.path.join(factor_cache_dir, factor_files[0])
    with open(sample_file, 'rb') as f:
        sample_df = pickle.load(f)
    
    # 获取原始数据文件的列名
    stock_files = [f for f in os.listdir(stock_data_dir) if f.endswith('_hfq.csv')]
    if stock_files:
        sample_stock_file = os.path.join(stock_data_dir, stock_files[0])
        stock_df = pd.read_csv(sample_stock_file)
        original_columns = [col for col in stock_df.columns if col not in ['date', 'code', 'adjustflag', 'tradestatus', 'isST']]
    else:
        original_columns = ['open', 'high', 'low', 'close', 'volume']
    
    # 计算因子列表
    factor_columns = [col for col in sample_df.columns 
                     if not col.endswith('_percentile') and not col.endswith('_rank')]
    
    original_factors = [col for col in original_columns if col in factor_columns]
    computed_factors = [col for col in factor_columns if col not in original_factors]
    
    print(f"原始数据列: {original_columns}")
    print(f"因子数据列: {factor_columns}")
    print(f"原始数据因子 ({len(original_factors)} 个): {original_factors}")
    print(f"计算因子 ({len(computed_factors)} 个): {computed_factors}")
    
    # 如果没有找到原始数据因子，直接使用原始数据列中的基本因子
    if not original_factors:
        basic_factors = ['open', 'high', 'low', 'close', 'volume', 'amount', 'turn', 'pctChg']
        original_factors = [col for col in basic_factors if col in original_columns]
        print(f"使用基本原始数据因子: {original_factors}")
    
    all_factors = original_factors + computed_factors
    print(f"总共将处理 {len(all_factors)} 个因子")
    
    # 收集少量交易日期（只取最近100个交易日）
    print("\n收集交易日期...")
    all_dates = set()
    
    for filename in tqdm(factor_files, desc="收集日期"):
        file_path = os.path.join(factor_cache_dir, filename)
        try:
            with open(file_path, 'rb') as f:
                df = pickle.load(f)
            # 个股因子文件使用日期作为索引
            if pd.api.types.is_datetime64_any_dtype(df.index):
                dates = df.index.strftime('%Y-%m-%d').tolist()
                all_dates.update(dates)
            elif 'date' in df.columns:
                dates = pd.to_datetime(df['date']).dt.strftime('%Y-%m-%d').tolist()
                all_dates.update(dates)
        except Exception as e:
            print(f"警告: 读取文件 {filename} 失败: {e}")
    
    trading_dates = sorted(list(all_dates))[-100:] if all_dates else []  # 只取最近100个交易日
    
    if not trading_dates:
        print("❌ 没有找到交易日期")
        return False
        
    print(f"选择 {len(trading_dates)} 个交易日进行测试")
    print(f"日期范围: {trading_dates[0]} 到 {trading_dates[-1]}")
    
    # 处理每个交易日
    print("\n开始处理交易日...")
    
    for i, date in enumerate(tqdm(trading_dates, desc="处理交易日")):
        try:
            # 收集当日所有股票的因子数据
            daily_data = {}
            
            for filename in factor_files:
                stock_code = filename.replace('_factors.pkl', '')
                file_path = os.path.join(factor_cache_dir, filename)
                
                try:
                    with open(file_path, 'rb') as f:
                        df = pickle.load(f)
                    
                    # 筛选当日数据 - 个股因子文件使用日期作为索引
                    if pd.api.types.is_datetime64_any_dtype(df.index):
                        daily_df = df[df.index.strftime('%Y-%m-%d') == date]
                    elif 'date' in df.columns:
                        daily_df = df[pd.to_datetime(df['date']).dt.strftime('%Y-%m-%d') == date]
                    else:
                        continue
                    
                    if len(daily_df) > 0:
                        # 获取因子值
                        factor_values = {}
                        for factor in all_factors:
                            if factor in daily_df.columns:
                                value = daily_df[factor].iloc[0]
                                if pd.notna(value):
                                    factor_values[factor] = float(value)
                        
                        # 添加原始数据因子
                        if original_factors:
                            # 读取原始股票数据
                            stock_file = os.path.join(stock_data_dir, f"{stock_code}_hfq.csv")
                            if os.path.exists(stock_file):
                                stock_df = pd.read_csv(stock_file)
                                stock_df['date'] = pd.to_datetime(stock_df['date']).dt.strftime('%Y-%m-%d')
                                daily_stock = stock_df[stock_df['date'] == date]
                                
                                if len(daily_stock) > 0:
                                    for factor in original_factors:
                                        if factor in daily_stock.columns:
                                            value = daily_stock[factor].iloc[0]
                                            if pd.notna(value):
                                                factor_values[factor] = float(value)
                        
                        if factor_values:
                            daily_data[stock_code] = factor_values
                
                except Exception as e:
                    continue
            
            if not daily_data:
                continue
            
            # 计算排序百分比
            ranking_data = {}
            
            for factor in all_factors:
                # 收集所有股票在该因子上的值
                values = []
                stocks = []
                
                for stock_code, factors in daily_data.items():
                    if factor in factors:
                        values.append(factors[factor])
                        stocks.append(stock_code)
                
                if len(values) > 1:
                    # 计算排序百分比
                    ranks = pd.Series(values).rank(method='min', pct=True)
                    
                    for stock_code, rank_value in zip(stocks, ranks):
                        if stock_code not in ranking_data:
                            ranking_data[stock_code] = {'date': date}
                        ranking_data[stock_code][f"{factor}_rank"] = rank_value
            
            # 保存每个股票的排序数据
            for stock_code, data in ranking_data.items():
                ranking_file = os.path.join(ranking_factor_dir, f"{stock_code}_ranking_factors.pkl")
                
                # 读取现有数据或创建新数据
                if os.path.exists(ranking_file):
                    try:
                        with open(ranking_file, 'rb') as f:
                            existing_df = pickle.load(f)
                    except:
                        existing_df = pd.DataFrame()
                else:
                    existing_df = pd.DataFrame()
                
                # 添加新数据
                new_row = pd.DataFrame([data])
                if len(existing_df) > 0:
                    # 检查是否已存在该日期的数据
                    if 'date' in existing_df.columns:
                        existing_df = existing_df[existing_df['date'] != date]
                    updated_df = pd.concat([existing_df, new_row], ignore_index=True)
                else:
                    updated_df = new_row
                
                # 保存数据
                with open(ranking_file, 'wb') as f:
                    pickle.dump(updated_df, f)
        
        except Exception as e:
            print(f"处理日期 {date} 时出错: {e}")
            continue
    
    print("\n✅ 快速测试完成")
    
    # 验证结果
    verify_quick_test_results()
    
    return True

def verify_quick_test_results():
    """
    验证快速测试结果
    """
    print("\n验证测试结果...")
    
    ranking_factor_dir = DATA_CONFIG['ranking_factor_dir']
    ranking_files = [f for f in os.listdir(ranking_factor_dir) if f.endswith('_ranking_factors.pkl')]
    
    if not ranking_files:
        print("❌ 没有生成排序因子文件")
        return False
    
    print(f"✅ 生成了 {len(ranking_files)} 个排序因子文件")
    
    # 检查第一个文件
    sample_file = os.path.join(ranking_factor_dir, ranking_files[0])
    
    try:
        with open(sample_file, 'rb') as f:
            df = pickle.load(f)
        
        print(f"\n样本文件: {ranking_files[0]}")
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        # 检查列名
        rank_columns = [col for col in df.columns if col.endswith('_rank')]
        other_columns = [col for col in df.columns if not col.endswith('_rank')]
        
        print(f"_rank列数量: {len(rank_columns)}")
        print(f"_rank列: {rank_columns}")
        print(f"其他列: {other_columns}")
        
        # 检查原始数据因子
        original_rank_columns = [col for col in rank_columns if any(col.startswith(f"{factor}_") for factor in ['open', 'close', 'volume'])]
        print(f"原始数据排序因子: {original_rank_columns}")
        
        if original_rank_columns:
            print("✅ 包含原始数据排序因子")
        else:
            print("⚠️  缺少原始数据排序因子")
        
        # 显示数据预览
        print(f"\n数据预览:")
        print(df.head())
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    success = quick_test_ranking_factors()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 快速测试成功完成")
    else:
        print("❌ 快速测试失败")
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)