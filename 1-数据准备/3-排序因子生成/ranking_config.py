#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排序因子配置文件

定义需要进行百分位排序的因子列表
"""

# 需要进行百分位排序的因子配置
RANKING_FACTORS_CONFIG = {
    # 技术指标 - 选择性排序
    'technical_indicators': {
        'RSI': True,           # 相对强弱指标
        'MACD': False,         # MACD指标（通常不需要排序）
        'KDJ': True,           # 随机指标
        'BOLL': False,         # 布林带（通常不需要排序）
        'CCI': True,           # 商品通道指标
        'WR': True,            # 威廉指标
        'ATR': True,           # 平均真实波动范围
        'OBV': True,           # 能量潮指标
        'VWAP': False,         # 成交量加权平均价格（通常不需要排序）
        'DMI': True,           # 动向指标
    },
    
    # 统计指标 - 全部排序
    'statistical_indicators': {
        'turnover_std20': True,    # 20日换手率标准差
        'ReturnStd120': True,      # 120日收益率标准差
    },
    
    # 基本面指标 - 从原始数据提取，不在因子计算中
    # 注意：这些指标已从因子计算模块中移除，直接从原始数据获取
    'fundamental_indicators': {
        'turnover': True,     # 换手率（从原始数据提取）
        'peTTM': False,        # 市盈率TTM（从原始数据提取）
        'psTTM': False,        # 市销率TTM（从原始数据提取）
        'pcfNcfTTM': False,    # 市现率TTM（从原始数据提取）
        'pbMRQ': False,        # 市净率MRQ（从原始数据提取）
    }
}

def get_target_factors():
    """
    获取需要进行排序的因子列表
    
    Returns:
        list: 需要排序的因子名称列表
    """
    target_factors = []
    
    # 收集所有需要排序的因子
    for category, factors in RANKING_FACTORS_CONFIG.items():
        for factor_name, should_rank in factors.items():
            if should_rank:
                target_factors.append(factor_name)
    
    return target_factors

def get_factors_by_category(category):
    """
    获取指定类别中需要排序的因子
    
    Args:
        category (str): 因子类别 ('technical_indicators', 'statistical_indicators', 'fundamental_indicators')
    
    Returns:
        list: 该类别中需要排序的因子列表
    """
    if category not in RANKING_FACTORS_CONFIG:
        return []
    
    factors = []
    for factor_name, should_rank in RANKING_FACTORS_CONFIG[category].items():
        if should_rank:
            factors.append(factor_name)
    
    return factors

def print_ranking_config():
    """
    打印排序配置信息
    """
    print("排序因子配置:")
    print("=" * 50)
    
    total_factors = 0
    ranking_factors = 0
    
    for category, factors in RANKING_FACTORS_CONFIG.items():
        print(f"\n{category.replace('_', ' ').title()}:")
        category_ranking = 0
        
        for factor_name, should_rank in factors.items():
            status = "✓ 排序" if should_rank else "✗ 跳过"
            print(f"  {factor_name:<15} - {status}")
            total_factors += 1
            if should_rank:
                ranking_factors += 1
                category_ranking += 1
        
        print(f"  小计: {category_ranking}/{len(factors)} 个因子需要排序")
    
    print(f"\n总计: {ranking_factors}/{total_factors} 个因子需要排序")
    print("=" * 50)

if __name__ == "__main__":
    # 显示配置信息
    print_ranking_config()
    
    # 显示目标因子列表
    target_factors = get_target_factors()
    print(f"\n需要排序的因子: {target_factors}")
    print(f"总数: {len(target_factors)} 个")