# 排序因子生成模块

## 模块说明

本模块负责计算股票因子的百分位排序，为机器学习模型提供标准化的排序因子数据。

## 核心文件

### 必要文件

1. **`run_ranking_factors.py`** - 主程序
   - 排序因子生成的入口程序
   - 包含数据检查、计算执行、结果验证等完整流程
   - 使用方法：`python run_ranking_factors.py`

2. **`calculate_factor_percentiles_optimized.py`** - 核心计算模块
   - 优化版百分位排序计算算法
   - 采用分批处理，内存友好
   - 支持大规模数据集处理

3. **`ranking_config.py`** - 排序配置
   - 定义需要进行百分位排序的因子列表
   - 支持按类别（技术指标、统计指标、基本面指标）配置
   - 可灵活控制每个因子是否参与排序

4. **`config.py`** - 数据路径配置
   - 定义输入和输出数据目录
   - 统一管理数据路径配置

### 辅助文件

5. **`verify_percentiles.py`** - 结果验证
   - 验证百分位排序计算的正确性
   - 提供统计分析和质量检查

## 已删除的冗余文件

以下文件已被删除以精简模块：

- `calculate_factor_percentiles.py` - 标准版计算（内存占用高）
- `calculate_factor_percentiles_fast.py` - 快速版计算（功能重复）
- `check_factor_file.py` - 单文件检查工具（功能重复）
- `check_percentile_progress.py` - 进度检查工具（功能重复）
- `check_progress.py` - 通用进度检查（功能重复）

## 使用流程

1. **配置排序因子**
   ```python
   # 编辑 ranking_config.py
   # 设置需要排序的因子
   ```

2. **运行排序计算**
   ```bash
   python run_ranking_factors.py
   ```

3. **验证结果**（可选）
   ```bash
   python verify_percentiles.py
   ```

## 输出结果

- **完整因子数据**：保存到原始因子文件中（包含排序因子）
- **排序因子数据**：单独保存到 `ranking_factors` 目录
- **文件格式**：pickle格式（.pkl）

## 性能特点

- **内存优化**：采用分批处理，避免内存溢出
- **计算效率**：优化的算法实现，支持大规模数据
- **结果可靠**：包含完整的验证机制

## 注意事项

1. 确保输入的因子数据已经生成完成
2. 排序因子配置需要与实际因子数据匹配
3. 建议定期清理 `__pycache__` 目录