#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版因子百分比排序计算脚本
计算指定因子在每个交易日中所有股票的百分比排序值
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime
import pickle
from tqdm import tqdm
import gc
from config import DATA_CONFIG
from ranking_config import get_target_factors, print_ranking_config

def get_all_available_factors(factor_files, factors_dir):
    """
    获取所有可用的因子列（包括原始数据列和计算因子列）
    
    Args:
        factor_files (list): 因子文件列表
        factors_dir (str): 因子数据目录
    
    Returns:
        tuple: (原始数据列列表, 因子数据列列表)
    """
    # 原始数据中的基本列
    original_data_columns = [
        'open', 'high', 'low', 'close', 'preclose', 
        'volume', 'amount', 'turn', 'pctChg', 
        'peTTM', 'psTTM', 'pcfNcfTTM', 'pbMRQ'
    ]
    
    # 从因子文件获取计算因子列
    factor_data_columns = []
    if factor_files:
        sample_file = os.path.join(factors_dir, factor_files[0])
        with open(sample_file, 'rb') as f:
            sample_df = pickle.load(f)
        # 过滤掉已经存在的排序因子列（_percentile, _rank后缀）
        all_columns = list(sample_df.columns)
        factor_data_columns = [col for col in all_columns 
                              if not (col.endswith('_percentile') or col.endswith('_rank'))]
    
    return original_data_columns, factor_data_columns



def calculate_factor_percentiles_optimized(custom_factors=None, include_original=True, include_computed=True):
    """
    优化版本：为所有可用因子生成排序数据
    
    Args:
        custom_factors (list, optional): 自定义因子列表，如果提供则只处理这些因子
        include_original (bool): 是否包含原始数据列（如open, high等）
        include_computed (bool): 是否包含计算因子列
    """
    print("=" * 60)
    print("优化版因子百分比排序计算脚本")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 配置参数
    factor_cache_dir = DATA_CONFIG['factor_cache_dir']
    factors_dir = factor_cache_dir
    ranking_factor_dir = DATA_CONFIG['ranking_factor_dir']
    original_data_dir = DATA_CONFIG['stock_data_dir']

    batch_size = 100  # 每批处理的交易日数量
    
    # 确保排序因子目录存在
    os.makedirs(ranking_factor_dir, exist_ok=True)
    
    # 获取所有因子文件
    factor_files = [f for f in os.listdir(factors_dir) if f.endswith('_factors.pkl')]
    print(f"发现 {len(factor_files)} 个因子文件")
    
    if not factor_files:
        print("未找到因子文件！")
        return
    
    # 获取所有可用的因子列
    print("\n分析可用因子列...")
    original_data_columns, factor_data_columns = get_all_available_factors(factor_files, factors_dir)
    
    # 确定要处理的因子列表
    all_factors = []
    original_factors = []
    computed_factors = []
    
    if custom_factors is not None:
        # 使用自定义因子列表
        all_factors = custom_factors
        print(f"使用自定义因子列表: {custom_factors}")
        # 分类自定义因子
        for factor in custom_factors:
            if factor in original_data_columns:
                original_factors.append(factor)
            elif factor in factor_data_columns:
                computed_factors.append(factor)
    else:
        # 使用所有可用因子
        if include_original:
            original_factors = original_data_columns
            all_factors.extend(original_data_columns)
        if include_computed:
            computed_factors = factor_data_columns
            all_factors.extend(factor_data_columns)
    
    if not all_factors:
        print("错误：没有找到可处理的因子列")
        return
    
    print(f"\n原始数据因子 ({len(original_factors)} 个): {original_factors}")
    print(f"计算因子 ({len(computed_factors)} 个): {computed_factors}")
    print(f"总共将处理 {len(all_factors)} 个因子，生成对应的排序因子")
    
    # 首先收集所有交易日期
    print("\n收集交易日期...")
    all_dates = set()
    sample_files = factor_files[:50]  # 只从前50个文件中收集日期
    
    for file in tqdm(sample_files, desc="收集日期"):
        try:
            file_path = os.path.join(factors_dir, file)
            with open(file_path, 'rb') as f:
                df = pickle.load(f)
            all_dates.update(df.index)
        except Exception as e:
            continue
    
    # 排序日期
    sorted_dates = sorted(list(all_dates))
    print(f"发现 {len(sorted_dates)} 个交易日")
    
    if len(sorted_dates) == 0:
        print("未找到交易日期！")
        return
    
    # 分批处理交易日
    total_batches = (len(sorted_dates) + batch_size - 1) // batch_size
    print(f"将分 {total_batches} 批处理，每批 {batch_size} 个交易日")
    
    # 为每个股票初始化百分比排序数据
    percentile_data = {}
    for file in factor_files:
        stock_code = file.replace('_factors.pkl', '')
        percentile_data[stock_code] = {f"{factor}_rank": {} for factor in all_factors}
    
    # 分批处理
    for batch_idx in range(total_batches):
        start_idx = batch_idx * batch_size
        end_idx = min((batch_idx + 1) * batch_size, len(sorted_dates))
        batch_dates = sorted_dates[start_idx:end_idx]
        
        print(f"\n处理批次 {batch_idx + 1}/{total_batches}: {len(batch_dates)} 个交易日")
        print(f"日期范围: {batch_dates[0].strftime('%Y-%m-%d')} 到 {batch_dates[-1].strftime('%Y-%m-%d')}")
        
        # 为当前批次的每个交易日计算百分比排序
        for date in tqdm(batch_dates, desc=f"批次 {batch_idx + 1}"):
            # 收集该日期所有股票的因子值
            daily_factor_data = {factor: {} for factor in all_factors}
            
            for file in factor_files:
                try:
                    stock_code = file.replace('_factors.pkl', '')
                    
                    # 处理计算因子（从因子数据文件获取）
                    if computed_factors:
                        file_path = os.path.join(factors_dir, file)
                        with open(file_path, 'rb') as f:
                            factor_df = pickle.load(f)
                        
                        if date in factor_df.index:
                            for factor in computed_factors:
                                if factor in factor_df.columns:
                                    value = factor_df.loc[date, factor]
                                    if pd.notna(value):
                                        daily_factor_data[factor][stock_code] = value
                    
                    # 处理原始数据因子（从原始数据文件获取）
                    if original_factors:
                        original_file = os.path.join(original_data_dir, f"{stock_code}_hfq.csv")
                        if os.path.exists(original_file):
                            original_df = pd.read_csv(original_file, index_col=0, parse_dates=True)
                            
                            if date in original_df.index:
                                for factor in original_factors:
                                    if factor in original_df.columns:
                                        value = original_df.loc[date, factor]
                                        if pd.notna(value):
                                            daily_factor_data[factor][stock_code] = value
                                
                except Exception as e:
                    continue
            
            # 计算每个因子的百分比排序
            for factor in all_factors:
                if len(daily_factor_data[factor]) > 1:
                    # 转换为Series并计算百分比排序
                    factor_series = pd.Series(daily_factor_data[factor])
                    percentiles = factor_series.rank(pct=True, method='min')
                    
                    # 保存百分比排序结果（使用_rank后缀）
                    for stock_code, percentile in percentiles.items():
                        percentile_data[stock_code][f"{factor}_rank"][date] = percentile
        
        # 强制垃圾回收
        gc.collect()
        print(f"批次 {batch_idx + 1} 完成")
    
    # 保存结果到各个股票文件
    print("\n保存百分比排序结果...")
    success_count = 0
    
    for file in tqdm(factor_files, desc="保存结果"):
        try:
            stock_code = file.replace('_factors.pkl', '')
            file_path = os.path.join(factors_dir, file)
            
            # 加载原始数据
            with open(file_path, 'rb') as f:
                df = pickle.load(f)
            
            # 添加百分比排序数据
            ranking_columns = []
            for factor in all_factors:
                rank_col = f"{factor}_rank"
                rank_dict = percentile_data[stock_code][rank_col]
                
                if rank_dict:
                    # 创建百分比排序Series
                    rank_series = pd.Series(rank_dict, name=rank_col)
                    rank_series.index.name = 'Date'
                    
                    # 合并到原DataFrame
                    if rank_col in df.columns:
                        df[rank_col] = df[rank_col].combine_first(rank_series)
                    else:
                        df = df.join(rank_series, how='left')
                    
                    ranking_columns.append(rank_col)
            
            # 1. 保存完整的因子数据到原始目录（包含排序因子）
            with open(file_path, 'wb') as f:
                pickle.dump(df, f)
            
            # 2. 提取并保存排序因子到专门目录
            if ranking_columns:
                ranking_data = df[ranking_columns].copy()
                ranking_path = os.path.join(ranking_factor_dir, f"{stock_code}_ranking_factors.pkl")
                
                with open(ranking_path, 'wb') as f:
                    pickle.dump(ranking_data, f)
            
            success_count += 1
            
        except Exception as e:
            print(f"保存股票 {stock_code} 时出错: {e}")
            continue
    
    print(f"\n成功更新 {success_count}/{len(factor_files)} 个股票文件")
    print("=" * 60)
    print(f"计算完成！结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

if __name__ == "__main__":
    calculate_factor_percentiles_optimized()