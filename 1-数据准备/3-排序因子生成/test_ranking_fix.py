#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试排序因子修复的脚本
只处理少量数据来验证修复是否有效
"""

import os
import sys
import pickle
import pandas as pd
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import DATA_CONFIG
from calculate_factor_percentiles_optimized import calculate_factor_percentiles_optimized

def test_small_sample():
    """
    测试小样本数据的排序因子生成
    """
    print("=" * 60)
    print("测试排序因子修复")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 只选择几个基本因子进行测试
    test_factors = ['open', 'close', 'volume', 'RSI', 'CCI']
    
    print(f"测试因子: {test_factors}")
    
    try:
        # 运行排序因子计算
        calculate_factor_percentiles_optimized(
            custom_factors=test_factors,
            include_original=True,
            include_computed=True
        )
        
        print("\n✅ 排序因子计算完成")
        
        # 验证结果
        verify_test_results(test_factors)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(traceback.format_exc())

def verify_test_results(test_factors):
    """
    验证测试结果
    """
    print("\n验证测试结果...")
    
    ranking_factor_dir = DATA_CONFIG['ranking_factor_dir']
    
    if not os.path.exists(ranking_factor_dir):
        print("❌ 排序因子目录不存在")
        return False
    
    ranking_files = [f for f in os.listdir(ranking_factor_dir) if f.endswith('_ranking_factors.pkl')]
    
    if not ranking_files:
        print("❌ 没有找到排序因子文件")
        return False
    
    print(f"✅ 找到 {len(ranking_files)} 个排序因子文件")
    
    # 检查第一个文件的内容
    sample_file = os.path.join(ranking_factor_dir, ranking_files[0])
    
    try:
        with open(sample_file, 'rb') as f:
            df = pickle.load(f)
        
        print(f"\n样本文件: {ranking_files[0]}")
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        # 检查列名是否都使用_rank后缀
        rank_columns = [col for col in df.columns if col.endswith('_rank')]
        non_rank_columns = [col for col in df.columns if not col.endswith('_rank')]
        
        print(f"\n_rank列数量: {len(rank_columns)}")
        print(f"_rank列: {rank_columns}")
        print(f"非_rank列: {non_rank_columns}")
        
        if non_rank_columns:
            print("⚠️  发现非_rank列，可能存在问题")
            return False
        else:
            print("✅ 所有列都使用_rank后缀")
        
        # 检查是否包含预期的因子
        expected_rank_columns = [f"{factor}_rank" for factor in test_factors]
        missing_columns = [col for col in expected_rank_columns if col not in rank_columns]
        
        if missing_columns:
            print(f"⚠️  缺少预期的排序因子: {missing_columns}")
        else:
            print("✅ 包含所有预期的排序因子")
        
        # 检查数据内容
        print(f"\n数据预览:")
        print(df.head())
        
        # 检查数据范围（排序值应该在0-1之间）
        print(f"\n数据范围检查:")
        for col in rank_columns:
            valid_data = df[col].dropna()
            if len(valid_data) > 0:
                min_val = valid_data.min()
                max_val = valid_data.max()
                print(f"{col}: [{min_val:.4f}, {max_val:.4f}]")
                
                if min_val < 0 or max_val > 1:
                    print(f"⚠️  {col} 的值超出了[0,1]范围")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def check_original_data_factors():
    """
    检查原始数据因子是否正确生成
    """
    print("\n检查原始数据因子...")
    
    ranking_factor_dir = DATA_CONFIG['ranking_factor_dir']
    sample_file = os.path.join(ranking_factor_dir, 'sh.600000_ranking_factors.pkl')
    
    if not os.path.exists(sample_file):
        print("❌ 样本文件不存在")
        return
    
    try:
        with open(sample_file, 'rb') as f:
            df = pickle.load(f)
        
        # 检查原始数据因子的排序列
        original_factors = ['open', 'close', 'volume']
        original_rank_columns = [f"{factor}_rank" for factor in original_factors]
        
        found_original = [col for col in original_rank_columns if col in df.columns]
        missing_original = [col for col in original_rank_columns if col not in df.columns]
        
        print(f"找到的原始数据排序因子: {found_original}")
        print(f"缺少的原始数据排序因子: {missing_original}")
        
        if found_original:
            print("✅ 原始数据因子排序正常生成")
            
            # 检查数据是否有值
            for col in found_original:
                valid_count = df[col].notna().sum()
                total_count = len(df)
                print(f"{col}: {valid_count}/{total_count} 个有效值")
        else:
            print("❌ 原始数据因子排序未生成")
            
    except Exception as e:
        print(f"❌ 检查原始数据因子失败: {e}")

if __name__ == "__main__":
    test_small_sample()
    check_original_data_factors()
    
    print("\n=" * 60)
    print(f"测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)