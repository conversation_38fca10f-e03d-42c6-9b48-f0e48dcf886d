#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排序因子生成使用示例
"""

from calculate_factor_percentiles_optimized import calculate_factor_percentiles_optimized

def example_usage():
    """
    排序因子生成使用示例
    """
    print("排序因子生成使用示例")
    print("=" * 50)
    
    # 示例1: 生成所有可用因子的排序数据
    print("\n示例1: 生成所有可用因子的排序数据")
    print("这将为所有原始数据列和计算因子列生成对应的_rank排序因子")
    # calculate_factor_percentiles_optimized()
    
    # 示例2: 只生成原始数据因子的排序数据
    print("\n示例2: 只生成原始数据因子的排序数据")
    print("只处理 open, high, low, close 等原始数据列")
    # calculate_factor_percentiles_optimized(include_original=True, include_computed=False)
    
    # 示例3: 只生成计算因子的排序数据
    print("\n示例3: 只生成计算因子的排序数据")
    print("只处理 RSI, MACD, KDJ 等计算因子列")
    # calculate_factor_percentiles_optimized(include_original=False, include_computed=True)
    
    # 示例4: 生成指定因子的排序数据
    print("\n示例4: 生成指定因子的排序数据")
    print("只处理指定的因子列表")
    custom_factors = ['open', 'high', 'low', 'close', 'RSI', 'MACD_MACD', 'KDJ_K']
    # calculate_factor_percentiles_optimized(custom_factors=custom_factors)
    
    print("\n注意事项:")
    print("1. 原始数据因子来源: 0-数据汇总/1-原始股票数据/*.csv")
    print("2. 计算因子来源: 0-数据汇总/2-个股因子数据/*_factors.pkl")
    print("3. 排序因子输出: 0-数据汇总/3-排序因子数据/*_ranking_factors.pkl")
    print("4. 排序因子命名: 原因子名 + '_rank' 后缀")
    print("5. 排序值范围: 0-1 (百分位排序)")
    
    print("\n可用的原始数据因子:")
    original_factors = [
        'open', 'high', 'low', 'close', 'preclose', 
        'volume', 'amount', 'turn', 'pctChg', 
        'peTTM', 'psTTM', 'pcfNcfTTM', 'pbMRQ'
    ]
    for i, factor in enumerate(original_factors, 1):
        print(f"  {i:2d}. {factor} -> {factor}_rank")
    
    print("\n计算因子示例 (实际数量取决于因子计算结果):")
    computed_factors_examples = [
        'RSI', 'MACD_MACD', 'MACD_Signal', 'KDJ_K', 'KDJ_D', 'KDJ_J',
        'BOLL_Upper', 'BOLL_Middle', 'BOLL_Lower', 'CCI', 'WR', 'ATR', 'OBV'
    ]
    for i, factor in enumerate(computed_factors_examples, 1):
        print(f"  {i:2d}. {factor} -> {factor}_rank")

if __name__ == "__main__":
    example_usage()