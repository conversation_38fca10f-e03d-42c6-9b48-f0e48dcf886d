#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证百分比排序因子的脚本

功能：
1. 检查百分比排序因子是否正确计算
2. 验证百分比排序的合理性
3. 显示统计信息
"""

import os
import pickle
import pandas as pd
import numpy as np
from datetime import datetime
from config import DATA_CONFIG
import random

def load_sample_factor_data(sample_size=10):
    """
    加载样本股票的因子数据
    
    Args:
        sample_size (int): 样本大小
    
    Returns:
        dict: {stock_code: factor_dataframe}
    """
    factor_cache_dir = DATA_CONFIG['factor_cache_dir']
factors_dir = factor_cache_dir
    ranking_factor_dir = DATA_CONFIG['ranking_factor_dir']
    
    print(f"从因子目录加载数据: {factors_dir}")
    print(f"排序因子目录: {ranking_factor_dir}")
    
    if not os.path.exists(factors_dir):
        raise FileNotFoundError(f"因子缓存目录不存在: {factors_dir}")
    
    factor_files = [f for f in os.listdir(factors_dir) if f.endswith('_factors.pkl')]
    
    if len(factor_files) < sample_size:
        sample_size = len(factor_files)
    
    sample_files = random.sample(factor_files, sample_size)
    
    sample_factors = {}
    
    for factor_file in sample_files:
        stock_code = factor_file.replace('_factors.pkl', '')
        factor_path = os.path.join(factors_dir, factor_file)
        
        try:
            with open(factor_path, 'rb') as f:
                factor_data = pickle.load(f)
                if isinstance(factor_data, pd.DataFrame) and not factor_data.empty:
                    sample_factors[stock_code] = factor_data
        except Exception as e:
            print(f"加载 {stock_code} 因子数据失败: {e}")
    
    return sample_factors

def check_percentile_factors(sample_factors):
    """
    检查百分比排序因子
    
    Args:
        sample_factors (dict): 样本因子数据
    """
    print("\n检查百分比排序因子:")
    print("=" * 80)
    
    percentile_factors = ['turnover_std20_percentile', 'ReturnStd120_percentile']
    base_factors = ['turnover_std20', 'ReturnStd120']
    
    # 统计信息
    stats = {
        'total_stocks': len(sample_factors),
        'has_base_factors': {factor: 0 for factor in base_factors},
        'has_percentile_factors': {factor: 0 for factor in percentile_factors},
        'percentile_ranges': {factor: [] for factor in percentile_factors}
    }
    
    for i, (stock_code, factor_data) in enumerate(sample_factors.items(), 1):
        print(f"{i:2d}. {stock_code:<12}", end=" ")
        
        # 检查基础因子
        for factor in base_factors:
            if factor in factor_data.columns:
                stats['has_base_factors'][factor] += 1
                print(f"{factor}:✓", end=" ")
            else:
                print(f"{factor}:✗", end=" ")
        
        print("|", end=" ")
        
        # 检查百分比排序因子
        for factor in percentile_factors:
            if factor in factor_data.columns:
                stats['has_percentile_factors'][factor] += 1
                
                # 获取百分比排序值的范围
                percentile_values = factor_data[factor].dropna()
                if len(percentile_values) > 0:
                    min_val = percentile_values.min()
                    max_val = percentile_values.max()
                    stats['percentile_ranges'][factor].append((min_val, max_val))
                    print(f"{factor}:✓({min_val:.1f}-{max_val:.1f})", end=" ")
                else:
                    print(f"{factor}:✓(无数据)", end=" ")
            else:
                print(f"{factor}:✗", end=" ")
        
        print(f"因子数:{len(factor_data.columns)}")
    
    # 显示统计结果
    print("\n" + "=" * 80)
    print("统计结果:")
    print(f"样本股票数: {stats['total_stocks']}")
    
    print("\n基础因子覆盖率:")
    for factor in base_factors:
        coverage = stats['has_base_factors'][factor] / stats['total_stocks'] * 100
        print(f"  {factor}: {stats['has_base_factors'][factor]}/{stats['total_stocks']} ({coverage:.1f}%)")
    
    print("\n百分比排序因子覆盖率:")
    for factor in percentile_factors:
        coverage = stats['has_percentile_factors'][factor] / stats['total_stocks'] * 100
        print(f"  {factor}: {stats['has_percentile_factors'][factor]}/{stats['total_stocks']} ({coverage:.1f}%)")
    
    print("\n百分比排序值范围:")
    for factor in percentile_factors:
        if stats['percentile_ranges'][factor]:
            all_mins = [r[0] for r in stats['percentile_ranges'][factor]]
            all_maxs = [r[1] for r in stats['percentile_ranges'][factor]]
            overall_min = min(all_mins)
            overall_max = max(all_maxs)
            print(f"  {factor}: {overall_min:.1f} - {overall_max:.1f}")
        else:
            print(f"  {factor}: 无数据")

def analyze_percentile_distribution(sample_factors, target_date=None):
    """
    分析特定日期的百分比排序分布
    
    Args:
        sample_factors (dict): 样本因子数据
        target_date: 目标日期，如果为None则使用最新日期
    """
    print("\n分析百分比排序分布:")
    print("=" * 80)
    
    percentile_factors = ['turnover_std20_percentile', 'ReturnStd120_percentile']
    
    # 如果没有指定日期，使用最新的共同日期
    if target_date is None:
        common_dates = None
        for stock_code, factor_data in sample_factors.items():
            if 'Date' in factor_data.columns:
                stock_dates = set(factor_data['Date'].values)
                if common_dates is None:
                    common_dates = stock_dates
                else:
                    common_dates = common_dates.intersection(stock_dates)
        
        if common_dates:
            target_date = max(common_dates)
            print(f"使用最新共同日期: {target_date}")
        else:
            print("没有找到共同日期")
            return
    
    # 收集目标日期的百分比排序数据
    date_data = {}
    for factor in percentile_factors:
        date_data[factor] = []
    
    for stock_code, factor_data in sample_factors.items():
        date_mask = factor_data['Date'] == target_date
        if date_mask.any():
            date_row = factor_data[date_mask].iloc[0]
            for factor in percentile_factors:
                if factor in date_row and pd.notna(date_row[factor]):
                    date_data[factor].append({
                        'stock': stock_code,
                        'percentile': date_row[factor]
                    })
    
    # 显示分布情况
    for factor in percentile_factors:
        if date_data[factor]:
            print(f"\n{factor} 在 {target_date} 的分布:")
            percentiles = [item['percentile'] for item in date_data[factor]]
            
            print(f"  样本数: {len(percentiles)}")
            print(f"  最小值: {min(percentiles):.1f}")
            print(f"  最大值: {max(percentiles):.1f}")
            print(f"  平均值: {np.mean(percentiles):.1f}")
            print(f"  中位数: {np.median(percentiles):.1f}")
            
            # 显示前5和后5的股票
            sorted_data = sorted(date_data[factor], key=lambda x: x['percentile'])
            print(f"  排名最低的5只股票:")
            for i, item in enumerate(sorted_data[:5]):
                print(f"    {i+1}. {item['stock']}: {item['percentile']:.1f}%")
            
            print(f"  排名最高的5只股票:")
            for i, item in enumerate(sorted_data[-5:]):
                print(f"    {len(sorted_data)-4+i}. {item['stock']}: {item['percentile']:.1f}%")
        else:
            print(f"\n{factor}: 在 {target_date} 无数据")

def main():
    """
    主函数
    """
    print("=" * 60)
    print("百分比排序因子验证脚本")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 加载样本数据
        sample_factors = load_sample_factor_data(sample_size=20)
        
        if not sample_factors:
            print("没有找到任何因子数据")
            return
        
        print(f"成功加载 {len(sample_factors)} 只股票的因子数据")
        
        # 检查百分比排序因子
        check_percentile_factors(sample_factors)
        
        # 分析百分比排序分布
        analyze_percentile_distribution(sample_factors)
        
        print("\n" + "=" * 60)
        print("验证完成!")
        print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()