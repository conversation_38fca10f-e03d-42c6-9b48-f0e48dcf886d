#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排序因子生成主程序

功能：
1. 从'0-数据汇总/2-因子数据'目录读取个股因子数据
2. 计算排序因子（百分比排序）
3. 将结果保存到'0-数据汇总/2-因子数据/ranking_factors'目录
"""

import os
import sys
from datetime import datetime
from config import DATA_CONFIG

def check_data_directories():
    """
    检查数据目录是否存在
    """
    print("=" * 60)
    print("检查数据目录")
    print("=" * 60)
    
    # 检查原始股票数据目录
    stock_data_dir = DATA_CONFIG['stock_data_dir']
    print(f"原始股票数据目录: {stock_data_dir}")
    if os.path.exists(stock_data_dir):
        csv_files = [f for f in os.listdir(stock_data_dir) if f.endswith('_hfq.csv')]
        print(f"  ✅ 目录存在，发现 {len(csv_files)} 个股票数据文件")
    else:
        print(f"  ❌ 目录不存在")
        return False
    
    # 检查因子数据目录
    factor_cache_dir = DATA_CONFIG['factor_cache_dir']
    factors_dir = factor_cache_dir
    print(f"\n因子数据目录: {factors_dir}")
    if os.path.exists(factors_dir):
        factor_files = [f for f in os.listdir(factors_dir) if f.endswith('_factors.pkl')]
        print(f"  ✅ 目录存在，发现 {len(factor_files)} 个因子文件")
        if len(factor_files) == 0:
            print(f"  ⚠️  警告：因子文件数量为0，请先运行个股因子生成")
            return False
    else:
        print(f"  ❌ 目录不存在")
        return False
    
    # 检查排序因子保存目录
    ranking_factor_dir = DATA_CONFIG['ranking_factor_dir']
    print(f"\n排序因子保存目录: {ranking_factor_dir}")
    if os.path.exists(ranking_factor_dir):
        ranking_files = [f for f in os.listdir(ranking_factor_dir) if f.endswith('_ranking_factors.pkl')]
        print(f"  ✅ 目录存在，发现 {len(ranking_files)} 个排序因子文件")
    else:
        print(f"  ✅ 目录不存在，将自动创建")
        os.makedirs(ranking_factor_dir, exist_ok=True)
    
    print("\n✅ 数据目录检查完成")
    return True

def check_factor_data_sample():
    """
    检查因子数据样本
    """
    print("\n=" * 60)
    print("检查因子数据样本")
    print("=" * 60)
    
    import pickle
    import pandas as pd
    
    factor_cache_dir = DATA_CONFIG['factor_cache_dir']
    factors_dir = factor_cache_dir
    
    factor_files = [f for f in os.listdir(factors_dir) if f.endswith('_factors.pkl')]
    
    if not factor_files:
        print("❌ 没有找到因子文件")
        return False
    
    # 检查第一个文件
    sample_file = factor_files[0]
    sample_path = os.path.join(factors_dir, sample_file)
    stock_code = sample_file.replace('_factors.pkl', '')
    
    try:
        with open(sample_path, 'rb') as f:
            sample_data = pickle.load(f)
        
        print(f"样本股票: {stock_code}")
        print(f"数据形状: {sample_data.shape}")
        print(f"因子列表: {list(sample_data.columns)}")
        print(f"数据时间范围: {sample_data.index[0]} 到 {sample_data.index[-1]}")
        
        # 检查目标因子是否存在
        target_factors = ['turnover_std20', 'ReturnStd120']
        missing_factors = [f for f in target_factors if f not in sample_data.columns]
        
        if missing_factors:
            print(f"⚠️  警告：缺少目标因子: {missing_factors}")
            available_factors = [f for f in target_factors if f in sample_data.columns]
            if available_factors:
                print(f"✅ 可用目标因子: {available_factors}")
            else:
                print(f"❌ 没有可用的目标因子")
                return False
        else:
            print(f"✅ 所有目标因子都存在: {target_factors}")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取样本文件失败: {e}")
        return False

def run_ranking_calculation():
    """
    运行排序因子计算（使用优化版本）
    
    Returns:
        bool: 是否成功
    """
    print(f"\n开始运行排序因子计算 (优化版本)...")
    
    try:
        from calculate_factor_percentiles_optimized import calculate_factor_percentiles_optimized
        calculate_factor_percentiles_optimized()
        
        print("✅ 排序因子计算完成")
        return True
        
    except Exception as e:
        print(f"❌ 排序因子计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_results():
    """
    验证计算结果
    """
    print("\n=" * 60)
    print("验证计算结果")
    print("=" * 60)
    
    try:
        from verify_percentiles import main
        main()
        return True
    except Exception as e:
        print(f"❌ 结果验证失败: {e}")
        return False

def main():
    """
    主函数
    """
    print("=" * 80)
    print("排序因子生成程序")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 1. 检查数据目录
    if not check_data_directories():
        print("\n❌ 数据目录检查失败，程序退出")
        return
    
    # 2. 检查因子数据样本
    if not check_factor_data_sample():
        print("\n❌ 因子数据检查失败，程序退出")
        return
    
    # 3. 运行排序因子计算（优化版本）
    if not run_ranking_calculation():
        print("\n❌ 排序因子计算失败，程序退出")
        return
    
    # 4. 验证结果
    print("\n是否验证计算结果? (y/n) [默认: y]: ", end="")
    verify_choice = input().strip().lower()
    if verify_choice == '' or verify_choice == 'y':
        verify_results()
    
    print("\n=" * 80)
    print("排序因子生成程序完成")
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 显示结果目录
    ranking_factor_dir = DATA_CONFIG['ranking_factor_dir']
    if os.path.exists(ranking_factor_dir):
        ranking_files = [f for f in os.listdir(ranking_factor_dir) if f.endswith('_ranking_factors.pkl')]
        print(f"\n✅ 排序因子文件已保存到: {ranking_factor_dir}")
        print(f"   生成文件数量: {len(ranking_files)}")

if __name__ == "__main__":
    main()