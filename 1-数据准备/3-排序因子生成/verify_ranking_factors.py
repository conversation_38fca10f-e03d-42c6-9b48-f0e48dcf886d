#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证排序因子修复结果的脚本
"""

import os
import sys
import pickle
import pandas as pd
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import DATA_CONFIG

def verify_ranking_factors():
    """
    验证排序因子文件是否已经修复
    """
    print("=" * 60)
    print("验证排序因子修复结果")
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    ranking_factor_dir = DATA_CONFIG['ranking_factor_dir']
    
    if not os.path.exists(ranking_factor_dir):
        print("❌ 排序因子目录不存在")
        return False
    
    ranking_files = [f for f in os.listdir(ranking_factor_dir) if f.endswith('_ranking_factors.pkl')]
    
    if not ranking_files:
        print("❌ 没有找到排序因子文件")
        return False
    
    print(f"✅ 找到 {len(ranking_files)} 个排序因子文件")
    
    # 检查前几个文件
    sample_files = ranking_files[:3]
    
    all_good = True
    
    for i, filename in enumerate(sample_files, 1):
        print(f"\n--- 检查文件 {i}: {filename} ---")
        
        file_path = os.path.join(ranking_factor_dir, filename)
        
        try:
            with open(file_path, 'rb') as f:
                df = pickle.load(f)
            
            print(f"数据形状: {df.shape}")
            print(f"列数: {len(df.columns)}")
            
            # 检查列名
            columns = list(df.columns)
            rank_columns = [col for col in columns if col.endswith('_rank')]
            percentile_columns = [col for col in columns if col.endswith('_percentile')]
            other_columns = [col for col in columns if not col.endswith('_rank') and not col.endswith('_percentile')]
            
            print(f"_rank列数量: {len(rank_columns)}")
            print(f"_percentile列数量: {len(percentile_columns)}")
            print(f"其他列数量: {len(other_columns)}")
            
            if rank_columns:
                print(f"_rank列示例: {rank_columns[:5]}")
            
            if percentile_columns:
                print(f"⚠️  仍有_percentile列: {percentile_columns[:5]}")
                all_good = False
            
            if other_columns:
                print(f"其他列: {other_columns}")
            
            # 检查是否包含原始数据因子的排序
            original_factors = ['open', 'close', 'volume', 'high', 'low']
            original_rank_columns = [f"{factor}_rank" for factor in original_factors]
            found_original = [col for col in original_rank_columns if col in rank_columns]
            
            print(f"找到的原始数据排序因子: {found_original}")
            
            if not found_original:
                print("⚠️  没有找到原始数据排序因子")
                all_good = False
            
            # 检查数据范围
            if rank_columns:
                sample_col = rank_columns[0]
                valid_data = df[sample_col].dropna()
                if len(valid_data) > 0:
                    min_val = valid_data.min()
                    max_val = valid_data.max()
                    print(f"数据范围示例 ({sample_col}): [{min_val:.4f}, {max_val:.4f}]")
                    
                    if min_val < 0 or max_val > 1:
                        print(f"⚠️  数据范围异常")
                        all_good = False
            
            # 显示前几行数据
            print(f"\n数据预览:")
            print(df.head(3))
            
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
            all_good = False
    
    print("\n" + "=" * 60)
    if all_good:
        print("✅ 排序因子修复验证通过")
        print("- 所有文件都使用_rank后缀")
        print("- 包含原始数据排序因子")
        print("- 数据范围正常")
    else:
        print("❌ 排序因子修复验证失败")
        print("- 仍存在问题需要修复")
    
    return all_good

def check_individual_factor_files():
    """
    检查个股因子文件是否已经清理
    """
    print("\n" + "=" * 60)
    print("检查个股因子文件清理情况")
    print("=" * 60)
    
    factor_cache_dir = DATA_CONFIG['factor_cache_dir']
    
    if not os.path.exists(factor_cache_dir):
        print("❌ 个股因子目录不存在")
        return False
    
    factor_files = [f for f in os.listdir(factor_cache_dir) if f.endswith('_factors.pkl')]
    
    if not factor_files:
        print("❌ 没有找到个股因子文件")
        return False
    
    print(f"✅ 找到 {len(factor_files)} 个个股因子文件")
    
    # 检查第一个文件
    sample_file = factor_files[0]
    file_path = os.path.join(factor_cache_dir, sample_file)
    
    try:
        with open(file_path, 'rb') as f:
            df = pickle.load(f)
        
        print(f"\n样本文件: {sample_file}")
        print(f"数据形状: {df.shape}")
        
        columns = list(df.columns)
        rank_columns = [col for col in columns if col.endswith('_rank')]
        percentile_columns = [col for col in columns if col.endswith('_percentile')]
        
        print(f"_rank列数量: {len(rank_columns)}")
        print(f"_percentile列数量: {len(percentile_columns)}")
        
        if rank_columns:
            print(f"⚠️  仍有_rank列: {rank_columns[:5]}")
            return False
        
        if percentile_columns:
            print(f"⚠️  仍有_percentile列: {percentile_columns[:5]}")
            return False
        
        print("✅ 个股因子文件已清理，无_rank或_percentile列")
        return True
        
    except Exception as e:
        print(f"❌ 检查个股因子文件失败: {e}")
        return False

if __name__ == "__main__":
    # 验证排序因子
    ranking_ok = verify_ranking_factors()
    
    # 检查个股因子文件
    factor_ok = check_individual_factor_files()
    
    print("\n" + "=" * 60)
    print("总体验证结果:")
    if ranking_ok and factor_ok:
        print("✅ 所有修复都已完成")
    else:
        print("❌ 仍有问题需要修复")
    print("=" * 60)