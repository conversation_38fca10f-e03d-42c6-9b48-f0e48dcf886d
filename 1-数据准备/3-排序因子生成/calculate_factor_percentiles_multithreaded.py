#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多线程优化版因子百分比排序计算脚本
大幅提升计算速度，支持并行处理
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime
import pickle
from tqdm import tqdm
import gc
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from threading import Lock
import multiprocessing as mp
from config import DATA_CONFIG
from ranking_config import get_target_factors, print_ranking_config

# 全局锁用于线程安全
data_lock = Lock()

def load_stock_data_cache(factor_files, factors_dir, original_data_dir, original_factors, computed_factors):
    """
    预加载所有股票数据到内存缓存
    """
    print("\n预加载股票数据到内存...")
    
    factor_cache = {}
    original_cache = {}
    
    # 加载因子数据
    if computed_factors:
        for file in tqdm(factor_files, desc="加载因子数据"):
            try:
                stock_code = file.replace('_factors.pkl', '')
                file_path = os.path.join(factors_dir, file)
                with open(file_path, 'rb') as f:
                    df = pickle.load(f)
                factor_cache[stock_code] = df[computed_factors].copy() if computed_factors else pd.DataFrame()
            except Exception as e:
                continue
    
    # 加载原始数据
    if original_factors:
        for file in tqdm(factor_files, desc="加载原始数据"):
            try:
                stock_code = file.replace('_factors.pkl', '')
                original_file = os.path.join(original_data_dir, f"{stock_code}_hfq.csv")
                if os.path.exists(original_file):
                    df = pd.read_csv(original_file, index_col=0, parse_dates=True)
                    original_cache[stock_code] = df[original_factors].copy() if original_factors else pd.DataFrame()
            except Exception as e:
                continue
    
    print(f"✅ 缓存加载完成: {len(factor_cache)} 个因子文件, {len(original_cache)} 个原始数据文件")
    return factor_cache, original_cache

def process_date_batch(args):
    """
    处理单个日期批次的函数（用于多进程）
    """
    batch_dates, factor_cache, original_cache, all_factors, original_factors, computed_factors = args
    
    batch_results = {}
    
    for date in batch_dates:
        # 收集该日期所有股票的因子值
        daily_factor_data = {factor: {} for factor in all_factors}
        
        # 处理计算因子
        if computed_factors:
            for stock_code, factor_df in factor_cache.items():
                if date in factor_df.index:
                    for factor in computed_factors:
                        if factor in factor_df.columns:
                            value = factor_df.loc[date, factor]
                            if pd.notna(value):
                                daily_factor_data[factor][stock_code] = value
        
        # 处理原始数据因子
        if original_factors:
            for stock_code, original_df in original_cache.items():
                if date in original_df.index:
                    for factor in original_factors:
                        if factor in original_df.columns:
                            value = original_df.loc[date, factor]
                            if pd.notna(value):
                                daily_factor_data[factor][stock_code] = value
        
        # 计算每个因子的百分比排序
        date_results = {}
        for factor in all_factors:
            if len(daily_factor_data[factor]) > 1:
                factor_series = pd.Series(daily_factor_data[factor])
                percentiles = factor_series.rank(pct=True, method='min')
                date_results[factor] = percentiles.to_dict()
        
        batch_results[date] = date_results
    
    return batch_results

def process_single_date(args):
    """
    处理单个交易日的函数（用于多线程）
    """
    date, factor_cache, original_cache, all_factors, original_factors, computed_factors = args
    
    # 收集该日期所有股票的因子值
    daily_factor_data = {factor: {} for factor in all_factors}
    
    # 处理计算因子
    if computed_factors:
        for stock_code, factor_df in factor_cache.items():
            if date in factor_df.index:
                for factor in computed_factors:
                    if factor in factor_df.columns:
                        value = factor_df.loc[date, factor]
                        if pd.notna(value):
                            daily_factor_data[factor][stock_code] = value
    
    # 处理原始数据因子
    if original_factors:
        for stock_code, original_df in original_cache.items():
            if date in original_df.index:
                for factor in original_factors:
                    if factor in original_df.columns:
                        value = original_df.loc[date, factor]
                        if pd.notna(value):
                            daily_factor_data[factor][stock_code] = value
    
    # 计算每个因子的百分比排序
    date_results = {}
    for factor in all_factors:
        if len(daily_factor_data[factor]) > 1:
            factor_series = pd.Series(daily_factor_data[factor])
            percentiles = factor_series.rank(pct=True, method='min')
            date_results[factor] = percentiles.to_dict()
    
    return date, date_results

def get_all_available_factors(factor_files, factors_dir):
    """
    获取所有可用的因子列
    """
    original_data_columns = []
    factor_data_columns = []
    
    # 从第一个因子文件获取计算因子列
    if factor_files:
        sample_file = os.path.join(factors_dir, factor_files[0])
        try:
            with open(sample_file, 'rb') as f:
                sample_df = pickle.load(f)
            factor_data_columns = [col for col in sample_df.columns 
                                 if not col.endswith('_rank') and not col.endswith('_percentile')]
        except Exception as e:
            print(f"读取样本文件失败: {e}")
    
    # 从原始数据文件获取原始数据列
    stock_data_dir = DATA_CONFIG['stock_data_dir']
    stock_files = [f for f in os.listdir(stock_data_dir) if f.endswith('_hfq.csv')]
    if stock_files:
        sample_stock_file = os.path.join(stock_data_dir, stock_files[0])
        try:
            stock_df = pd.read_csv(sample_stock_file)
            original_data_columns = [col for col in stock_df.columns 
                                   if col not in ['date', 'code', 'adjustflag', 'tradestatus', 'isST']]
        except Exception as e:
            print(f"读取原始数据样本文件失败: {e}")
    
    return original_data_columns, factor_data_columns

def calculate_factor_percentiles_multithreaded(custom_factors=None, include_original=True, include_computed=True, 
                                              max_workers=None, use_multiprocessing=False):
    """
    多线程优化版本：为所有可用因子生成排序数据
    
    Args:
        custom_factors (list, optional): 自定义因子列表
        include_original (bool): 是否包含原始数据列
        include_computed (bool): 是否包含计算因子列
        max_workers (int, optional): 最大工作线程数，默认为CPU核心数
        use_multiprocessing (bool): 是否使用多进程（更快但内存占用更大）
    """
    print("=" * 60)
    print("多线程优化版因子百分比排序计算脚本")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 配置参数
    factor_cache_dir = DATA_CONFIG['factor_cache_dir']
    factors_dir = factor_cache_dir
    ranking_factor_dir = DATA_CONFIG['ranking_factor_dir']
    original_data_dir = DATA_CONFIG['stock_data_dir']
    
    if max_workers is None:
        max_workers = min(mp.cpu_count(), 8)  # 限制最大线程数避免过度占用资源
    
    print(f"使用 {max_workers} 个工作线程")
    print(f"多进程模式: {'是' if use_multiprocessing else '否'}")
    
    # 确保排序因子目录存在
    os.makedirs(ranking_factor_dir, exist_ok=True)
    
    # 获取所有因子文件
    factor_files = [f for f in os.listdir(factors_dir) if f.endswith('_factors.pkl')]
    print(f"发现 {len(factor_files)} 个因子文件")
    
    if not factor_files:
        print("未找到因子文件！")
        return
    
    # 获取所有可用的因子列
    print("\n分析可用因子列...")
    original_data_columns, factor_data_columns = get_all_available_factors(factor_files, factors_dir)
    
    # 确定要处理的因子列表
    all_factors = []
    original_factors = []
    computed_factors = []
    
    if custom_factors:
        # 使用自定义因子列表
        print(f"使用自定义因子列表: {custom_factors}")
        for factor in custom_factors:
            all_factors.append(factor)
            if factor in original_data_columns:
                original_factors.append(factor)
            elif factor in factor_data_columns:
                computed_factors.append(factor)
    else:
        # 使用所有可用因子
        if include_original:
            original_factors = original_data_columns
            all_factors.extend(original_data_columns)
        if include_computed:
            computed_factors = factor_data_columns
            all_factors.extend(factor_data_columns)
    
    if not all_factors:
        print("错误：没有找到可处理的因子列")
        return
    
    print(f"\n原始数据因子 ({len(original_factors)} 个): {original_factors}")
    print(f"计算因子 ({len(computed_factors)} 个): {computed_factors}")
    print(f"总共将处理 {len(all_factors)} 个因子，生成对应的排序因子")
    
    # 预加载所有数据到内存
    factor_cache, original_cache = load_stock_data_cache(
        factor_files, factors_dir, original_data_dir, original_factors, computed_factors
    )
    
    # 收集所有交易日期
    print("\n收集交易日期...")
    all_dates = set()
    
    # 从因子缓存收集日期
    for df in factor_cache.values():
        all_dates.update(df.index)
    
    # 从原始数据缓存收集日期
    for df in original_cache.values():
        all_dates.update(df.index)
    
    sorted_dates = sorted(list(all_dates))
    print(f"发现 {len(sorted_dates)} 个交易日")
    
    if len(sorted_dates) == 0:
        print("未找到交易日期！")
        return
    
    # 为每个股票初始化百分比排序数据
    percentile_data = {}
    for file in factor_files:
        stock_code = file.replace('_factors.pkl', '')
        percentile_data[stock_code] = {f"{factor}_rank": {} for factor in all_factors}
    
    # 多线程处理交易日
    print(f"\n开始多线程处理 {len(sorted_dates)} 个交易日...")
    
    if use_multiprocessing:
        # 使用多进程处理（更快但内存占用更大）
        batch_size = max(1, len(sorted_dates) // (max_workers * 4))  # 每个进程处理更多日期
        date_batches = [sorted_dates[i:i+batch_size] for i in range(0, len(sorted_dates), batch_size)]
        
        args_list = [(batch, factor_cache, original_cache, all_factors, original_factors, computed_factors) 
                     for batch in date_batches]
        
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            batch_results = list(tqdm(executor.map(process_date_batch, args_list), 
                                    total=len(date_batches), desc="多进程处理"))
        
        # 合并批次结果
        for batch_result in batch_results:
            for date, date_results in batch_result.items():
                for factor, stock_percentiles in date_results.items():
                    for stock_code, percentile in stock_percentiles.items():
                        if stock_code in percentile_data:
                            percentile_data[stock_code][f"{factor}_rank"][date] = percentile
    
    else:
        # 使用多线程处理（内存友好）
        args_list = [(date, factor_cache, original_cache, all_factors, original_factors, computed_factors) 
                     for date in sorted_dates]
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            results = list(tqdm(executor.map(process_single_date, args_list), 
                              total=len(sorted_dates), desc="多线程处理"))
        
        # 整理结果
        for date, date_results in results:
            for factor, stock_percentiles in date_results.items():
                for stock_code, percentile in stock_percentiles.items():
                    if stock_code in percentile_data:
                        percentile_data[stock_code][f"{factor}_rank"][date] = percentile
    
    # 清理缓存释放内存
    del factor_cache, original_cache
    gc.collect()
    
    # 保存结果到各个股票文件
    print("\n保存百分比排序结果...")
    success_count = 0
    
    for file in tqdm(factor_files, desc="保存结果"):
        try:
            stock_code = file.replace('_factors.pkl', '')
            file_path = os.path.join(factors_dir, file)
            
            # 加载原始数据
            with open(file_path, 'rb') as f:
                df = pickle.load(f)
            
            # 添加百分比排序数据
            ranking_columns = []
            for factor in all_factors:
                rank_col = f"{factor}_rank"
                rank_dict = percentile_data[stock_code][rank_col]
                
                if rank_dict:
                    # 创建百分比排序Series
                    rank_series = pd.Series(rank_dict, name=rank_col)
                    rank_series.index.name = 'Date'
                    
                    # 合并到原DataFrame
                    if rank_col in df.columns:
                        df[rank_col] = df[rank_col].combine_first(rank_series)
                    else:
                        df = df.join(rank_series, how='left')
                    
                    ranking_columns.append(rank_col)
            
            # 1. 保存完整的因子数据到原始目录（包含排序因子）
            with open(file_path, 'wb') as f:
                pickle.dump(df, f)
            
            # 2. 提取并保存排序因子到专门目录
            if ranking_columns:
                ranking_data = df[ranking_columns].copy()
                # 添加日期列
                ranking_data.reset_index(inplace=True)
                ranking_data.rename(columns={'Date': 'date'}, inplace=True)
                
                ranking_path = os.path.join(ranking_factor_dir, f"{stock_code}_ranking_factors.pkl")
                
                with open(ranking_path, 'wb') as f:
                    pickle.dump(ranking_data, f)
            
            success_count += 1
            
        except Exception as e:
            print(f"保存股票 {stock_code} 时出错: {e}")
            continue
    
    print(f"\n成功更新 {success_count}/{len(factor_files)} 个股票文件")
    print("=" * 60)
    print(f"计算完成！结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

if __name__ == "__main__":
    # 示例用法
    print("多线程优化版排序因子计算")
    print("选择运行模式:")
    print("1. 多线程模式（推荐，内存友好）")
    print("2. 多进程模式（最快，但内存占用大）")
    print("3. 测试模式（只处理少量因子）")
    
    choice = input("请选择模式 (1/2/3) [默认: 1]: ").strip()
    
    if choice == '2':
        # 多进程模式
        calculate_factor_percentiles_multithreaded(use_multiprocessing=True)
    elif choice == '3':
        # 测试模式
        test_factors = ['open', 'close', 'volume', 'turn']
        calculate_factor_percentiles_multithreaded(custom_factors=test_factors)
    else:
        # 默认多线程模式
        calculate_factor_percentiles_multithreaded()