#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查个股因子文件结构
"""

import os
import sys
import pickle
import pandas as pd

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import DATA_CONFIG

def check_factor_file_structure():
    """
    检查个股因子文件的结构
    """
    factor_cache_dir = DATA_CONFIG['factor_cache_dir']
    
    # 获取第一个因子文件
    factor_files = [f for f in os.listdir(factor_cache_dir) if f.endswith('_factors.pkl')]
    
    if not factor_files:
        print("没有找到因子文件")
        return
    
    sample_file = os.path.join(factor_cache_dir, factor_files[0])
    
    print(f"检查文件: {factor_files[0]}")
    
    try:
        with open(sample_file, 'rb') as f:
            df = pickle.load(f)
        
        print(f"数据类型: {type(df)}")
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        print(f"索引: {df.index}")
        
        # 检查是否有日期相关的列或索引
        if 'date' in df.columns:
            print(f"\n日期列存在: {df['date'].dtype}")
            print(f"日期范围: {df['date'].min()} 到 {df['date'].max()}")
            print(f"日期示例: {df['date'].head()}")
        elif hasattr(df.index, 'name') and df.index.name == 'date':
            print(f"\n日期作为索引: {df.index.dtype}")
            print(f"日期范围: {df.index.min()} 到 {df.index.max()}")
            print(f"日期示例: {df.index[:5]}")
        elif pd.api.types.is_datetime64_any_dtype(df.index):
            print(f"\n索引是日期类型: {df.index.dtype}")
            print(f"日期范围: {df.index.min()} 到 {df.index.max()}")
            print(f"日期示例: {df.index[:5]}")
        else:
            print("\n没有找到明显的日期列或索引")
            print(f"索引类型: {type(df.index)}")
            print(f"索引示例: {df.index[:5]}")
        
        print(f"\n数据预览:")
        print(df.head())
        
        print(f"\n数据信息:")
        print(df.info())
        
    except Exception as e:
        print(f"读取文件失败: {e}")
        import traceback
        print(traceback.format_exc())

def check_stock_data_structure():
    """
    检查原始股票数据文件的结构
    """
    print("\n" + "="*50)
    print("检查原始股票数据文件结构")
    print("="*50)
    
    stock_data_dir = DATA_CONFIG['stock_data_dir']
    
    # 获取第一个股票数据文件
    stock_files = [f for f in os.listdir(stock_data_dir) if f.endswith('_hfq.csv')]
    
    if not stock_files:
        print("没有找到股票数据文件")
        return
    
    sample_file = os.path.join(stock_data_dir, stock_files[0])
    
    print(f"检查文件: {stock_files[0]}")
    
    try:
        df = pd.read_csv(sample_file)
        
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        if 'date' in df.columns:
            print(f"\n日期列存在: {df['date'].dtype}")
            print(f"日期范围: {df['date'].min()} 到 {df['date'].max()}")
            print(f"日期示例: {df['date'].head()}")
        
        print(f"\n数据预览:")
        print(df.head())
        
    except Exception as e:
        print(f"读取文件失败: {e}")

if __name__ == "__main__":
    check_factor_file_structure()
    check_stock_data_structure()