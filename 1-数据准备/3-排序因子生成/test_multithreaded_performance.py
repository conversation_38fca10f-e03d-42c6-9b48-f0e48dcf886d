#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多线程版本的性能提升
对比原版本和多线程版本的计算速度
"""

import os
import sys
import time
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import DATA_CONFIG

def test_performance_comparison():
    """
    性能对比测试
    """
    print("=" * 80)
    print("排序因子计算性能对比测试")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 测试因子列表（选择少量因子进行快速测试）
    test_factors = ['turn']  # 只测试一个因子，快速验证
    
    print(f"测试因子: {test_factors}")
    print(f"测试模式: 只处理单个因子以快速验证性能")
    
    # 测试1: 原版本
    print("\n" + "=" * 50)
    print("测试1: 原版本（单线程）")
    print("=" * 50)
    
    start_time = time.time()
    try:
        from calculate_factor_percentiles_optimized import calculate_factor_percentiles_optimized
        calculate_factor_percentiles_optimized(custom_factors=test_factors)
        original_time = time.time() - start_time
        print(f"✅ 原版本完成，耗时: {original_time:.2f} 秒")
    except Exception as e:
        print(f"❌ 原版本测试失败: {e}")
        original_time = None
    
    # 测试2: 多线程版本
    print("\n" + "=" * 50)
    print("测试2: 多线程版本")
    print("=" * 50)
    
    start_time = time.time()
    try:
        from calculate_factor_percentiles_multithreaded import calculate_factor_percentiles_multithreaded
        calculate_factor_percentiles_multithreaded(custom_factors=test_factors, use_multiprocessing=False)
        multithreaded_time = time.time() - start_time
        print(f"✅ 多线程版本完成，耗时: {multithreaded_time:.2f} 秒")
    except Exception as e:
        print(f"❌ 多线程版本测试失败: {e}")
        multithreaded_time = None
    
    # 测试3: 多进程版本
    print("\n" + "=" * 50)
    print("测试3: 多进程版本")
    print("=" * 50)
    
    start_time = time.time()
    try:
        from calculate_factor_percentiles_multithreaded import calculate_factor_percentiles_multithreaded
        calculate_factor_percentiles_multithreaded(custom_factors=test_factors, use_multiprocessing=True)
        multiprocessing_time = time.time() - start_time
        print(f"✅ 多进程版本完成，耗时: {multiprocessing_time:.2f} 秒")
    except Exception as e:
        print(f"❌ 多进程版本测试失败: {e}")
        multiprocessing_time = None
    
    # 性能对比总结
    print("\n" + "=" * 80)
    print("性能对比总结")
    print("=" * 80)
    
    if original_time:
        print(f"原版本（单线程）: {original_time:.2f} 秒")
    
    if multithreaded_time:
        print(f"多线程版本: {multithreaded_time:.2f} 秒")
        if original_time:
            speedup = original_time / multithreaded_time
            print(f"多线程加速比: {speedup:.2f}x")
    
    if multiprocessing_time:
        print(f"多进程版本: {multiprocessing_time:.2f} 秒")
        if original_time:
            speedup = original_time / multiprocessing_time
            print(f"多进程加速比: {speedup:.2f}x")
    
    # 推荐使用的版本
    print("\n推荐使用:")
    if multiprocessing_time and multithreaded_time:
        if multiprocessing_time < multithreaded_time:
            print("🚀 多进程版本（最快，但内存占用较大）")
        else:
            print("🚀 多线程版本（平衡速度和内存占用）")
    elif multithreaded_time:
        print("🚀 多线程版本")
    elif multiprocessing_time:
        print("🚀 多进程版本")
    else:
        print("⚠️  建议检查代码，所有优化版本都失败了")
    
    print("\n" + "=" * 80)
    print(f"测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

def quick_benchmark():
    """
    快速基准测试
    """
    print("快速基准测试 - 多线程版本")
    print("=" * 50)
    
    # 只测试多线程版本的不同配置
    test_factors = ['turn']
    
    configs = [
        {'max_workers': 2, 'use_multiprocessing': False, 'name': '2线程'},
        {'max_workers': 4, 'use_multiprocessing': False, 'name': '4线程'},
        {'max_workers': 8, 'use_multiprocessing': False, 'name': '8线程'},
        {'max_workers': 4, 'use_multiprocessing': True, 'name': '4进程'},
    ]
    
    results = []
    
    for config in configs:
        print(f"\n测试配置: {config['name']}")
        start_time = time.time()
        
        try:
            from calculate_factor_percentiles_multithreaded import calculate_factor_percentiles_multithreaded
            calculate_factor_percentiles_multithreaded(
                custom_factors=test_factors,
                max_workers=config['max_workers'],
                use_multiprocessing=config['use_multiprocessing']
            )
            elapsed_time = time.time() - start_time
            results.append((config['name'], elapsed_time))
            print(f"✅ {config['name']} 完成，耗时: {elapsed_time:.2f} 秒")
        except Exception as e:
            print(f"❌ {config['name']} 失败: {e}")
    
    # 显示结果
    print("\n基准测试结果:")
    print("-" * 30)
    for name, time_taken in sorted(results, key=lambda x: x[1]):
        print(f"{name:<10}: {time_taken:.2f} 秒")

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 完整性能对比（原版本 vs 多线程版本 vs 多进程版本）")
    print("2. 快速基准测试（只测试多线程版本的不同配置）")
    
    choice = input("请选择 (1/2) [默认: 2]: ").strip()
    
    if choice == '1':
        test_performance_comparison()
    else:
        quick_benchmark()