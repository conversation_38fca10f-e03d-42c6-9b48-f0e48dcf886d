#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复排序因子问题的脚本

问题：
1. 排序因子使用了_percentile后缀而不是_rank后缀
2. 个股因子数据中已存在_percentile列导致重复
3. 原始股票数据的排序因子没有正确生成

解决方案：
1. 清理现有的排序因子文件
2. 重新生成使用_rank后缀的排序因子
3. 确保原始数据因子和计算因子都能正确处理
"""

import os
import sys
import shutil
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import DATA_CONFIG

def clean_existing_ranking_factors():
    """
    清理现有的排序因子文件
    """
    print("清理现有的排序因子文件...")
    
    ranking_factor_dir = DATA_CONFIG['ranking_factor_dir']
    if os.path.exists(ranking_factor_dir):
        # 备份现有文件
        backup_dir = f"{ranking_factor_dir}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        print(f"备份现有文件到: {backup_dir}")
        shutil.copytree(ranking_factor_dir, backup_dir)
        
        # 清理现有文件
        shutil.rmtree(ranking_factor_dir)
        print(f"已清理目录: {ranking_factor_dir}")
    
    # 重新创建目录
    os.makedirs(ranking_factor_dir, exist_ok=True)
    print(f"重新创建目录: {ranking_factor_dir}")

def clean_existing_percentile_columns():
    """
    清理个股因子数据中的_percentile列
    """
    print("\n清理个股因子数据中的_percentile列...")
    
    import pickle
    import pandas as pd
    
    factor_cache_dir = DATA_CONFIG['factor_cache_dir']
    factor_files = [f for f in os.listdir(factor_cache_dir) if f.endswith('_factors.pkl')]
    
    cleaned_count = 0
    for file in factor_files:
        try:
            file_path = os.path.join(factor_cache_dir, file)
            
            # 读取数据
            with open(file_path, 'rb') as f:
                df = pickle.load(f)
            
            # 检查是否有_percentile或_rank列
            percentile_cols = [col for col in df.columns if col.endswith('_percentile') or col.endswith('_rank')]
            
            if percentile_cols:
                print(f"清理 {file}: {percentile_cols}")
                # 删除这些列
                df = df.drop(columns=percentile_cols)
                
                # 保存清理后的数据
                with open(file_path, 'wb') as f:
                    pickle.dump(df, f)
                
                cleaned_count += 1
                
        except Exception as e:
            print(f"清理 {file} 时出错: {e}")
            continue
    
    print(f"已清理 {cleaned_count} 个因子文件中的排序列")

def regenerate_ranking_factors():
    """
    重新生成排序因子
    """
    print("\n重新生成排序因子...")
    
    try:
        from calculate_factor_percentiles_optimized import calculate_factor_percentiles_optimized
        
        # 重新生成排序因子
        calculate_factor_percentiles_optimized(
            custom_factors=None,  # 处理所有可用因子
            include_original=True,  # 包含原始数据因子
            include_computed=True   # 包含计算因子
        )
        
        print("✅ 排序因子重新生成完成")
        
    except Exception as e:
        print(f"❌ 重新生成排序因子失败: {e}")
        import traceback
        print(traceback.format_exc())

def verify_results():
    """
    验证修复结果
    """
    print("\n验证修复结果...")
    
    import pickle
    import pandas as pd
    
    ranking_factor_dir = DATA_CONFIG['ranking_factor_dir']
    
    if not os.path.exists(ranking_factor_dir):
        print("❌ 排序因子目录不存在")
        return False
    
    ranking_files = [f for f in os.listdir(ranking_factor_dir) if f.endswith('_ranking_factors.pkl')]
    
    if not ranking_files:
        print("❌ 没有找到排序因子文件")
        return False
    
    print(f"✅ 找到 {len(ranking_files)} 个排序因子文件")
    
    # 检查第一个文件的内容
    sample_file = os.path.join(ranking_factor_dir, ranking_files[0])
    
    try:
        with open(sample_file, 'rb') as f:
            df = pickle.load(f)
        
        print(f"\n样本文件: {ranking_files[0]}")
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        # 检查列名是否都使用_rank后缀
        rank_columns = [col for col in df.columns if col.endswith('_rank')]
        non_rank_columns = [col for col in df.columns if not col.endswith('_rank')]
        
        print(f"\n_rank列数量: {len(rank_columns)}")
        print(f"非_rank列: {non_rank_columns}")
        
        if non_rank_columns:
            print("⚠️  发现非_rank列，可能存在问题")
        else:
            print("✅ 所有列都使用_rank后缀")
        
        # 检查数据内容
        print(f"\n数据预览:")
        print(df.head())
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """
    主函数
    """
    print("=" * 80)
    print("修复排序因子问题")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 1. 清理现有的排序因子文件
    clean_existing_ranking_factors()
    
    # 2. 清理个股因子数据中的_percentile列
    clean_existing_percentile_columns()
    
    # 3. 重新生成排序因子
    regenerate_ranking_factors()
    
    # 4. 验证结果
    if verify_results():
        print("\n✅ 排序因子修复完成")
    else:
        print("\n❌ 排序因子修复失败")
    
    print("\n=" * 80)
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

if __name__ == "__main__":
    main()