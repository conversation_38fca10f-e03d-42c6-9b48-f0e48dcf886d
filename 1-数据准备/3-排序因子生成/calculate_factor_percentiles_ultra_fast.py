#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超高速因子百分比排序计算脚本
针对大规模数据进行极致优化

主要优化策略：
1. 智能内存管理和数据分块
2. 向量化计算和批量处理
3. 异步I/O和并行计算
4. 缓存优化和增量更新
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime
import pickle
from tqdm import tqdm
import gc
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from threading import Lock
import multiprocessing as mp
from functools import partial
import warnings
warnings.filterwarnings('ignore')

from config import DATA_CONFIG
from ranking_config import get_target_factors, print_ranking_config

class UltraFastRankingCalculator:
    """
    超高速排序因子计算器
    """
    
    def __init__(self, max_workers=None, chunk_size=1000, memory_limit_gb=8):
        self.max_workers = max_workers or min(mp.cpu_count(), 12)
        self.chunk_size = chunk_size
        self.memory_limit_gb = memory_limit_gb
        
        # 配置路径
        self.factor_cache_dir = DATA_CONFIG['factor_cache_dir']
        self.ranking_factor_dir = DATA_CONFIG['ranking_factor_dir']
        self.original_data_dir = DATA_CONFIG['stock_data_dir']
        
        # 确保目录存在
        os.makedirs(self.ranking_factor_dir, exist_ok=True)
        
        print(f"🚀 超高速排序计算器初始化完成")
        print(f"   最大工作线程: {self.max_workers}")
        print(f"   数据块大小: {self.chunk_size}")
        print(f"   内存限制: {self.memory_limit_gb}GB")
    
    def get_available_factors(self):
        """
        快速获取可用因子
        """
        factor_files = [f for f in os.listdir(self.factor_cache_dir) if f.endswith('_factors.pkl')]
        
        if not factor_files:
            return [], [], []
        
        # 从样本文件快速获取列信息
        sample_factor_file = os.path.join(self.factor_cache_dir, factor_files[0])
        with open(sample_factor_file, 'rb') as f:
            factor_df = pickle.load(f)
        
        factor_columns = [col for col in factor_df.columns 
                         if not col.endswith('_rank') and not col.endswith('_percentile')]
        
        # 从原始数据获取列信息
        stock_files = [f for f in os.listdir(self.original_data_dir) if f.endswith('_hfq.csv')]
        if stock_files:
            sample_stock_file = os.path.join(self.original_data_dir, stock_files[0])
            stock_df = pd.read_csv(sample_stock_file, nrows=1)  # 只读第一行获取列名
            original_columns = [col for col in stock_df.columns 
                              if col not in ['date', 'code', 'adjustflag', 'tradestatus', 'isST']]
        else:
            original_columns = []
        
        return factor_files, original_columns, factor_columns
    
    def load_data_smart(self, factor_files, original_factors, computed_factors):
        """
        智能数据加载 - 只加载需要的数据
        """
        print("\n🔄 智能加载数据...")
        
        factor_data = {}
        original_data = {}
        all_dates = set()
        
        # 并行加载因子数据
        if computed_factors:
            def load_factor_file(file):
                stock_code = file.replace('_factors.pkl', '')
                file_path = os.path.join(self.factor_cache_dir, file)
                try:
                    with open(file_path, 'rb') as f:
                        df = pickle.load(f)
                    # 只保留需要的列
                    df_filtered = df[computed_factors].copy()
                    return stock_code, df_filtered, set(df_filtered.index)
                except:
                    return stock_code, pd.DataFrame(), set()
            
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                results = list(tqdm(executor.map(load_factor_file, factor_files), 
                                  total=len(factor_files), desc="加载因子数据"))
            
            for stock_code, df, dates in results:
                if not df.empty:
                    factor_data[stock_code] = df
                    all_dates.update(dates)
        
        # 并行加载原始数据
        if original_factors:
            def load_original_file(file):
                stock_code = file.replace('_factors.pkl', '')
                original_file = os.path.join(self.original_data_dir, f"{stock_code}_hfq.csv")
                try:
                    if os.path.exists(original_file):
                        df = pd.read_csv(original_file, index_col=0, parse_dates=True)
                        # 只保留需要的列
                        df_filtered = df[original_factors].copy()
                        return stock_code, df_filtered, set(df_filtered.index)
                except:
                    pass
                return stock_code, pd.DataFrame(), set()
            
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                results = list(tqdm(executor.map(load_original_file, factor_files), 
                                  total=len(factor_files), desc="加载原始数据"))
            
            for stock_code, df, dates in results:
                if not df.empty:
                    original_data[stock_code] = df
                    all_dates.update(dates)
        
        sorted_dates = sorted(list(all_dates))
        
        print(f"✅ 数据加载完成:")
        print(f"   因子数据: {len(factor_data)} 个股票")
        print(f"   原始数据: {len(original_data)} 个股票")
        print(f"   交易日期: {len(sorted_dates)} 个")
        
        return factor_data, original_data, sorted_dates
    

    
    def calculate_rankings_ultra_fast(self, custom_factors=None, include_original=True, include_computed=True):
        """
        超高速排序计算主函数
        """
        print("=" * 80)
        print("🚀 超高速因子百分比排序计算")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        # 获取可用因子
        factor_files, original_columns, factor_columns = self.get_available_factors()
        
        if not factor_files:
            print("❌ 未找到因子文件！")
            return
        
        print(f"📊 发现 {len(factor_files)} 个股票文件")
        
        # 确定处理的因子
        all_factors = []
        original_factors = []
        computed_factors = []
        
        if custom_factors:
            print(f"🎯 使用自定义因子: {custom_factors}")
            for factor in custom_factors:
                all_factors.append(factor)
                if factor in original_columns:
                    original_factors.append(factor)
                elif factor in factor_columns:
                    computed_factors.append(factor)
        else:
            if include_original:
                original_factors = original_columns
                all_factors.extend(original_columns)
            if include_computed:
                computed_factors = factor_columns
                all_factors.extend(factor_columns)
        
        if not all_factors:
            print("❌ 没有找到可处理的因子")
            return
        
        print(f"\n📈 处理因子统计:")
        print(f"   原始数据因子: {len(original_factors)} 个")
        print(f"   计算因子: {len(computed_factors)} 个")
        print(f"   总计: {len(all_factors)} 个")
        
        # 智能加载数据
        factor_data, original_data, sorted_dates = self.load_data_smart(
            factor_files, original_factors, computed_factors
        )
        
        if not sorted_dates:
            print("❌ 未找到交易日期数据")
            return
        
        # 分块处理日期
        date_chunks = [sorted_dates[i:i+self.chunk_size] 
                      for i in range(0, len(sorted_dates), self.chunk_size)]
        
        print(f"\n⚡ 开始超高速并行处理:")
        print(f"   总交易日: {len(sorted_dates)} 个")
        print(f"   分块数量: {len(date_chunks)} 个")
        print(f"   并行线程: {self.max_workers} 个")
        
        # 准备参数
        args_list = [(chunk, factor_data, original_data, all_factors, original_factors, computed_factors) 
                     for chunk in date_chunks]
        
        # 并行处理
        all_results = {}
        with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            chunk_results = list(tqdm(executor.map(process_date_chunk_vectorized, args_list), 
                                    total=len(date_chunks), desc="🔥 超高速处理"))
        
        # 合并结果
        print("\n🔄 合并计算结果...")
        for chunk_result in chunk_results:
            all_results.update(chunk_result)
        
        # 清理内存
        del factor_data, original_data, chunk_results
        gc.collect()
        
        # 重组数据结构
        print("\n📦 重组数据结构...")
        stock_rankings = {}
        for file in factor_files:
            stock_code = file.replace('_factors.pkl', '')
            stock_rankings[stock_code] = {f"{factor}_rank": {} for factor in all_factors}
        
        # 填充排序数据
        for date, date_rankings in tqdm(all_results.items(), desc="填充排序数据"):
            for factor, stock_percentiles in date_rankings.items():
                for stock_code, percentile in stock_percentiles.items():
                    if stock_code in stock_rankings:
                        stock_rankings[stock_code][f"{factor}_rank"][date] = percentile
        
        # 快速保存结果
        print("\n💾 快速保存结果...")
        self.save_results_fast(factor_files, stock_rankings, all_factors)
        
        print("\n" + "=" * 80)
        print(f"🎉 超高速计算完成！结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
    
    def save_results_fast(self, factor_files, stock_rankings, all_factors):
        """
        快速保存结果
        """
        def save_single_stock(args):
            file, stock_rankings, all_factors = args
            try:
                stock_code = file.replace('_factors.pkl', '')
                file_path = os.path.join(self.factor_cache_dir, file)
                
                # 加载原始数据
                with open(file_path, 'rb') as f:
                    df = pickle.load(f)
                
                # 添加排序数据
                ranking_columns = []
                for factor in all_factors:
                    rank_col = f"{factor}_rank"
                    if stock_code in stock_rankings and rank_col in stock_rankings[stock_code]:
                        rank_dict = stock_rankings[stock_code][rank_col]
                        
                        if rank_dict:
                            rank_series = pd.Series(rank_dict, name=rank_col)
                            rank_series.index.name = 'Date'
                            
                            if rank_col in df.columns:
                                df[rank_col] = df[rank_col].combine_first(rank_series)
                            else:
                                df = df.join(rank_series, how='left')
                            
                            ranking_columns.append(rank_col)
                
                # 保存更新的因子文件
                with open(file_path, 'wb') as f:
                    pickle.dump(df, f)
                
                # 保存排序因子文件
                if ranking_columns:
                    ranking_data = df[ranking_columns].copy()
                    ranking_data.reset_index(inplace=True)
                    ranking_data.rename(columns={'Date': 'date'}, inplace=True)
                    
                    ranking_path = os.path.join(self.ranking_factor_dir, f"{stock_code}_ranking_factors.pkl")
                    with open(ranking_path, 'wb') as f:
                        pickle.dump(ranking_data, f)
                
                return True
            except Exception as e:
                print(f"保存 {stock_code} 失败: {e}")
                return False
        
        # 并行保存
        args_list = [(file, stock_rankings, all_factors) for file in factor_files]
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            results = list(tqdm(executor.map(save_single_stock, args_list), 
                              total=len(factor_files), desc="保存文件"))
        
        success_count = sum(results)
        print(f"✅ 成功保存 {success_count}/{len(factor_files)} 个文件")

def calculate_factor_percentiles_ultra_fast(custom_factors=None, include_original=True, include_computed=True,
                                           max_workers=None, chunk_size=1000, memory_limit_gb=8):
    """
    超高速排序因子计算入口函数
    
    Args:
        custom_factors: 自定义因子列表
        include_original: 是否包含原始数据因子
        include_computed: 是否包含计算因子
        max_workers: 最大工作线程数
        chunk_size: 数据块大小
        memory_limit_gb: 内存限制(GB)
    """
    calculator = UltraFastRankingCalculator(
        max_workers=max_workers,
        chunk_size=chunk_size,
        memory_limit_gb=memory_limit_gb
    )
    
    calculator.calculate_rankings_ultra_fast(
        custom_factors=custom_factors,
        include_original=include_original,
        include_computed=include_computed
    )

if __name__ == "__main__":
    print("🚀 超高速排序因子计算器")
    print("=" * 50)
    print("选择运行模式:")
    print("1. 🔥 超高速模式（推荐）")
    print("2. 🧪 测试模式（单因子）")
    print("3. ⚙️  自定义配置")
    
    choice = input("请选择模式 (1/2/3) [默认: 1]: ").strip()
    
    if choice == '2':
        # 测试模式
        print("\n🧪 测试模式 - 只处理 turn 因子")
        calculate_factor_percentiles_ultra_fast(custom_factors=['turn'])
    
    elif choice == '3':
        # 自定义配置
        print("\n⚙️ 自定义配置模式")
        max_workers = int(input(f"最大工作线程数 [默认: {mp.cpu_count()}]: ") or mp.cpu_count())
        chunk_size = int(input("数据块大小 [默认: 1000]: ") or 1000)
        
        calculate_factor_percentiles_ultra_fast(
            max_workers=max_workers,
            chunk_size=chunk_size
        )
    
    else:
        # 默认超高速模式
        print("\n🔥 超高速模式启动")
        calculate_factor_percentiles_ultra_fast()