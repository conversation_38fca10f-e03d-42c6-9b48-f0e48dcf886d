#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排序因子生成配置文件
"""

import os

# 项目根目录 - 指向RLStrategy目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 数据目录配置
DATA_CONFIG = {
    'project_root': PROJECT_ROOT,
    'stock_data_dir': os.path.join(PROJECT_ROOT, '0-数据汇总', '1-原始股票数据'),
    'factor_cache_dir': os.path.join(PROJECT_ROOT, '0-数据汇总', '2-个股因子数据'),
    'ranking_factor_dir': os.path.join(PROJECT_ROOT, '0-数据汇总', '3-排序因子数据'),
    'log_dir': os.path.join(PROJECT_ROOT, '0-数据汇总', '5-日志文件')
}

# 确保目录存在
for dir_path in DATA_CONFIG.values():
    if not os.path.exists(dir_path):
        os.makedirs(dir_path, exist_ok=True)
        print(f"创建目录: {dir_path}")

print(f"配置加载完成:")
print(f"  项目根目录: {PROJECT_ROOT}")
print(f"  股票数据目录: {DATA_CONFIG['stock_data_dir']}")
print(f"  因子缓存目录: {DATA_CONFIG['factor_cache_dir']}")
print(f"  排序因子目录: {DATA_CONFIG['ranking_factor_dir']}")
print(f"  日志目录: {DATA_CONFIG['log_dir']}")