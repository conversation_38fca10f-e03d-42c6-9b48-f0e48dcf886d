#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全版本性能基准测试
对比原始版本、多线程版本和超高速版本的性能
"""

import time
import psutil
import os
from datetime import datetime
import gc

# 导入各个版本的计算函数
from calculate_factor_percentiles_optimized import calculate_factor_percentiles_optimized
from calculate_factor_percentiles_multithreaded import calculate_factor_percentiles_multithreaded
from calculate_factor_percentiles_ultra_fast import calculate_factor_percentiles_ultra_fast

def get_memory_usage():
    """获取当前内存使用情况"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024  # MB

def benchmark_version(version_name, calc_function, *args, **kwargs):
    """基准测试单个版本"""
    print(f"\n{'='*60}")
    print(f"🧪 测试版本: {version_name}")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}")
    
    # 记录开始状态
    start_time = time.time()
    start_memory = get_memory_usage()
    
    try:
        # 执行计算
        calc_function(*args, **kwargs)
        
        # 记录结束状态
        end_time = time.time()
        end_memory = get_memory_usage()
        
        # 计算性能指标
        duration = end_time - start_time
        memory_used = end_memory - start_memory
        
        print(f"\n✅ {version_name} 完成")
        print(f"⏱️  执行时间: {duration:.2f} 秒 ({duration/60:.2f} 分钟)")
        print(f"💾 内存使用: {memory_used:.2f} MB")
        print(f"🏁 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return {
            'version': version_name,
            'duration': duration,
            'memory_used': memory_used,
            'success': True
        }
        
    except Exception as e:
        print(f"❌ {version_name} 执行失败: {e}")
        return {
            'version': version_name,
            'duration': 0,
            'memory_used': 0,
            'success': False,
            'error': str(e)
        }
    finally:
        # 清理内存
        gc.collect()

def run_comprehensive_benchmark():
    """运行全面的性能基准测试"""
    print("🚀 全版本性能基准测试")
    print("=" * 80)
    print(f"测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"系统信息: CPU核心数={psutil.cpu_count()}, 内存={psutil.virtual_memory().total/1024/1024/1024:.1f}GB")
    print("=" * 80)
    
    # 测试配置
    test_factors = ['turn']  # 使用单个因子进行快速测试
    
    results = []
    
    # 测试1: 原始优化版本
    print("\n🔄 准备测试原始优化版本...")
    result1 = benchmark_version(
        "原始优化版本",
        calculate_factor_percentiles_optimized,
        custom_factors=test_factors
    )
    results.append(result1)
    
    # 等待一段时间让系统稳定
    print("\n⏳ 等待系统稳定...")
    time.sleep(5)
    
    # 测试2: 多线程版本
    print("\n🔄 准备测试多线程版本...")
    result2 = benchmark_version(
        "多线程版本",
        calculate_factor_percentiles_multithreaded,
        custom_factors=test_factors,
        max_workers=8
    )
    results.append(result2)
    
    # 等待一段时间让系统稳定
    print("\n⏳ 等待系统稳定...")
    time.sleep(5)
    
    # 测试3: 超高速版本
    print("\n🔄 准备测试超高速版本...")
    result3 = benchmark_version(
        "超高速版本",
        calculate_factor_percentiles_ultra_fast,
        custom_factors=test_factors,
        max_workers=8,
        chunk_size=500
    )
    results.append(result3)
    
    # 生成性能报告
    generate_performance_report(results)

def run_quick_benchmark():
    """快速基准测试 - 只测试多线程和超高速版本"""
    print("⚡ 快速性能基准测试")
    print("=" * 60)
    print(f"测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    test_factors = ['turn']
    results = []
    
    # 测试多线程版本
    print("\n🔄 测试多线程版本...")
    result1 = benchmark_version(
        "多线程版本",
        calculate_factor_percentiles_multithreaded,
        custom_factors=test_factors,
        max_workers=8
    )
    results.append(result1)
    
    time.sleep(3)
    
    # 测试超高速版本
    print("\n🔄 测试超高速版本...")
    result2 = benchmark_version(
        "超高速版本",
        calculate_factor_percentiles_ultra_fast,
        custom_factors=test_factors,
        max_workers=8,
        chunk_size=500
    )
    results.append(result2)
    
    # 生成性能报告
    generate_performance_report(results)

def generate_performance_report(results):
    """生成性能报告"""
    print("\n" + "=" * 80)
    print("📊 性能测试报告")
    print("=" * 80)
    
    # 过滤成功的结果
    successful_results = [r for r in results if r['success']]
    
    if not successful_results:
        print("❌ 没有成功的测试结果")
        return
    
    # 排序结果（按执行时间）
    successful_results.sort(key=lambda x: x['duration'])
    
    print(f"{'版本':<15} {'执行时间(秒)':<12} {'执行时间(分钟)':<12} {'内存使用(MB)':<12} {'性能提升':<10}")
    print("-" * 80)
    
    baseline_time = successful_results[-1]['duration']  # 最慢的作为基准
    
    for i, result in enumerate(successful_results):
        duration = result['duration']
        duration_min = duration / 60
        memory = result['memory_used']
        speedup = baseline_time / duration if duration > 0 else 0
        
        rank_emoji = ["🥇", "🥈", "🥉"][i] if i < 3 else "📊"
        
        print(f"{rank_emoji} {result['version']:<12} {duration:<12.2f} {duration_min:<12.2f} {memory:<12.2f} {speedup:<10.2f}x")
    
    # 显示失败的结果
    failed_results = [r for r in results if not r['success']]
    if failed_results:
        print("\n❌ 失败的测试:")
        for result in failed_results:
            print(f"   {result['version']}: {result.get('error', '未知错误')}")
    
    # 总结
    if len(successful_results) >= 2:
        fastest = successful_results[0]
        slowest = successful_results[-1]
        improvement = slowest['duration'] / fastest['duration']
        
        print(f"\n🎯 关键指标:")
        print(f"   最快版本: {fastest['version']} ({fastest['duration']:.2f}秒)")
        print(f"   最慢版本: {slowest['version']} ({slowest['duration']:.2f}秒)")
        print(f"   性能提升: {improvement:.2f}x")
        print(f"   时间节省: {slowest['duration'] - fastest['duration']:.2f}秒")
    
    print("\n" + "=" * 80)
    print(f"📋 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

def run_scalability_test():
    """可扩展性测试 - 测试不同工作线程数的性能"""
    print("📈 可扩展性测试")
    print("=" * 60)
    
    test_factors = ['turn']
    worker_counts = [1, 2, 4, 8, 12, 16]
    results = []
    
    for workers in worker_counts:
        print(f"\n🔄 测试 {workers} 个工作线程...")
        result = benchmark_version(
            f"超高速版本({workers}线程)",
            calculate_factor_percentiles_ultra_fast,
            custom_factors=test_factors,
            max_workers=workers,
            chunk_size=500
        )
        results.append(result)
        time.sleep(2)
    
    # 生成可扩展性报告
    print("\n" + "=" * 60)
    print("📊 可扩展性测试报告")
    print("=" * 60)
    
    print(f"{'工作线程数':<10} {'执行时间(秒)':<12} {'性能提升':<10} {'效率':<10}")
    print("-" * 50)
    
    baseline = None
    for result in results:
        if result['success']:
            workers = int(result['version'].split('(')[1].split('线程')[0])
            duration = result['duration']
            
            if baseline is None:
                baseline = duration
                speedup = 1.0
                efficiency = 1.0
            else:
                speedup = baseline / duration
                efficiency = speedup / workers
            
            print(f"{workers:<10} {duration:<12.2f} {speedup:<10.2f}x {efficiency:<10.2f}")

if __name__ == "__main__":
    print("🧪 性能基准测试工具")
    print("=" * 50)
    print("选择测试模式:")
    print("1. ⚡ 快速测试（推荐）")
    print("2. 🔬 全面测试")
    print("3. 📈 可扩展性测试")
    
    choice = input("请选择测试模式 (1/2/3) [默认: 1]: ").strip()
    
    if choice == '2':
        run_comprehensive_benchmark()
    elif choice == '3':
        run_scalability_test()
    else:
        run_quick_benchmark()