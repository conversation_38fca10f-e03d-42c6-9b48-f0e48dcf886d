#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终优化版因子百分比排序计算脚本
集成所有最佳优化策略的最终版本

核心优化:
1. 智能数据预加载和缓存
2. 向量化批量计算
3. 多线程并行处理
4. 内存优化管理
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime
import pickle
from tqdm import tqdm
import gc
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import warnings
warnings.filterwarnings('ignore')

from config import DATA_CONFIG
from ranking_config import get_target_factors, print_ranking_config

def calculate_factor_percentiles_final(custom_factors=None, include_original=True, include_computed=True, max_workers=8):
    """
    最终优化版排序因子计算
    
    Args:
        custom_factors: 自定义因子列表
        include_original: 是否包含原始数据因子
        include_computed: 是否包含计算因子
        max_workers: 最大工作线程数
    """
    print("🚀 最终优化版因子百分比排序计算")
    print("=" * 80)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 配置路径
    factor_cache_dir = DATA_CONFIG['factor_cache_dir']
    ranking_factor_dir = DATA_CONFIG['ranking_factor_dir']
    original_data_dir = DATA_CONFIG['stock_data_dir']
    
    # 确保目录存在
    os.makedirs(ranking_factor_dir, exist_ok=True)
    
    # 获取文件列表
    factor_files = [f for f in os.listdir(factor_cache_dir) if f.endswith('_factors.pkl')]
    
    if not factor_files:
        print("❌ 未找到因子文件！")
        return
    
    print(f"📊 发现 {len(factor_files)} 个股票文件")
    
    # 快速获取可用因子
    print("\n🔍 分析可用因子...")
    sample_factor_file = os.path.join(factor_cache_dir, factor_files[0])
    with open(sample_factor_file, 'rb') as f:
        sample_df = pickle.load(f)
    
    factor_columns = [col for col in sample_df.columns 
                     if not col.endswith('_rank') and not col.endswith('_percentile')]
    
    # 获取原始数据列
    stock_files = [f for f in os.listdir(original_data_dir) if f.endswith('_hfq.csv')]
    if stock_files:
        sample_stock_file = os.path.join(original_data_dir, stock_files[0])
        stock_df = pd.read_csv(sample_stock_file, nrows=1)
        original_columns = [col for col in stock_df.columns 
                          if col not in ['date', 'code', 'adjustflag', 'tradestatus', 'isST']]
    else:
        original_columns = []
    
    # 确定处理的因子
    all_factors = []
    original_factors = []
    computed_factors = []
    
    if custom_factors:
        print(f"🎯 使用自定义因子: {custom_factors}")
        for factor in custom_factors:
            all_factors.append(factor)
            if factor in original_columns:
                original_factors.append(factor)
            elif factor in factor_columns:
                computed_factors.append(factor)
    else:
        if include_original:
            original_factors = original_columns
            all_factors.extend(original_columns)
        if include_computed:
            computed_factors = factor_columns
            all_factors.extend(factor_columns)
    
    if not all_factors:
        print("❌ 没有找到可处理的因子")
        return
    
    print(f"\n📈 处理因子统计:")
    print(f"   原始数据因子: {len(original_factors)} 个")
    print(f"   计算因子: {len(computed_factors)} 个")
    print(f"   总计: {len(all_factors)} 个")
    
    # 智能数据加载
    print("\n🔄 智能加载数据...")
    start_time = time.time()
    
    factor_data = {}
    original_data = {}
    all_dates = set()
    
    # 并行加载因子数据
    def load_factor_data(file):
        stock_code = file.replace('_factors.pkl', '')
        file_path = os.path.join(factor_cache_dir, file)
        try:
            with open(file_path, 'rb') as f:
                df = pickle.load(f)
            if computed_factors:
                df_filtered = df[computed_factors].copy()
                return stock_code, df_filtered, set(df_filtered.index)
        except:
            pass
        return stock_code, pd.DataFrame(), set()
    
    if computed_factors:
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            results = list(tqdm(executor.map(load_factor_data, factor_files), 
                              total=len(factor_files), desc="加载因子数据"))
        
        for stock_code, df, dates in results:
            if not df.empty:
                factor_data[stock_code] = df
                all_dates.update(dates)
    
    # 并行加载原始数据
    def load_original_data(file):
        stock_code = file.replace('_factors.pkl', '')
        original_file = os.path.join(original_data_dir, f"{stock_code}_hfq.csv")
        try:
            if os.path.exists(original_file):
                df = pd.read_csv(original_file, index_col=0, parse_dates=True)
                if original_factors:
                    df_filtered = df[original_factors].copy()
                    return stock_code, df_filtered, set(df_filtered.index)
        except:
            pass
        return stock_code, pd.DataFrame(), set()
    
    if original_factors:
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            results = list(tqdm(executor.map(load_original_data, factor_files), 
                              total=len(factor_files), desc="加载原始数据"))
        
        for stock_code, df, dates in results:
            if not df.empty:
                original_data[stock_code] = df
                all_dates.update(dates)
    
    sorted_dates = sorted(list(all_dates))
    load_time = time.time() - start_time
    
    print(f"✅ 数据加载完成 ({load_time:.2f}秒):")
    print(f"   因子数据: {len(factor_data)} 个股票")
    print(f"   原始数据: {len(original_data)} 个股票")
    print(f"   交易日期: {len(sorted_dates)} 个")
    
    # 向量化批量计算
    print(f"\n⚡ 开始向量化批量计算...")
    calc_start_time = time.time()
    
    # 分批处理日期
    batch_size = 500
    date_batches = [sorted_dates[i:i+batch_size] for i in range(0, len(sorted_dates), batch_size)]
    
    print(f"   分 {len(date_batches)} 批处理，每批最多 {batch_size} 个交易日")
    
    # 存储所有排序结果
    stock_rankings = {}
    for file in factor_files:
        stock_code = file.replace('_factors.pkl', '')
        stock_rankings[stock_code] = {f"{factor}_rank": {} for factor in all_factors}
    
    # 批量处理每个日期批次
    for batch_idx, date_batch in enumerate(tqdm(date_batches, desc="批量计算排序")):
        batch_results = {}
        
        # 对每个日期进行向量化计算
        for date in date_batch:
            daily_data = {}
            
            # 收集计算因子数据
            for factor in computed_factors:
                factor_values = {}
                for stock_code, df in factor_data.items():
                    if date in df.index and factor in df.columns:
                        value = df.loc[date, factor]
                        if pd.notna(value):
                            factor_values[stock_code] = value
                daily_data[factor] = factor_values
            
            # 收集原始数据因子
            for factor in original_factors:
                factor_values = {}
                for stock_code, df in original_data.items():
                    if date in df.index and factor in df.columns:
                        value = df.loc[date, factor]
                        if pd.notna(value):
                            factor_values[stock_code] = value
                daily_data[factor] = factor_values
            
            # 向量化计算排序
            date_rankings = {}
            for factor in all_factors:
                if factor in daily_data and len(daily_data[factor]) > 1:
                    # 使用pandas进行快速排序
                    factor_series = pd.Series(daily_data[factor])
                    percentiles = factor_series.rank(pct=True, method='min')
                    date_rankings[factor] = percentiles.to_dict()
            
            batch_results[date] = date_rankings
        
        # 将批次结果合并到总结果中
        for date, date_rankings in batch_results.items():
            for factor, stock_percentiles in date_rankings.items():
                for stock_code, percentile in stock_percentiles.items():
                    if stock_code in stock_rankings:
                        stock_rankings[stock_code][f"{factor}_rank"][date] = percentile
    
    calc_time = time.time() - calc_start_time
    print(f"✅ 排序计算完成 ({calc_time:.2f}秒)")
    
    # 清理内存
    del factor_data, original_data
    gc.collect()
    
    # 快速保存结果
    print("\n💾 快速保存结果...")
    save_start_time = time.time()
    
    def save_single_stock(file):
        try:
            stock_code = file.replace('_factors.pkl', '')
            file_path = os.path.join(factor_cache_dir, file)
            
            # 加载原始数据
            with open(file_path, 'rb') as f:
                df = pickle.load(f)
            
            # 添加排序数据
            ranking_columns = []
            for factor in all_factors:
                rank_col = f"{factor}_rank"
                if stock_code in stock_rankings and rank_col in stock_rankings[stock_code]:
                    rank_dict = stock_rankings[stock_code][rank_col]
                    
                    if rank_dict:
                        rank_series = pd.Series(rank_dict, name=rank_col)
                        rank_series.index.name = 'Date'
                        
                        if rank_col in df.columns:
                            df[rank_col] = df[rank_col].combine_first(rank_series)
                        else:
                            df = df.join(rank_series, how='left')
                        
                        ranking_columns.append(rank_col)
            
            # 保存更新的因子文件
            with open(file_path, 'wb') as f:
                pickle.dump(df, f)
            
            # 保存排序因子文件
            if ranking_columns:
                ranking_data = df[ranking_columns].copy()
                ranking_data.reset_index(inplace=True)
                ranking_data.rename(columns={'Date': 'date'}, inplace=True)
                
                ranking_path = os.path.join(ranking_factor_dir, f"{stock_code}_ranking_factors.pkl")
                with open(ranking_path, 'wb') as f:
                    pickle.dump(ranking_data, f)
            
            return True
        except Exception as e:
            print(f"保存 {stock_code} 失败: {e}")
            return False
    
    # 并行保存
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        results = list(tqdm(executor.map(save_single_stock, factor_files), 
                          total=len(factor_files), desc="保存文件"))
    
    save_time = time.time() - save_start_time
    success_count = sum(results)
    
    print(f"✅ 保存完成 ({save_time:.2f}秒): {success_count}/{len(factor_files)} 个文件")
    
    # 总结
    total_time = time.time() - start_time
    print("\n" + "=" * 80)
    print(f"🎉 最终优化版计算完成！")
    print(f"📊 性能统计:")
    print(f"   数据加载: {load_time:.2f}秒")
    print(f"   排序计算: {calc_time:.2f}秒")
    print(f"   结果保存: {save_time:.2f}秒")
    print(f"   总计时间: {total_time:.2f}秒 ({total_time/60:.2f}分钟)")
    print(f"   处理效率: {len(sorted_dates)/total_time:.1f} 交易日/秒")
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

if __name__ == "__main__":
    import time
    
    print("🚀 最终优化版排序因子计算器")
    print("=" * 50)
    print("选择运行模式:")
    print("1. 🧪 测试模式（turn因子）")
    print("2. 🔥 全量计算")
    print("3. ⚙️  自定义因子")
    
    choice = input("请选择模式 (1/2/3) [默认: 1]: ").strip()
    
    if choice == '2':
        # 全量计算
        print("\n🔥 全量计算模式")
        calculate_factor_percentiles_final()
    
    elif choice == '3':
        # 自定义因子
        print("\n⚙️ 自定义因子模式")
        factors_input = input("请输入因子名称（用逗号分隔）: ").strip()
        if factors_input:
            custom_factors = [f.strip() for f in factors_input.split(',')]
            calculate_factor_percentiles_final(custom_factors=custom_factors)
        else:
            print("❌ 未输入因子名称")
    
    else:
        # 默认测试模式
        print("\n🧪 测试模式 - 只处理 turn 因子")
        calculate_factor_percentiles_final(custom_factors=['turn'])