import pickle
import pandas as pd
import os

# 检查多个pkl文件内容
pkl_files = [
    r"d:\开发项目\Stock Models\RLStrategy_V2.1（SAC算法）\0-数据汇总\2-因子数据\factors\sh.600000_factors.pkl",
    r"d:\开发项目\Stock Models\RLStrategy_V2.1（SAC算法）\0-数据汇总\2-因子数据\factors\sh.600004_factors.pkl",
    r"d:\开发项目\Stock Models\RLStrategy_V2.1（SAC算法）\0-数据汇总\2-因子数据\factors\sh.600018_factors.pkl"
]

for i, pkl_file in enumerate(pkl_files):
    print(f"\n=== 检查文件 {i+1}: {pkl_file.split('/')[-1]} ===")
    
    if os.path.exists(pkl_file):
        with open(pkl_file, 'rb') as f:
            data = pickle.load(f)
        
        print(f"数据类型: {type(data)}")
        print(f"数据形状: {data.shape}")
        print(f"列名: {list(data.columns)}")
        print(f"索引类型: {type(data.index)}")
        print(f"前3行数据:")
        print(data.head(3))
    else:
        print(f"文件不存在: {pkl_file}")