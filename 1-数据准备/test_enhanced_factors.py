#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强因子生成功能
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime

# 添加路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '2-个股因子生成'))

# 导入配置
import importlib.util
config_spec = importlib.util.spec_from_file_location("config", os.path.join(project_root, "2-训练与回测", "config.py"))
config_module = importlib.util.module_from_spec(config_spec)
config_spec.loader.exec_module(config_module)
DATA_CONFIG = config_module.DATA_CONFIG

from indicators import FactorCalcula<PERSON>

def test_fundamental_factors():
    """测试基本面因子计算"""
    print("🧪 测试基本面因子计算...")
    
    # 读取样本股票数据
    stock_data_dir = DATA_CONFIG['stock_data_dir']
    sample_file = None
    
    # 找到第一个股票文件
    for filename in os.listdir(stock_data_dir):
        if filename.endswith('_hfq.csv'):
            sample_file = os.path.join(stock_data_dir, filename)
            stock_code = filename.replace('_hfq.csv', '')
            break
    
    if not sample_file:
        print("❌ 未找到股票数据文件")
        return False
    
    print(f"使用样本股票: {stock_code}")
    
    try:
        # 读取数据
        df = pd.read_csv(sample_file)
        print(f"数据形状: {df.shape}")
        print(f"可用列: {list(df.columns)}")
        
        # 检查必需的列
        required_cols = ['date', 'open', 'high', 'low', 'close', 'volume', 'turn', 'peTTM', 'psTTM', 'pcfNcfTTM', 'pbMRQ']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"❌ 缺少必需的列: {missing_cols}")
            return False
        
        # 数据预处理
        df['date'] = pd.to_datetime(df['date'])
        df.set_index('date', inplace=True)
        df.sort_index(inplace=True)
        
        # 重命名列以匹配计算器期望的格式
        df = df.rename(columns={
            'open': 'Open',
            'high': 'High', 
            'low': 'Low',
            'close': 'Close',
            'volume': 'Volume'
        })
        
        # 取最近1年的数据进行测试
        recent_data = df.tail(252)  # 约1年交易日
        print(f"测试数据范围: {recent_data.index[0]} 到 {recent_data.index[-1]}")
        
        # 创建因子计算器
        calculator = FactorCalculator()
        
        # 测试基本面因子
        fundamental_factors = ['turn', 'peTTM', 'psTTM', 'pcfNcfTTM', 'pbMRQ']
        
        print(f"\n📊 计算基本面因子...")
        for factor in fundamental_factors:
            try:
                result = calculator.calculate_single_indicator(recent_data, factor)
                
                if result is not None:
                    valid_count = result.notna().sum()
                    print(f"  ✅ {factor}: {valid_count}/{len(result)} 个有效值")
                    print(f"     范围: {result.min():.4f} - {result.max():.4f}")
                    print(f"     均值: {result.mean():.4f}")
                else:
                    print(f"  ❌ {factor}: 计算失败")
                    
            except Exception as e:
                print(f"  ❌ {factor}: 错误 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本面因子测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_technical_factors():
    """测试技术指标因子计算"""
    print("\n🧪 测试技术指标因子计算...")
    
    # 读取样本股票数据
    stock_data_dir = DATA_CONFIG['stock_data_dir']
    sample_file = None
    
    # 找到第一个股票文件
    for filename in os.listdir(stock_data_dir):
        if filename.endswith('_hfq.csv'):
            sample_file = os.path.join(stock_data_dir, filename)
            stock_code = filename.replace('_hfq.csv', '')
            break
    
    try:
        # 读取数据
        df = pd.read_csv(sample_file)
        
        # 数据预处理
        df['date'] = pd.to_datetime(df['date'])
        df.set_index('date', inplace=True)
        df.sort_index(inplace=True)
        
        # 重命名列
        df = df.rename(columns={
            'open': 'Open',
            'high': 'High', 
            'low': 'Low',
            'close': 'Close',
            'volume': 'Volume'
        })
        
        # 取最近1年的数据
        recent_data = df.tail(252)
        
        # 创建因子计算器
        calculator = FactorCalculator()
        
        # 测试技术指标
        technical_factors = ['RSI', 'MACD', 'KDJ', 'BOLL', 'CCI', 'WR', 'ATR', 'OBV', 'VWAP', 'DMI']
        
        print(f"📊 计算技术指标...")
        for factor in technical_factors:
            try:
                result = calculator.calculate_single_indicator(recent_data, factor)
                
                if result is not None:
                    if isinstance(result, dict):
                        # 多值指标（如MACD、KDJ等）
                        print(f"  ✅ {factor}: 多值指标")
                        for key, series in result.items():
                            valid_count = series.notna().sum()
                            print(f"     {key}: {valid_count}/{len(series)} 个有效值")
                    else:
                        # 单值指标
                        valid_count = result.notna().sum()
                        print(f"  ✅ {factor}: {valid_count}/{len(result)} 个有效值")
                        if valid_count > 0:
                            print(f"     范围: {result.min():.4f} - {result.max():.4f}")
                else:
                    print(f"  ❌ {factor}: 计算失败")
                    
            except Exception as e:
                print(f"  ❌ {factor}: 错误 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 技术指标测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_calculation():
    """测试批量因子计算"""
    print("\n🧪 测试批量因子计算...")
    
    try:
        # 导入预计算管理器
        from indicators import PrecomputeManager
        
        # 获取少量股票进行测试
        stock_data_dir = DATA_CONFIG['stock_data_dir']
        test_stocks = []
        
        for filename in os.listdir(stock_data_dir):
            if filename.endswith('_hfq.csv') and len(test_stocks) < 3:
                stock_code = filename.replace('_hfq.csv', '')
                test_stocks.append(stock_code)
        
        if not test_stocks:
            print("❌ 未找到测试股票")
            return False
        
        print(f"测试股票: {test_stocks}")
        
        # 创建预计算管理器
        manager = PrecomputeManager()
        
        # 定义测试因子（选择几个代表性的）
        test_factors = ['RSI', 'turn', 'peTTM', 'turnover_std20']
        
        print(f"测试因子: {test_factors}")
        
        # 批量计算
        results = manager.precompute_batch_factors(
            stock_list=test_stocks,
            indicators=test_factors,
            force_update=True
        )
        
        # 检查结果
        success_count = sum(results.values())
        print(f"✅ 批量计算完成: {success_count}/{len(test_stocks)} 只股票成功")
        
        # 验证生成的文件
        factor_cache_dir = DATA_CONFIG['factor_cache_dir']
    factors_dir = factor_cache_dir
        
        for stock_code in test_stocks:
            factor_file = os.path.join(factors_dir, f"{stock_code}_factors.pkl")
            if os.path.exists(factor_file):
                print(f"  ✅ {stock_code}: 因子文件已生成")
            else:
                print(f"  ❌ {stock_code}: 因子文件未生成")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 批量计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 增强因子生成功能测试")
    print("=" * 80)
    
    # 1. 测试基本面因子
    fundamental_success = test_fundamental_factors()
    
    # 2. 测试技术指标
    technical_success = test_technical_factors()
    
    # 3. 测试批量计算
    batch_success = test_batch_calculation()
    
    print("\n" + "=" * 80)
    print("📋 测试结果总结")
    print("=" * 80)
    
    if all([fundamental_success, technical_success, batch_success]):
        print("🎉 所有测试通过！增强因子功能正常")
        print("💡 现在可以生成包含17个因子的完整数据集:")
        print("   - 10个技术指标: RSI, MACD, KDJ, BOLL, CCI, WR, ATR, OBV, VWAP, DMI")
        print("   - 2个统计指标: turnover_std20, ReturnStd120") 
        print("   - 5个基本面指标: turn, peTTM, psTTM, pcfNcfTTM, pbMRQ")
    else:
        print("⚠️ 部分测试未通过:")
        if not fundamental_success:
            print("   - 基本面因子测试失败")
        if not technical_success:
            print("   - 技术指标测试失败")
        if not batch_success:
            print("   - 批量计算测试失败")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
