#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成增强因子数据的简化脚本
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
from datetime import datetime

# 添加路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)
sys.path.append(os.path.join(project_root, "2-训练与回测"))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "2-个股因子生成"))

# 导入配置
import importlib.util
config_spec = importlib.util.spec_from_file_location("config", os.path.join(project_root, "2-训练与回测", "config.py"))
config_module = importlib.util.module_from_spec(config_spec)
config_spec.loader.exec_module(config_module)
DATA_CONFIG = config_module.DATA_CONFIG
get_available_stocks = config_module.get_available_stocks
get_stock_file_path = config_module.get_stock_file_path

from indicators import FactorCalculator

def generate_factors_for_stock(stock_code: str, calculator: FactorCalculator) -> bool:
    """
    为单只股票生成所有因子
    
    Args:
        stock_code: 股票代码
        calculator: 因子计算器
        
    Returns:
        是否成功
    """
    try:
        print(f"开始处理股票: {stock_code}")
        
        # 获取股票数据文件
        file_path = get_stock_file_path(stock_code)
        if not os.path.exists(file_path):
            print(f"  ❌ 数据文件不存在: {file_path}")
            return False
        
        # 读取数据
        df = pd.read_csv(file_path)
        print(f"  数据形状: {df.shape}")
        
        # 数据预处理
        df['date'] = pd.to_datetime(df['date'])
        df.set_index('date', inplace=True)
        df.sort_index(inplace=True)
        
        # 重命名列以匹配计算器期望的格式
        df = df.rename(columns={
            'open': 'Open',
            'high': 'High', 
            'low': 'Low',
            'close': 'Close',
            'volume': 'Volume'
        })
        
        # 定义所有要计算的因子
        all_factors = [
            # 技术指标
            'RSI', 'MACD', 'KDJ', 'BOLL', 'CCI', 'WR', 'ATR', 'OBV', 'VWAP', 'DMI',
            # 统计指标
            'turnover_std20', 'ReturnStd120',
            # 基本面指标
            'turn', 'peTTM', 'psTTM', 'pcfNcfTTM', 'pbMRQ'
        ]
        
        # 计算所有因子
        factor_results = {}
        success_count = 0
        
        for factor_name in all_factors:
            try:
                result = calculator.calculate_single_indicator(df, factor_name)
                
                if result is not None:
                    if isinstance(result, dict):
                        # 多值指标（如MACD、KDJ等）
                        for key, series in result.items():
                            factor_results[f"{factor_name}_{key}"] = series
                    else:
                        # 单值指标
                        factor_results[factor_name] = result
                    
                    success_count += 1
                    print(f"  ✅ {factor_name}: 计算成功")
                else:
                    print(f"  ⚠️ {factor_name}: 计算结果为空")
                    
            except Exception as e:
                print(f"  ❌ {factor_name}: 计算失败 - {e}")
        
        if not factor_results:
            print(f"  ❌ 没有成功计算任何因子")
            return False
        
        # 合并所有因子到一个DataFrame
        factor_df = pd.DataFrame(factor_results)
        factor_df.index.name = 'date'
        
        print(f"  📊 因子数据形状: {factor_df.shape}")
        print(f"  📊 成功计算因子: {success_count}/{len(all_factors)}")
        
        # 保存因子数据
        output_dir = str(DATA_CONFIG['factor_cache_dir'])
        os.makedirs(output_dir, exist_ok=True)
        
        output_file = os.path.join(output_dir, f"{stock_code}_factors.pkl")
        
        with open(output_file, 'wb') as f:
            pickle.dump(factor_df, f)
        
        print(f"  ✅ 因子数据已保存: {output_file}")
        return True
        
    except Exception as e:
        print(f"  ❌ 处理股票 {stock_code} 失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 生成增强因子数据")
    print("=" * 80)
    
    # 获取可用股票
    try:
        available_stocks = get_available_stocks()
        print(f"发现 {len(available_stocks)} 只股票")
    except Exception as e:
        print(f"❌ 获取股票列表失败: {e}")
        return
    
    # 创建因子计算器
    try:
        calculator = FactorCalculator()
        print("✅ 因子计算器创建成功")
    except Exception as e:
        print(f"❌ 创建因子计算器失败: {e}")
        return
    
    # 处理股票（限制数量以便测试）
    test_stocks = available_stocks[:10]  # 只处理前10只股票进行测试
    print(f"测试处理 {len(test_stocks)} 只股票: {test_stocks}")
    
    success_count = 0
    total_count = len(test_stocks)
    
    for i, stock_code in enumerate(test_stocks):
        print(f"\n--- 进度: {i+1}/{total_count} ---")
        
        if generate_factors_for_stock(stock_code, calculator):
            success_count += 1
    
    print("\n" + "=" * 80)
    print("📋 处理结果总结")
    print("=" * 80)
    print(f"成功处理: {success_count}/{total_count} 只股票")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count > 0:
        print(f"✅ 因子数据已保存到: {DATA_CONFIG['factor_cache_dir']}")
        print("💡 下一步可以运行排序因子生成:")
        print("   python 3-排序因子生成/calculate_factor_percentiles_optimized.py")
    else:
        print("❌ 没有成功处理任何股票，请检查错误信息")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
