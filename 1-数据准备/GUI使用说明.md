# 股票数据准备GUI工具 v1.1 使用说明

## 1. 概述

本GUI工具 (v1.1) 是一个功能强大的股票数据处理平台，旨在为量化策略研究提供一站式的数据准备服务。它整合了从**股票清单获取**、**历史数据下载**到**个股因子预计算**和**排序因子生成**的全流程功能，并提供了便捷的**数据可视化查看**工具。

相比旧版，v1.1进行了全面的功能升级和界面重构，将复杂的操作流程整合到统一的图形化界面中，极大地提升了数据准备的效率和便捷性。

## 2. 如何启动

进入 `1-数据准备` 目录，通过以下任一方式启动GUI程序：

```bash
# 方式一：运行封装好的启动脚本
python run_gui.py

# 方式二：直接运行GUI主程序
python data_preparation_gui.py
```

## 3. 功能模块详解

GUI界面采用选项卡式设计，主要分为以下六大功能模块：

### 3.1. 数据下载

这是进行数据获取的核心模块，包含**股票清单管理**和**历史数据下载**两部分。

#### 股票清单管理
- **功能**: 获取三大主流指数（沪深300、上证50、中证500）的最新成分股列表。
- **操作**:
  1. 点击对应指数的 **“获取清单”** 按钮。
  2. 程序会自动从网络获取最新数据，并更新状态标签（如“已获取 (300只)”）。
  3. 点击 **“查看清单”** 可在新窗口中预览完整的成分股列表。
- **文件位置**: 获取的清单将保存在 `0-数据汇总/1-原始股票数据/` 目录下，如 `hs300_stocks.csv`。

#### 历史数据下载
- **功能**: 下载指定股票池在特定时间范围内的日线历史数据。
- **操作**:
  1. **设置参数**:
     - **开始/结束日期**: 格式为 `YYYY-MM-DD`。
     - **股票池**: 可选“沪深300”、“上证50”、“中证500”或“全市场”。
     - **下载模式**:
       - **断点续传模式**: 默认选项，会自动跳过已下载的文件，适合日常更新。
       - **全新下载模式**: 会删除已有进度并重新下载所有数据。
       - **测试模式**: 仅下载少量数据（前5只股票），用于快速验证流程。
  2. **控制下载**:
     - 点击 **“开始下载”** 启动任务。
     - 进度条会实时显示下载进度。
     - 点击 **“停止下载”** 可随时安全地中断任务。
     - 点击 **“清除进度”** 可删除断点续传的记录文件。
- **文件位置**: 下载的日线数据（后复权）将保存在 `0-数据汇总/1-原始股票数据/` 目录下，格式为 `股票代码_hfq.csv`。

### 3.2. 因子预计算

本模块用于批量计算和管理个股的技术分析因子，并将结果缓存为 `.pkl` 文件，以加速后续的模型训练和回测。

- **功能**: 对已下载的原始数据进行技术指标计算，支持多线程并行处理。
- **操作**:
  1. **配置设置**:
     - **数据/缓存目录**: 通常使用默认路径即可。
     - **并行线程数**: 根据您的CPU核心数量调整，建议不超过8。
     - **股票选择**:
       - **自动获取**: 默认选项，自动扫描数据目录中的所有股票。
       - **手动输入**: 可手动指定一个或多个股票代码（用逗号分隔）。
  2. **操作控制**:
     - **预计算所有因子**: 对指定的股票列表，从头计算所有技术因子。
     - **增量更新因子**: 仅对数据有变动或新增的股票进行计算，效率更高。
     - **强制重新计算**: 忽略现有缓存，对所有指定股票强制重新计算一遍。
     - **测试加载性能**: 随机加载几只股票的因子数据，测试读取速度。
     - **查看缓存信息**: 统计并显示当前缓存文件的数量、总大小和最新的文件列表。
     - **清理缓存**: 删除指定天数之前的旧缓存文件，以释放磁盘空间。
     - **性能基准测试**: 对比单个与批量加载因子的性能。
  3. **状态监控**:
     - 所有操作的详细日志和进度都会在下方的“状态信息”和“操作进度”区域显示。
     - 可随时 **“停止操作”**。

- **文件位置**: 计算好的个股因子数据保存在 `0-数据汇总/2-个股因子数据/` 目录下，格式为 `股票代码.pkl`。

### 3.3. 排序因子生成

本模块在个股因子数据的基础上，计算截面排序因子（如RSI的百分位排名），这是许多高级策略（如增强学习模型）所需的关键输入。

- **功能**: 计算指定因子的截面百分位数据。
- **操作**:
  1. **因子选择**:
     - **因子类型**: 可按“全部”、“基础”、“技术”快速筛选，或选择“自定义”。
     - **因子列表**: 通过中间的箭头按钮，从左侧“可选因子”中挑选因子到右侧“已选因子”列表。
  2. **控制**:
     - **检查数据**: 在生成前，检查所需的数据目录和文件是否齐全。
     - **生成排序因子**: 启动计算任务。此过程计算量较大，可能需要较长时间。
     - **验证结果**: （可选）运行验证脚本，检查生成结果的准确性。
  3. **状态监控**:
     - 日志区域会详细输出计算过程中的每一步信息。
     - 可随时 **“停止”** 操作。
     - 可通过 **“打开结果目录”** 直接访问生成的文件。

- **文件位置**: 生成的排序因子数据保存在 `0-数据汇总/3-排序因子数据/` 目录下，格式为 `股票代码_ranking_factors.pkl`。

### 3.4. 数据查看

提供一个交互式表格，用于查看和导出下载的**原始股票日线数据**。

- **操作**:
  1. **刷新列表**: 点击 **“刷新列表”** 加载所有已下载的股票。
  2. **选择股票**: 从下拉框中选择一只股票。
  3. **加载数据**: 表格会自动显示该股票的日线数据。
  4. **数据范围**: 可选择显示“最新50/100/200行”或“全部数据”。
  5. **导出数据**: 将当前表格中显示的数据导出为 `.csv` 文件。

### 3.5. 因子数据查看

提供一个交互式表格，用于查看和导出**个股因子数据** (`.pkl` 文件)。

- **操作**:
  1. **刷新列表**: 点击 **“刷新股票列表”** 加载所有已生成因子的股票。
  2. **选择股票**: 从下拉框中选择一只股票。
  3. **加载数据**: 点击 **“加载因子数据”**，表格会分页显示所有因子值。
  4. **分页浏览**: 使用“上一页”、“下一页”和页面大小选择器浏览数据。
  5. **导出数据**: 可选择导出“当前页”或“全部数据”为 `.csv` 文件。

### 3.6. 排序因子查看

提供一个交互式表格，用于查看和导出**排序因子数据** (`.pkl` 文件)。

- **操作**:
  1. **刷新列表**: 点击 **“刷新股票列表”** 加载所有已生成排序因子的股票。
  2. **选择股票**: 从下拉框中选择一只股票。
  3. **加载数据**: 点击 **“加载排序因子数据”**，表格会分页显示所有排序因子值。
  4. **分页浏览**: 使用“上一页”、“下一页”浏览数据。
  5. **导出数据**: 可选择导出“当前页”或“全部数据”为 `.csv` 文件。

## 4. 通用功能

- **操作日志**: GUI主窗口底部的日志区域会记录所有模块的关键操作信息，便于追踪和调试。
- **日志管理**: 所有日志区域都支持 **“清空日志”** 和 **“保存日志”** 功能。

## 5. 注意事项

- **环境依赖**: 启动前请确保已根据 `INSTALL.md` 文件正确安装所有必需的Python库。
- **网络连接**: “数据下载”功能需要稳定的网络连接以访问数据源。
- **计算资源**: “因子预计算”和“排序因子生成”是计算密集型任务，请耐心等待，尤其是在处理大量股票时。
- **文件路径**: 请尽量保持项目默认的目录结构，以避免因找不到文件而导致的错误。

---
*版本: v1.1*
*更新日期: 2028-08-10*
