#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据目录结构初始化脚本

功能：
1. 创建必要的数据目录结构
2. 生成示例配置文件
3. 创建测试数据
"""

import os
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_directory_structure():
    """
    创建数据目录结构
    """
    base_dir = Path(__file__).parent.parent
    
    # 定义目录结构
    directories = [
        "0-数据汇总/1-原始股票数据",
        "0-数据汇总/2-个股因子数据", 
        "0-数据汇总/3-排序因子数据",
        "0-数据汇总/4-模型训练数据",
        "1-数据准备/1-最新数据下载/logs",
        "1-数据准备/2-个股因子生成/logs",
        "1-数据准备/3-排序因子生成/logs",
        "2-模型训练/1-数据预处理",
        "2-模型训练/2-模型构建",
        "2-模型训练/3-模型训练",
        "2-模型训练/4-模型评估",
        "3-策略回测/1-回测引擎",
        "3-策略回测/2-回测结果",
        "4-实盘交易/1-交易接口",
        "4-实盘交易/2-交易记录"
    ]
    
    print("创建目录结构...")
    
    for dir_path in directories:
        full_path = base_dir / dir_path
        full_path.mkdir(parents=True, exist_ok=True)
        print(f"✓ 创建目录: {dir_path}")
        
    print(f"\n目录结构创建完成！")
    return base_dir

def create_sample_stock_data(base_dir):
    """
    创建示例股票数据
    """
    print("\n创建示例股票数据...")
    
    data_dir = base_dir / "0-数据汇总" / "1-原始股票数据"
    
    # 示例股票代码
    sample_stocks = ['000001', '000002', '600000', '600036', '000858']
    
    # 生成日期范围
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365)
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')
    
    # 过滤工作日（简化处理）
    trading_days = [d for d in date_range if d.weekday() < 5]
    
    for stock_code in sample_stocks:
        print(f"  生成 {stock_code} 数据...")
        
        # 生成随机股价数据
        np.random.seed(int(stock_code))  # 使用股票代码作为随机种子
        
        n_days = len(trading_days)
        base_price = np.random.uniform(10, 100)  # 基础价格
        
        # 生成价格序列（随机游走）
        returns = np.random.normal(0.001, 0.02, n_days)  # 日收益率
        prices = [base_price]
        
        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(max(new_price, 0.1))  # 确保价格为正
            
        prices = np.array(prices)
        
        # 生成其他数据
        highs = prices * np.random.uniform(1.0, 1.05, n_days)
        lows = prices * np.random.uniform(0.95, 1.0, n_days)
        opens = prices * np.random.uniform(0.98, 1.02, n_days)
        
        # 确保价格关系合理
        for i in range(n_days):
            high = max(highs[i], opens[i], prices[i])
            low = min(lows[i], opens[i], prices[i])
            highs[i] = high
            lows[i] = low
            
        volumes = np.random.uniform(1000000, 10000000, n_days)  # 成交量
        amounts = prices * volumes  # 成交额
        
        # 创建DataFrame
        df = pd.DataFrame({
            '日期': [d.strftime('%Y-%m-%d') for d in trading_days],
            '开盘': np.round(opens, 2),
            '收盘': np.round(prices, 2),
            '最高': np.round(highs, 2),
            '最低': np.round(lows, 2),
            '成交量': volumes.astype(int),
            '成交额': np.round(amounts, 2),
            '振幅': np.round((highs - lows) / prices * 100, 2),
            '涨跌幅': np.round(np.concatenate([[0], np.diff(prices) / prices[:-1] * 100]), 2),
            '涨跌额': np.round(np.concatenate([[0], np.diff(prices)]), 2),
            '换手率': np.round(np.random.uniform(0.1, 5.0, n_days), 2)
        })
        
        # 保存数据
        output_file = data_dir / f"{stock_code}_hfq.csv"
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
    print(f"✓ 示例股票数据创建完成")

def create_stock_lists(base_dir):
    """
    创建股票池列表文件
    """
    print("\n创建股票池列表...")
    
    data_dir = base_dir / "0-数据汇总" / "1-原始股票数据"
    
    # 示例股票池
    stock_pools = {
        'sz50': ['000001', '000002', '000858', '002415', '002594'],
        'hs300': ['000001', '000002', '600000', '600036', '600519'],
        'zz500': ['000001', '000002', '000858', '002415', '002594']
    }
    
    for pool_name, codes in stock_pools.items():
        # 创建代码列表文件
        codes_file = data_dir / f"{pool_name}_codes.txt"
        with open(codes_file, 'w', encoding='utf-8') as f:
            for code in codes:
                f.write(f"{code}\n")
                
        # 创建详细信息文件
        stocks_data = []
        for i, code in enumerate(codes):
            stocks_data.append({
                '品种代码': code,
                '品种名称': f"股票{code}",
                '纳入日期': '2020-01-01',
                '权重': round(100/len(codes), 2)
            })
            
        df = pd.DataFrame(stocks_data)
        stocks_file = data_dir / f"{pool_name}_stocks.csv"
        df.to_csv(stocks_file, index=False, encoding='utf-8-sig')
        
        print(f"✓ 创建 {pool_name} 股票池，包含 {len(codes)} 只股票")

def create_config_files(base_dir):
    """
    创建配置文件
    """
    print("\n创建配置文件...")
    
    # 检查排序因子配置是否存在
    ranking_config_file = base_dir / "1-数据准备" / "3-排序因子生成" / "ranking_config.py"
    if not ranking_config_file.exists():
        print("  排序因子配置文件不存在，跳过")
    else:
        print("✓ 排序因子配置文件已存在")
        
    # 创建GUI配置文件
    gui_config_content = """
# GUI配置文件

# 数据路径配置
DATA_PATHS = {
    'raw_data': '0-数据汇总/1-原始股票数据',
    'factor_data': '0-数据汇总/2-个股因子数据',
    'ranking_data': '0-数据汇总/3-排序因子数据'
}

# 股票池配置
STOCK_POOLS = ['sz50', 'hs300', 'zz500']

# 下载配置
DOWNLOAD_CONFIG = {
    'max_retry': 3,
    'delay_between_requests': 0.1,
    'default_period': 365  # 默认下载天数
}

# GUI配置
GUI_CONFIG = {
    'window_size': '1200x800',
    'theme': 'default',
    'font_size': 10
}
"""
    
    gui_config_file = base_dir / "1-数据准备" / "gui_config.py"
    with open(gui_config_file, 'w', encoding='utf-8') as f:
        f.write(gui_config_content)
        
    print("✓ GUI配置文件创建完成")

def main():
    """
    主函数
    """
    print("="*50)
    print("数据目录结构初始化")
    print("="*50)
    
    try:
        # 创建目录结构
        base_dir = create_directory_structure()
        
        # 创建示例数据
        create_sample_stock_data(base_dir)
        
        # 创建股票池列表
        create_stock_lists(base_dir)
        
        # 创建配置文件
        create_config_files(base_dir)
        
        print("\n" + "="*50)
        print("初始化完成！")
        print("="*50)
        print("\n现在可以启动GUI程序进行测试：")
        print("python data_preparation_gui.py")
        print("\n或者运行测试脚本：")
        print("python test_gui.py")
        
    except Exception as e:
        print(f"\n初始化过程发生错误: {str(e)}")
        
if __name__ == "__main__":
    main()