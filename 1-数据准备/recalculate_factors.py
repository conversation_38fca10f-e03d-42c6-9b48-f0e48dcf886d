#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新计算个股因子
确保所有股票都有完整的13个因子
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
from datetime import datetime

# 添加路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)
sys.path.append(os.path.join(project_root, "2-训练与回测"))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "2-个股因子生成"))

# 导入配置
import importlib.util
config_spec = importlib.util.spec_from_file_location("config", os.path.join(project_root, "2-训练与回测", "config.py"))
config_module = importlib.util.module_from_spec(config_spec)
config_spec.loader.exec_module(config_module)
DATA_CONFIG = config_module.DATA_CONFIG
get_available_stocks = config_module.get_available_stocks
get_stock_file_path = config_module.get_stock_file_path

from indicators import FactorCalculator

def check_existing_factors():
    """检查现有因子数据"""
    print("🔍 检查现有因子数据...")
    
    factor_dir = str(DATA_CONFIG['factor_cache_dir'])
    
    if not os.path.exists(factor_dir):
        print(f"❌ 因子目录不存在: {factor_dir}")
        return {}
    
    factor_files = [f for f in os.listdir(factor_dir) if f.endswith('_factors.pkl')]
    
    factor_info = {}
    incomplete_stocks = []
    
    for filename in factor_files:
        stock_code = filename.replace('_factors.pkl', '')
        file_path = os.path.join(factor_dir, filename)
        
        try:
            with open(file_path, 'rb') as f:
                df = pickle.load(f)
            
            factor_count = len(df.columns)
            factor_info[stock_code] = {
                'factor_count': factor_count,
                'shape': df.shape,
                'factors': list(df.columns)
            }
            
            if factor_count < 13:
                incomplete_stocks.append(stock_code)
                print(f"  ⚠️ {stock_code}: 只有 {factor_count} 个因子")
            else:
                print(f"  ✅ {stock_code}: {factor_count} 个因子")
                
        except Exception as e:
            print(f"  ❌ {stock_code}: 读取失败 - {e}")
    
    print(f"\n📊 统计:")
    print(f"  总股票数: {len(factor_files)}")
    print(f"  不完整股票: {len(incomplete_stocks)}")
    print(f"  需要重新计算: {incomplete_stocks[:10]}...")
    
    return factor_info, incomplete_stocks

def recalculate_single_stock(stock_code: str, calculator: FactorCalculator, force_update: bool = False) -> bool:
    """重新计算单只股票的因子"""
    try:
        print(f"🔄 重新计算股票: {stock_code}")
        
        # 获取股票数据文件
        file_path = get_stock_file_path(stock_code)
        if not os.path.exists(file_path):
            print(f"  ❌ 数据文件不存在: {file_path}")
            return False
        
        # 读取数据
        df = pd.read_csv(file_path)
        print(f"  📊 数据形状: {df.shape}")
        
        # 检查必需的列
        required_cols = ['date', 'open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"  ❌ 缺少必需的列: {missing_cols}")
            return False
        
        # 数据预处理
        df['date'] = pd.to_datetime(df['date'])
        df.set_index('date', inplace=True)
        df.sort_index(inplace=True)
        
        # 重命名列以匹配计算器期望的格式
        df = df.rename(columns={
            'open': 'Open',
            'high': 'High', 
            'low': 'Low',
            'close': 'Close',
            'volume': 'Volume'
        })
        
        # 定义所有要计算的因子
        all_factors = [
            # 技术指标
            'RSI', 'MACD', 'KDJ', 'BOLL', 'CCI', 'WR', 'ATR', 'OBV', 'VWAP', 'DMI',
            # 统计指标
            'turnover_std20', 'ReturnStd120',
            # 基本面指标
            'turn', 'peTTM', 'psTTM', 'pcfNcfTTM', 'pbMRQ'
        ]
        
        # 计算所有因子
        factor_results = {}
        success_count = 0
        
        for factor_name in all_factors:
            try:
                result = calculator.calculate_single_indicator(df, factor_name)
                
                if result is not None:
                    if isinstance(result, dict):
                        # 多值指标（如MACD、KDJ等）
                        for key, series in result.items():
                            factor_results[f"{factor_name}_{key}"] = series
                    else:
                        # 单值指标
                        factor_results[factor_name] = result
                    
                    success_count += 1
                    print(f"    ✅ {factor_name}: 计算成功")
                else:
                    print(f"    ⚠️ {factor_name}: 计算结果为空")
                    
            except Exception as e:
                print(f"    ❌ {factor_name}: 计算失败 - {e}")
        
        if not factor_results:
            print(f"  ❌ 没有成功计算任何因子")
            return False
        
        # 合并所有因子到一个DataFrame
        factor_df = pd.DataFrame(factor_results)
        factor_df.index.name = 'date'
        
        print(f"  📊 因子数据形状: {factor_df.shape}")
        print(f"  📊 成功计算因子: {success_count}/{len(all_factors)}")
        print(f"  📊 总因子特征: {len(factor_df.columns)}")
        
        # 保存因子数据
        output_dir = str(DATA_CONFIG['factor_cache_dir'])
        os.makedirs(output_dir, exist_ok=True)
        
        output_file = os.path.join(output_dir, f"{stock_code}_factors.pkl")
        
        with open(output_file, 'wb') as f:
            pickle.dump(factor_df, f)
        
        print(f"  ✅ 因子数据已保存: {output_file}")
        return True
        
    except Exception as e:
        print(f"  ❌ 处理股票 {stock_code} 失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def recalculate_all_factors(target_stocks: list = None, force_update: bool = True):
    """重新计算所有因子"""
    print("🚀 重新计算个股因子")
    
    # 获取目标股票
    if target_stocks is None:
        available_stocks = get_available_stocks()
        target_stocks = available_stocks[:50]  # 限制为前50只股票
    
    print(f"目标股票数: {len(target_stocks)}")
    
    # 创建因子计算器
    try:
        calculator = FactorCalculator()
        print("✅ 因子计算器创建成功")
    except Exception as e:
        print(f"❌ 创建因子计算器失败: {e}")
        return False
    
    # 批量处理
    success_count = 0
    total_count = len(target_stocks)
    
    for i, stock_code in enumerate(target_stocks):
        print(f"\n--- 进度: {i+1}/{total_count} ---")
        
        if recalculate_single_stock(stock_code, calculator, force_update):
            success_count += 1
    
    print(f"\n📋 重新计算结果:")
    print(f"  成功: {success_count}/{total_count}")
    print(f"  成功率: {success_count/total_count*100:.1f}%")
    
    return success_count > 0

def verify_factors():
    """验证因子计算结果"""
    print("\n🔍 验证因子计算结果...")
    
    factor_dir = str(DATA_CONFIG['factor_cache_dir'])
    factor_files = [f for f in os.listdir(factor_dir) if f.endswith('_factors.pkl')]
    
    complete_count = 0
    incomplete_count = 0
    
    for filename in factor_files[:20]:  # 检查前20个文件
        stock_code = filename.replace('_factors.pkl', '')
        file_path = os.path.join(factor_dir, filename)
        
        try:
            with open(file_path, 'rb') as f:
                df = pickle.load(f)
            
            factor_count = len(df.columns)
            
            if factor_count >= 13:
                complete_count += 1
                print(f"  ✅ {stock_code}: {factor_count} 个因子")
            else:
                incomplete_count += 1
                print(f"  ❌ {stock_code}: 只有 {factor_count} 个因子")
                
        except Exception as e:
            print(f"  ❌ {stock_code}: 验证失败 - {e}")
            incomplete_count += 1
    
    print(f"\n📊 验证结果:")
    print(f"  完整因子股票: {complete_count}")
    print(f"  不完整因子股票: {incomplete_count}")
    
    return incomplete_count == 0

def main():
    """主函数"""
    print("=" * 80)
    print("🔄 重新计算个股因子")
    print("=" * 80)
    
    # 1. 检查现有因子
    factor_info, incomplete_stocks = check_existing_factors()
    
    # 2. 重新计算因子
    if incomplete_stocks or len(factor_info) < 30:
        print(f"\n🔄 开始重新计算因子...")
        success = recalculate_all_factors(force_update=True)
        
        if success:
            print(f"✅ 因子重新计算完成")
        else:
            print(f"❌ 因子重新计算失败")
            return
    else:
        print(f"✅ 所有股票因子都完整，无需重新计算")
    
    # 3. 验证结果
    verification_success = verify_factors()
    
    print("\n" + "=" * 80)
    print("📋 重新计算结果总结")
    print("=" * 80)
    
    if verification_success:
        print("🎉 因子重新计算成功！")
        print("💡 现在可以重新生成排序因子:")
        print("   cd ../3-排序因子生成")
        print("   python calculate_factor_percentiles_optimized.py")
    else:
        print("⚠️ 部分股票的因子仍不完整")
        print("   建议检查原始数据或因子计算逻辑")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
