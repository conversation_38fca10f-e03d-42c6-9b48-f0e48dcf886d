#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查系统CUDA兼容性和GPU信息
"""

import subprocess
import sys
import platform

def check_nvidia_gpu():
    """检查NVIDIA GPU"""
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 检测到NVIDIA GPU:")
            print(result.stdout)
            return True
        else:
            print("❌ 未检测到NVIDIA GPU或nvidia-smi不可用")
            return False
    except FileNotFoundError:
        print("❌ nvidia-smi命令不存在，可能未安装NVIDIA驱动")
        return False

def check_cuda_version():
    """检查CUDA版本"""
    try:
        result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 检测到CUDA:")
            print(result.stdout)
            return True
        else:
            print("❌ 未检测到CUDA或nvcc不可用")
            return False
    except FileNotFoundError:
        print("❌ nvcc命令不存在，可能未安装CUDA Toolkit")
        return False

def get_system_info():
    """获取系统信息"""
    print("🖥️ 系统信息:")
    print(f"  操作系统: {platform.system()} {platform.release()}")
    print(f"  Python版本: {sys.version}")
    print(f"  架构: {platform.machine()}")

def main():
    print("=" * 60)
    print("CUDA兼容性检查")
    print("=" * 60)
    
    get_system_info()
    print()
    
    has_gpu = check_nvidia_gpu()
    print()
    
    has_cuda = check_cuda_version()
    print()
    
    if has_gpu and has_cuda:
        print("✅ 系统支持CUDA，可以安装GPU版本的PyTorch")
    elif has_gpu and not has_cuda:
        print("⚠️ 有GPU但缺少CUDA Toolkit，需要安装CUDA")
    else:
        print("❌ 系统不支持CUDA或缺少必要组件")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
