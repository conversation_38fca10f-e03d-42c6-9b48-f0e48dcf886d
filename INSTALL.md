# 安装指南

## 🚀 快速安装

### 1. GPU版本安装（推荐）

如果您有NVIDIA GPU并且已安装CUDA 12.1+：

```bash
# 安装GPU版本的PyTorch和其他依赖
pip install -r requirements.txt

# 或者手动安装PyTorch GPU版本
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
pip install -r requirements-cpu.txt
```

### 2. CPU版本安装

如果您没有GPU或只想使用CPU：

```bash
# 安装CPU版本
pip install -r requirements-cpu.txt
```

## 🔧 环境检查

安装完成后，运行以下脚本检查环境：

```bash
python GPU环境检测.py
```

预期输出（GPU版本）：
```
PyTorch版本: 2.5.1+cu121
CUDA可用: True
CUDA版本: 12.1
GPU型号: NVIDIA GeForce RTX 3060
```

预期输出（CPU版本）：
```
PyTorch版本: 2.5.1+cpu
CUDA可用: False
CUDA版本: None
```

## 📋 系统要求

### GPU版本要求
- **操作系统**: Windows 10/11, Linux, macOS
- **GPU**: NVIDIA GPU（支持CUDA 12.1+）
- **显存**: 建议4GB+（训练大模型需要更多）
- **CUDA**: 12.1或更高版本
- **Python**: 3.8-3.11

### CPU版本要求
- **操作系统**: Windows 10/11, Linux, macOS
- **内存**: 建议8GB+
- **Python**: 3.8-3.11

## 🛠️ 故障排除

### 1. CUDA版本不匹配

如果遇到CUDA版本问题：

```bash
# 检查CUDA版本
nvcc --version

# 卸载当前PyTorch
pip uninstall torch torchvision torchaudio

# 安装对应CUDA版本的PyTorch
# CUDA 11.8
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# CUDA 12.1
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
```

### 2. 内存不足

如果训练时遇到GPU内存不足：

```python
# 在训练脚本中添加
import torch
torch.cuda.empty_cache()

# 或者减少batch_size
python run_training_pipeline.py --batch-size 32
```

### 3. 依赖冲突

如果遇到包版本冲突：

```bash
# 创建新的虚拟环境
python -m venv rl_trading_env
source rl_trading_env/bin/activate  # Linux/macOS
# 或
rl_trading_env\Scripts\activate     # Windows

# 重新安装依赖
pip install -r requirements.txt
```

## 🎯 性能优化建议

### GPU使用优化
1. **使用混合精度训练**：可以减少显存使用并加速训练
2. **调整batch_size**：根据GPU显存调整批处理大小
3. **启用CUDA优化**：确保使用最新的CUDA和cuDNN

### CPU使用优化
1. **多进程训练**：利用多核CPU并行训练
2. **内存管理**：及时释放不需要的变量
3. **数据预处理**：使用高效的数据加载器

## 📚 相关链接

- [PyTorch官方安装指南](https://pytorch.org/get-started/locally/)
- [CUDA安装指南](https://developer.nvidia.com/cuda-downloads)
- [项目文档](README.md)
