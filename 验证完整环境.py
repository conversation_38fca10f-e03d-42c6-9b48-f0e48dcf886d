#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证完整的训练环境
"""

import sys
import importlib

def check_package(package_name, min_version=None):
    """检查包是否安装并验证版本"""
    try:
        module = importlib.import_module(package_name)
        version = getattr(module, '__version__', 'Unknown')
        
        if min_version and hasattr(module, '__version__'):
            from packaging import version as pkg_version
            if pkg_version.parse(version) < pkg_version.parse(min_version):
                print(f"⚠️  {package_name}: {version} (需要 >= {min_version})")
                return False
        
        print(f"✅ {package_name}: {version}")
        return True
    except ImportError:
        print(f"❌ {package_name}: 未安装")
        return False

def check_gpu_environment():
    """检查GPU环境"""
    print("\n🔧 GPU环境检查:")
    try:
        import torch
        print(f"  PyTorch版本: {torch.__version__}")
        print(f"  CUDA可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"  CUDA版本: {torch.version.cuda}")
            print(f"  GPU数量: {torch.cuda.device_count()}")
            print(f"  GPU型号: {torch.cuda.get_device_name(0)}")
            print(f"  GPU显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
            return True
        else:
            print("  ⚠️ GPU不可用，将使用CPU训练")
            return False
    except Exception as e:
        print(f"  ❌ GPU检查失败: {e}")
        return False

def check_training_dependencies():
    """检查训练相关依赖"""
    print("\n📦 训练依赖检查:")
    
    required_packages = {
        'torch': '2.0.0',
        'numpy': '1.21.0',
        'pandas': '1.3.0',
        'matplotlib': '3.5.0',
        'seaborn': '0.11.0',
        'sklearn': '1.0.0',  # scikit-learn的导入名是sklearn
        'gymnasium': '1.0.0',
        'stable_baselines3': '1.6.0',
        'plotly': '5.0.0',
        'tqdm': '4.60.0',
        'tensorboard': '2.8.0'
    }
    
    all_ok = True
    for package, min_ver in required_packages.items():
        if not check_package(package, min_ver):
            all_ok = False
    
    return all_ok

def check_optional_dependencies():
    """检查可选依赖"""
    print("\n📚 可选依赖检查:")
    
    optional_packages = [
        'jupyter',
        'ipywidgets',
        'ta',
        'scipy'
    ]
    
    for package in optional_packages:
        check_package(package)

def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 基本功能测试:")
    
    try:
        # 测试PyTorch
        import torch
        x = torch.randn(2, 3)
        if torch.cuda.is_available():
            x_gpu = x.cuda()
            print("✅ PyTorch GPU操作正常")
        else:
            print("✅ PyTorch CPU操作正常")
        
        # 测试pandas
        import pandas as pd
        df = pd.DataFrame({'A': [1, 2, 3], 'B': [4, 5, 6]})
        print("✅ Pandas操作正常")
        
        # 测试matplotlib
        import matplotlib.pyplot as plt
        plt.figure()
        plt.close()
        print("✅ Matplotlib操作正常")
        
        # 测试stable-baselines3
        from stable_baselines3 import PPO
        print("✅ Stable-Baselines3导入正常")
        
        # 测试gymnasium
        import gymnasium as gym
        print("✅ Gymnasium导入正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 强化学习训练环境验证")
    print("=" * 60)
    
    print(f"Python版本: {sys.version}")
    
    # 检查GPU环境
    gpu_ok = check_gpu_environment()
    
    # 检查训练依赖
    deps_ok = check_training_dependencies()
    
    # 检查可选依赖
    check_optional_dependencies()
    
    # 测试基本功能
    func_ok = test_basic_functionality()
    
    print("\n" + "=" * 60)
    print("📋 环境验证总结")
    print("=" * 60)
    
    if gpu_ok:
        print("✅ GPU环境: 可用 (推荐用于训练)")
    else:
        print("⚠️  GPU环境: 不可用 (将使用CPU)")
    
    if deps_ok:
        print("✅ 训练依赖: 完整")
    else:
        print("❌ 训练依赖: 不完整")
    
    if func_ok:
        print("✅ 基本功能: 正常")
    else:
        print("❌ 基本功能: 异常")
    
    if deps_ok and func_ok:
        print("\n🎉 环境验证通过！可以开始训练")
        print("\n📝 下一步:")
        print("  1. 确保数据准备完成: python precompute_factors.py info")
        print("  2. 运行训练测试: cd '2-训练与回测' && python quick_test.py")
        print("  3. 开始正式训练: python run_training_pipeline.py --num-stocks 10")
    else:
        print("\n⚠️  环境验证未完全通过，请检查上述问题")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
