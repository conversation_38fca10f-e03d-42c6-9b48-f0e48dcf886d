#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pickle
import pandas as pd
import os

# 检查新生成的pkl文件
pkl_file = r"d:\开发项目\Stock Models\RLStrategy_V2.1（SAC算法）\0-数据汇总\2-因子数据\factors\sh.600000_factors.pkl"

if os.path.exists(pkl_file):
    with open(pkl_file, 'rb') as f:
        data = pickle.load(f)
    
    print(f"文件: {pkl_file}")
    print(f"数据类型: {type(data)}")
    print(f"形状: {data.shape}")
    print(f"列数: {len(data.columns)}")
    print(f"列名: {list(data.columns)}")
    print(f"索引类型: {type(data.index)}")
    print(f"数据范围: {data.index[0]} ~ {data.index[-1]}")
    print(f"\n前5行:")
    print(data.head())
    print(f"\n数据信息:")
    print(data.info())
else:
    print(f"文件不存在: {pkl_file}")