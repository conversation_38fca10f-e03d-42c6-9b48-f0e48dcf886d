# 预计算因子系统使用指南

## 概述

预计算因子系统是为股票强化学习项目设计的高性能因子计算和缓存系统。通过预先计算并缓存技术指标，可以将训练环境的初始化时间从10-30秒缩短到1-3秒，大幅提升开发和训练效率。

## 快速开始

### 1. 首次设置

```bash
# 预计算所有股票的因子
python precompute_factors.py precompute

# 查看预计算结果
python precompute_factors.py info

# 运行演示
python demo_precompute.py
```

### 2. 在训练中使用

```python
from stock_env_precomputed import StockTradingEnvPrecomputed

# 创建使用预计算因子的环境
env = StockTradingEnvPrecomputed(
    stock_list=['SZ.000001', 'SZ.000002', 'SZ.000858'],
    initial_balance=100000,
    use_precomputed=True
)

# 正常使用环境
state = env.reset()
action = env.action_space.sample()
next_state, reward, done, info = env.step(action)
```

## 详细使用说明

### 预计算管理命令

#### 预计算所有因子
```bash
# 基本用法
python precompute_factors.py precompute

# 指定股票
python precompute_factors.py precompute --stocks SZ.000001,SZ.000002,SZ.000858

# 强制更新
python precompute_factors.py precompute --force

# 使用更多线程
python precompute_factors.py precompute --max-workers 8
```

#### 增量更新
```bash
# 更新所有股票
python precompute_factors.py update

# 更新指定股票
python precompute_factors.py update --stocks SZ.000001,SZ.000002
```

#### 查看缓存信息
```bash
python precompute_factors.py info
```

输出示例：
```
=== 缓存信息 ===
缓存目录: factor_cache
股票总数: 100
最后更新: 2024-01-15T10:30:00
计算指标: ['RSI', 'MACD', 'KDJ', 'BOLL', 'CCI', 'WR', 'ATR', 'OBV', 'VWAP', 'DMI']

前10只股票的缓存信息:
   1. 000001: 25因子, 1250记录, 2.3MB
   2. 000002: 25因子, 1180记录, 2.1MB
   ...

缓存总大小: 230.50 MB
```

#### 缓存管理
```bash
# 清理30天前的缓存
python precompute_factors.py cleanup --keep-days 30

# 性能测试
python precompute_factors.py benchmark

# 测试加载
python precompute_factors.py test
```

### 编程接口使用

#### 基本使用
```python
from indicators import PrecomputeManager

# 创建管理器
manager = PrecomputeManager(
    cache_dir="factor_cache",
    data_dir="your_data_directory",
    max_workers=4
)

# 预计算单个股票
success = manager.precompute_stock_factors(
    stock_code="000001",
    indicators=['RSI', 'MACD', 'KDJ'],
    force_update=False
)

# 批量预计算
results = manager.precompute_batch_factors(
    stock_list=['000001', '000002', '000858'],
    indicators=['RSI', 'MACD', 'KDJ', 'BOLL'],
    force_update=False
)

# 加载预计算因子
factors_df = manager.load_precomputed_factors(
    stock_code="000001",
    start_date="2023-01-01",
    end_date="2023-12-31"
)

# 批量加载
factors_dict = manager.load_batch_factors(
    stock_list=['000001', '000002'],
    start_date="2023-01-01"
)
```

#### 增量更新
```python
# 增量更新单个股票
new_data = load_new_stock_data("000001")
success = manager.incremental_update(
    stock_code="000001",
    new_data=new_data,
    indicators=['RSI', 'MACD', 'KDJ']
)

# 批量增量更新
results = manager.batch_incremental_update(
    stock_list=['000001', '000002', '000858'],
    indicators=['RSI', 'MACD', 'KDJ']
)
```

#### 创建特征矩阵
```python
# 从预计算因子创建特征矩阵
feature_matrix = manager.create_feature_matrix(
    stock_list=['000001', '000002', '000858'],
    start_date="2023-01-01",
    end_date="2023-12-31",
    normalize=True
)

print(f"特征矩阵形状: {feature_matrix.shape}")
# 输出: 特征矩阵形状: (250, 75)  # 250天 x 75个特征
```

## 性能优化建议

### 1. 合理设置并行线程数
```bash
# 根据CPU核心数设置，通常为核心数的1-2倍
python precompute_factors.py precompute --max-workers 8
```

### 2. 定期清理缓存
```bash
# 每周清理一次过期缓存
python precompute_factors.py cleanup --keep-days 7
```

### 3. 分批处理大量股票
```python
# 对于大量股票，分批处理避免内存不足
stock_list = get_all_stocks()  # 假设有1000只股票
batch_size = 50

for i in range(0, len(stock_list), batch_size):
    batch = stock_list[i:i+batch_size]
    manager.precompute_batch_factors(batch)
```

### 4. 使用SSD存储缓存
```python
# 将缓存目录设置在SSD上
manager = PrecomputeManager(
    cache_dir="/ssd/factor_cache",  # SSD路径
    max_workers=4
)
```

## 故障排除

### 常见问题

#### 1. 预计算失败
```
错误: 计算股票 000001 因子失败: 数据不足
```
**解决方案**: 检查原始数据文件是否存在且格式正确

#### 2. 内存不足
```
错误: MemoryError
```
**解决方案**: 
- 减少并行线程数: `--max-workers 2`
- 分批处理股票
- 增加系统内存

#### 3. 缓存损坏
```
错误: 加载股票 000001 预计算因子失败
```
**解决方案**: 强制重新计算
```bash
python precompute_factors.py precompute --stocks 000001 --force
```

#### 4. 磁盘空间不足
```
错误: No space left on device
```
**解决方案**: 清理缓存或更换存储位置
```bash
python precompute_factors.py cleanup --keep-days 7
```

### 调试技巧

#### 1. 启用详细日志
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

#### 2. 检查缓存状态
```bash
python precompute_factors.py info
```

#### 3. 测试单个股票
```bash
python precompute_factors.py precompute --stocks 000001 --force
```

#### 4. 性能分析
```bash
python precompute_factors.py benchmark
```

## 最佳实践

### 1. 开发流程
1. **首次设置**: 运行完整预计算
2. **日常开发**: 使用预计算环境
3. **定期更新**: 每日增量更新
4. **定期清理**: 每周清理缓存

### 2. 生产环境
1. **自动化**: 设置定时任务自动更新
2. **监控**: 监控缓存大小和更新状态
3. **备份**: 定期备份重要缓存
4. **容错**: 设置回退机制

### 3. 性能调优
1. **硬件**: 使用SSD存储，足够内存
2. **并行**: 合理设置线程数
3. **批处理**: 大量数据分批处理
4. **缓存**: 定期清理过期数据

## 高级功能

### 1. 自定义指标
```python
from indicators.base import BaseIndicator

class CustomIndicator(BaseIndicator):
    def __init__(self):
        super().__init__("CUSTOM")
    
    def calculate(self, data, **kwargs):
        # 自定义计算逻辑
        return custom_result

# 添加到预计算系统
manager.calculator.add_custom_indicator('CUSTOM', CustomIndicator)
```

### 2. 分布式计算
```python
# 多机器分布式预计算（概念示例）
from multiprocessing import Pool

def compute_stock_batch(stock_batch):
    manager = PrecomputeManager()
    return manager.precompute_batch_factors(stock_batch)

# 分配到多个进程
with Pool(processes=4) as pool:
    results = pool.map(compute_stock_batch, stock_batches)
```

### 3. 实时更新
```python
# 实时监控数据变化并更新
import time
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class DataUpdateHandler(FileSystemEventHandler):
    def on_modified(self, event):
        if event.src_path.endswith('_hfq.txt'):
            stock_code = extract_stock_code(event.src_path)
            manager.incremental_update(stock_code, load_new_data(stock_code))

# 监控数据目录
observer = Observer()
observer.schedule(DataUpdateHandler(), data_directory, recursive=False)
observer.start()
```

---

通过使用预计算因子系统，你可以显著提升强化学习训练的效率，将更多时间专注于模型优化而不是等待数据加载。
