#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票量化交易项目主控制脚本

项目结构：
1. 数据准备：最新数据下载 -> 个股因子生成 -> 排序因子生成
2. 训练与回测：数据提取 -> 模型训练 -> 模型回测 -> 逐段回测
3. 实盘交易提醒：根据模型提醒买入卖出交易

使用方法：
    python run_project.py --mode all                    # 运行完整流程
    python run_project.py --mode data                   # 只运行数据准备
    python run_project.py --mode train                  # 只运行训练与回测
    python run_project.py --mode trade                  # 只运行实盘交易提醒
    python run_project.py --mode rolling                # 只运行滚动回测
"""

import os
import sys
import argparse
import subprocess
from datetime import datetime
import logging

# 导入数据管理器
sys.path.append(os.path.join(os.path.dirname(__file__), '0-数据汇总'))
try:
    from data_manager import DataManager
except ImportError:
    DataManager = None
    logging.warning("数据管理器导入失败，数据汇总功能将不可用")

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('project_run.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProjectController:
    """
    项目主控制器
    """
    
    def __init__(self, project_root=None):
        """
        初始化项目控制器
        
        Args:
            project_root: 项目根目录，默认为当前脚本所在目录
        """
        if project_root is None:
            self.project_root = os.path.dirname(os.path.abspath(__file__))
        else:
            self.project_root = project_root
        
        # 定义各模块路径
        self.data_prep_dir = os.path.join(self.project_root, '1-数据准备')
        self.train_backtest_dir = os.path.join(self.project_root, '2-训练与回测')
        self.trade_alert_dir = os.path.join(self.project_root, '3-实盘交易提醒')
        
        logger.info(f"项目根目录: {self.project_root}")
    
    def run_script(self, script_path, description, cwd=None):
        """
        运行Python脚本
        
        Args:
            script_path: 脚本路径
            description: 脚本描述
            cwd: 工作目录
        
        Returns:
            bool: 是否成功
        """
        if not os.path.exists(script_path):
            logger.error(f"脚本不存在: {script_path}")
            return False
        
        logger.info(f"开始执行: {description}")
        logger.info(f"脚本路径: {script_path}")
        
        try:
            if cwd is None:
                cwd = os.path.dirname(script_path)
            
            result = subprocess.run(
                [sys.executable, script_path],
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=3600  # 1小时超时
            )
            
            if result.returncode == 0:
                logger.info(f"✓ {description} 执行成功")
                if result.stdout:
                    logger.info(f"输出: {result.stdout[:500]}...")  # 只显示前500字符
                return True
            else:
                logger.error(f"✗ {description} 执行失败")
                logger.error(f"错误代码: {result.returncode}")
                if result.stderr:
                    logger.error(f"错误信息: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"✗ {description} 执行超时")
            return False
        except Exception as e:
            logger.error(f"✗ {description} 执行异常: {e}")
            return False
    
    def run_data_preparation(self):
        """
        运行数据准备模块
        
        Returns:
            bool: 是否成功
        """
        logger.info("\n" + "="*60)
        logger.info("开始数据准备阶段")
        logger.info("="*60)
        
        success = True
        
        # 1. 最新数据下载
        download_script = os.path.join(
            self.data_prep_dir, 
            '1-最新数据下载', 
            'download_latest_data.py'
        )
        if not self.run_script(download_script, "最新数据下载"):
            success = False
        
        # 2. 个股因子生成
        factor_script = os.path.join(
            self.data_prep_dir,
            '2-个股因子生成',
            'calculate_factors_fast.py'
        )
        if not self.run_script(factor_script, "个股因子生成"):
            success = False
        
        # 3. 排序因子生成
        percentile_script = os.path.join(
            self.data_prep_dir,
            '3-排序因子生成',
            'calculate_factor_percentiles_fast.py'
        )
        if not self.run_script(percentile_script, "排序因子生成"):
            success = False
        
        if success:
            logger.info("✓ 数据准备阶段完成")
        else:
            logger.error("✗ 数据准备阶段存在错误")
        
        return success
    
    def run_train_backtest(self):
        """
        运行训练与回测模块
        
        Returns:
            bool: 是否成功
        """
        logger.info("\n" + "="*60)
        logger.info("开始训练与回测阶段")
        logger.info("="*60)
        
        success = True
        
        # 1. 数据提取（如果有专门的数据提取脚本）
        data_extract_script = os.path.join(
            self.train_backtest_dir,
            '1-数据提取',
            'extract_training_data.py'
        )
        if os.path.exists(data_extract_script):
            if not self.run_script(data_extract_script, "训练数据提取"):
                success = False
        
        # 2. 模型训练（使用优化后的训练流水线）
        train_script = os.path.join(
            self.train_backtest_dir,
            'run_training_pipeline.py'
        )
        if not self.run_script(train_script, "模型训练"):
            success = False
        
        # 3. 模型回测（训练流水线已包含回测）
        backtest_script = os.path.join(
            self.train_backtest_dir,
            '3-回测',
            'ranking_factor_backtest.py'
        )
        if os.path.exists(backtest_script):
            if not self.run_script(backtest_script, "模型回测"):
                logger.warning("独立回测失败，但训练流水线已包含回测")
        
        if success:
            logger.info("✓ 训练与回测阶段完成")
        else:
            logger.error("✗ 训练与回测阶段存在错误")
        
        return success
    
    def run_rolling_backtest(self):
        """
        运行滚动回测
        
        Returns:
            bool: 是否成功
        """
        logger.info("\n" + "="*60)
        logger.info("开始滚动回测阶段")
        logger.info("="*60)
        
        rolling_script = os.path.join(
            self.train_backtest_dir,
            '4-逐段回测',
            'rolling_backtest.py'
        )
        
        success = self.run_script(rolling_script, "滚动回测")
        
        if success:
            logger.info("✓ 滚动回测阶段完成")
        else:
            logger.error("✗ 滚动回测阶段存在错误")
        
        return success
    
    def run_trade_alert(self):
        """
        运行实盘交易提醒模块
        
        Returns:
            bool: 是否成功
        """
        logger.info("\n" + "="*60)
        logger.info("开始实盘交易提醒阶段")
        logger.info("="*60)
        
        trade_script = os.path.join(
            self.trade_alert_dir,
            'main.py'
        )
        
        success = self.run_script(trade_script, "实盘交易提醒")
        
        if success:
            logger.info("✓ 实盘交易提醒阶段完成")
        else:
            logger.error("✗ 实盘交易提醒阶段存在错误")
        
        return success
    
    def run_data_collection(self):
        """
        运行数据汇总收集
        
        Returns:
            bool: 是否成功
        """
        logger.info("\n" + "="*60)
        logger.info("开始数据汇总收集")
        logger.info("="*60)
        
        if DataManager is None:
            logger.error("数据管理器不可用，跳过数据汇总")
            return False
        
        try:
            manager = DataManager(self.project_root)
            collected_data = manager.collect_all_data()
            
            total_files = sum(len(files) for files in collected_data.values())
            logger.info(f"✓ 数据汇总完成，共收集 {total_files} 个文件")
            return True
            
        except Exception as e:
            logger.error(f"✗ 数据汇总失败: {e}")
            return False
    
    def run_all(self):
        """
        运行完整项目流程

        Returns:
            bool: 是否成功
        """
        logger.info("\n" + "="*80)
        logger.info("开始运行完整项目流程")
        logger.info("="*80)
        
        start_time = datetime.now()
        
        # 1. 数据准备
        if not self.run_data_preparation():
            logger.error("数据准备失败，停止执行")
            return False
        
        # 2. 训练与回测
        if not self.run_train_backtest():
            logger.error("训练与回测失败，停止执行")
            return False
        
        # 3. 滚动回测
        if not self.run_rolling_backtest():
            logger.warning("滚动回测失败，但继续执行")
        
        # 4. 实盘交易提醒
        if not self.run_trade_alert():
            logger.warning("实盘交易提醒失败，但项目主要流程已完成")
        
        # 5. 数据汇总收集
        if not self.run_data_collection():
            logger.warning("数据汇总失败，但项目主要流程已完成")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info("\n" + "="*80)
        logger.info("项目完整流程执行完成")
        logger.info(f"总耗时: {duration}")
        logger.info("="*80)
        
        return True
    
    def check_environment(self):
        """
        检查项目环境
        
        Returns:
            bool: 环境是否正常
        """
        logger.info("检查项目环境...")
        
        # 检查目录结构
        required_dirs = [
            self.data_prep_dir,
            self.train_backtest_dir,
            self.trade_alert_dir
        ]
        
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                logger.error(f"缺少必要目录: {dir_path}")
                return False
        
        # 检查Python环境
        try:
            import pandas
            import numpy
            logger.info("✓ Python环境检查通过")
        except ImportError as e:
            logger.error(f"✗ Python环境检查失败: {e}")
            return False
        
        logger.info("✓ 项目环境检查通过")
        return True


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='股票量化交易项目主控制脚本')
    parser.add_argument(
        '--mode', 
        choices=['all', 'data', 'train', 'rolling', 'trade', 'collect', 'check'],
        default='all',
        help='运行模式'
    )
    parser.add_argument(
        '--project-root',
        help='项目根目录路径'
    )
    
    args = parser.parse_args()
    
    # 创建项目控制器
    controller = ProjectController(args.project_root)
    
    # 检查环境
    if not controller.check_environment():
        logger.error("环境检查失败，退出")
        return 1
    
    # 根据模式执行相应功能
    success = True
    
    if args.mode == 'all':
        success = controller.run_all()
    elif args.mode == 'data':
        success = controller.run_data_preparation()
    elif args.mode == 'train':
        success = controller.run_train_backtest()
    elif args.mode == 'rolling':
        success = controller.run_rolling_backtest()
    elif args.mode == 'trade':
        success = controller.run_trade_alert()
    elif args.mode == 'collect':
        success = controller.run_data_collection()
    elif args.mode == 'check':
        success = controller.check_environment()
    
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)